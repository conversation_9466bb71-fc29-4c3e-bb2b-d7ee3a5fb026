#!/bin/bash

# 调用当前目录的预构建脚本
./prevbuild.sh $1

# 切换到上一级目录并删除之前的编译结果
cd ..
rm -rf buildroot/output/rockchip_rk3576/build/ym-appd-1.0
rm -rf buildroot/output/rockchip_rk3576/build/ym-audio-mixer-1.0

# 调用主构建脚本
./build.sh

# 生成年月日时格式的时间戳（如2024102315）
timestamp=$(date +%Y%m%d%H)
target_dir="./output/firmware/${timestamp}"

# 创建目标文件夹（自动创建上级目录）
mkdir -p "$target_dir"

# 复制git信息文件
cp ./audio_mixer_rk3576/scripts/git_info_summary.txt "$target_dir/"

cp ./audio_mixer_rk3576/ymupdate.tar.gz "$target_dir/"

# 复制img文件
cp ./output/update/Image/update.img "$target_dir/"

