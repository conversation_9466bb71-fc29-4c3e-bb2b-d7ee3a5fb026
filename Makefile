# ------------------------------------------------
# Generic Makefile (based on gcc)
# ------------------------------------------------

######################################
# target
######################################
TARGET ?= 
PROJECT_DIR := $(shell pwd)

######################################
# building variables
######################################
# debug build?
DEBUG ?= 0

#######################################
# paths
#######################################
# Build path
BUILD_DIR = build
RELEASE_DIR = release

######################################
# source
######################################
SRC_DIR := component/
SRC_DIR += src/

SRCS := $(shell find $(SRC_DIR) platform/$(TARGET) -name '*.c')
OBJS := $(addprefix $(BUILD_DIR)/,$(notdir $(SRCS:%.c=%.o)))
vpath %.c $(sort $(dir $(SRCS)))

#######################################
# binaries
#######################################
CROSS_COMPILE ?= $(PROJECT_DIR)/../buildroot/output/rockchip_rk3576/host/bin/aarch64-linux-
# The gcc compiler bin path can be either defined in make command via GCC_PATH variable (> make GCC_PATH=xxx)
# either it can be added to the PATH environment variable.
CC = $(CROSS_COMPILE)gcc
SZ = $(CROSS_COMPILE)size
SR = $(CROSS_COMPILE)strip
AR = $(CROSS_COMPILE)ar

#######################################
# CFLAGS
#######################################
# macros for gcc
# C defines
C_DEFS = 

# C includes
C_INCLUDES := -I$(PROJECT_DIR)/include
C_INCLUDES += -Iplatform/$(TARGET)

# compile gcc flags
CFLAGS += $(C_DEFS) $(C_INCLUDES) -Wall -fdata-sections -ffunction-sections -fstack-protector-all

ifeq ($(DEBUG), 1)
CFLAGS += -O0 -g -gdwarf-2
else
CFLAGS += -O2 -D_FORTIFY_SOURCE=2
endif

# Generate dependency information
CFLAGS += -MMD -MP -MF"$(@:%.o=%.d)"

#######################################
# LDFLAGS
#######################################
# libraries
LIBS = -lc -lasound -lm -ludev -lpthread -lresample -lgpiod -lssl -lcrypto -lsamplerate -lzlog -lltdl -latsha204a
LIBDIR = -L../buildroot/output/rockchip_rk3576/target/lib64 -L./lib_dsp
LDFLAGS = $(LIBDIR) $(LIBS) -Wl,--gc-sections

# -include $(PROJECT_DIR)/platform/$(TARGET)/sub.mk

# C_INCLUDES += -Iplatform/$(TARGET)

all: $(BUILD_DIR) $(RELEASE_DIR) Makefile generate_version $(TARGET)
ifeq ($(DEBUG), 0)
	@echo "$(TARGET)  :\tstrip\t" && $(SR) $(BUILD_DIR)/$(TARGET)
endif
	@(bash scripts/post_build.sh $(TARGET) $(BUILD_DIR)/$(TARGET))
	@cp $(BUILD_DIR)/$(TARGET) $(RELEASE_DIR)/
	@echo "$(TARGET) For RK3576 Build Successfully!"

$(TARGET): $(OBJS)
	@echo "$(TARGET):\tCC\t$@" && $(CC) $^ $(LDFLAGS) -Wl,-rpath,/app -o $(BUILD_DIR)/$@

$(BUILD_DIR)/%.o: %.c
	@echo "$(TARGET):\tCC\t$@" && $(CC) -c $(CFLAGS) -Wa,-a,-ad,-alms=$(BUILD_DIR)/$(notdir $(<:.c=.lst)) $< -o $@

# tools: $(BUILD_DIR) $(RELEASE_DIR) net_discovery sing upgrade cmp_version
# 	@cp $(BUILD_DIR)/net_discovery $(BUILD_DIR)/upgrade $(BUILD_DIR)/sing $(BUILD_DIR)/cmp_version $(RELEASE_DIR)/
# 	@echo "tools build Successfully"

# net_discovery: tools/net_discovery.c component/common.c
# 	@echo "$@:\tCC\t$^" && $(CC) -Iinclude $^ -lm -lpthread -o $(BUILD_DIR)/$@

# sing: tools/generate_sine.c
# 	@echo "$@:\tCC\t$^" && $(CC) -Iinclude $^ -lm -lpthread -o $(BUILD_DIR)/$@

# upgrade: tools/upgrade.c
# 	@echo "$@:\tCC\t$^" && $(CC) -Iinclude $^ -lm -lpthread -o $(BUILD_DIR)/$@

# cmp_version: tools/cmp_version.c
# 	@echo "$@:\tCC\t$^" && $(CC) -Iinclude $^ -o $(BUILD_DIR)/$@

$(BUILD_DIR):
	@mkdir $@

$(RELEASE_DIR):
	@mkdir $@

.PHONY: generate_version
generate_version:
	@(bash scripts/generate_version.sh $(TARGET))

#######################################
# clean up
#######################################
clean:
	-rm -fR $(BUILD_DIR)

#######################################
# dependencies
#######################################
-include $(wildcard $(BUILD_DIR)/*.d)

# *** EOF ***
