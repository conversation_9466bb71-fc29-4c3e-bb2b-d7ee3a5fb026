/******************************************************************************
 * This program is protected under international and China Copyright laws as
 * an unpublished work. This program is confidential and proprietary to the
 * copyright owners. Reproduction or disclosure, in whole or in part, or the
 * production of derivative works therefrom without the express permission of
 * the copyright owners is prohibited.
 *
 *                Copyright (C) 2011      by YinMan Tech Co. Ltd,
 *                Copyright (C) 2011-2013 by YinMan Tech Co. Ltd.
 *                            All rights reserved.  
 * HeadRelatedTransferFunction(HRTF)
 ******************************************************************************/

#ifndef _YM_MIXER_H_
#define _YM_MIXER_H_

//#define SPEAKER_CONF_PLATFORM // to set the speaker conference platform, this will limit the modules and the input/output channels
#ifdef SPEAKER_CONF_PLATFORM
#ifndef LIMIT_AEC_PATH_TO_ONE
#define LIMIT_AEC_PATH_TO_ONE // to set the AEC channels of the system
#endif // #ifndef LIMIT_AEC_PATH_TO_ONE
#else // #ifdef SPEAKER_CONF_PLATFORM
#define YM_MIXER_INPUT_BUF_NON_INTERLEAVE           // the input buffer should be non-interleave when call ym_matrix_process
#endif // #ifdef SPEAKER_CONF_PLATFORM
#ifndef ENABLE_TWO_AEC_PATH
#define ENABLE_TWO_AEC_PATH  // to enable the AEC2 path with different denoise modes
#endif // #ifndef ENABLE_TWO_AEC_PATH
//#define AEC_INBLOCK_11_MS   // to set the AEC input block 512@48kHz
//#define AEC_INBLOCK_4_MS   // to set the AEC input block 4ms@16kHz
//#define AEC_INBLOCK_8_MS   // to set the input block size as 10ms to integrate the lib from kuaishou
//#define AEC_AFC_48K      // to enable the AEC/AFC 48k mode, this will change the default input block size as well
#if defined(AEC_INBLOCK_11_MS) && defined(YM_BACKEND_RK3308_FLOAT)
#define SET_FUNCTION_VISIBLE    __attribute__((visibility("default")))
#define FUNC(name) ym10ms_##name
#else // #if defined(AEC_INBLOCK_11_MS) && defined(YM_BACKEND_RK3308_FLOAT)
#define SET_FUNCTION_VISIBLE
#define FUNC(name) name
#endif // #if defined(AEC_INBLOCK_11_MS) && defined(YM_BACKEND_RK3308_FLOAT)
#ifndef AEC_AFC_24K
#define AEC_AFC_24K      // to enable the AEC/AFC 24k mode, this will change the default input block size as well
#endif // #ifndef AEC_AFC_24K
#ifdef YM_MIXER_INPUT_BUF_NON_INTERLEAVE
#define REUSE_INBUF_AS_INTERNAL       // to reuse the input buffer ptr as the one used internally, i.e. the conversion in-place
#endif // YM_MIXER_INPUT_BUF_NON_INTERLEAVE
//#define MIXER_SHORT_INOUT       // the mixer input and output of data type short
#ifdef MIXER_SHORT_INOUT
#ifdef YM_MIXER_INPUT_BUF_NON_INTERLEAVE
#undef YM_MIXER_INPUT_BUF_NON_INTERLEAVE    // we can support interleave input for short type input
#endif // #ifdef YM_MIXER_INPUT_BUF_NON_INTERLEAVE
#ifdef REUSE_INBUF_AS_INTERNAL
#undef REUSE_INBUF_AS_INTERNAL
#endif // #ifdef REUSE_INBUF_AS_INTERNAL
#endif // #ifdef MIXER_SHORT_INOUT
#ifndef SPEAKER_CONF_PLATFORM
#define YM_MIXER_UPDATE_LINEAR_FILTER_FIRST         // to update the linear filter coef first before the error output generation, can be used to fill the time interval for DMA transfer
#endif // #ifndef SPEAKER_CONF_PLATFORM
//#define DISABLE_AEC_NLP_MODULE  // to disable the AEC/AFC and NLP part
//#define ENABLE_MIC_ARRAY_MODULE     // To enable the mic array modules
/* Define the reverb levels */
#define YM_MATRIX_MAX_INPUT_VOLUME_LEVELS           (220)   // from [-90, 12] dB for input channels
#define YM_MATRIX_MAX_MATRIX_VOLUME_LEVELS          (220)   // from [-90, 12] dB for matrix mapping channels
#define YM_MATRIX_MAX_OUTPUT_VOLUME_LEVELS          (220)   // from [-90, 12] dB for output channels
#define YM_MIXER_INOUT_VOLUME_DEFAULT               (100)
/* Define the GEQ maximum number bands */
#define YM_MIXER_MIN_GEQ_BANDS               (3)
#define YM_MIXER_MAX_GEQ_BANDS               (32)
#define YM_MIXER_DEFAULT_GEQ_BANDS           (15)
#define YM_MIXER_DEFAULT_GEQ_BANDS_MIC       (30)
#define YM_MIXER_DEFAULT_OUTPUT_GEQ_BANDS    (7)

//#define SIMULATE_FEEDBACK_RPE_16K
/* Define the default sampling rate */
#if defined(SIMULATE_FEEDBACK_RPE_16K) || defined(MIXER_INPUT_FS_16K)
#define YM_MIXER_SAMPLING_RATE          (16000)
#else
#define YM_MIXER_SAMPLING_RATE          (48000)
#endif // #if defined(SIMULATE_FEEDBACK_RPE_16K) || defined(MIXER_INPUT_FS_16K)

/* Define the EQ maximum and minimum center frequency in Hz */
#define YM_MIXER_MIN_EQ_CENTER_FREQ  (20)
#define YM_MIXER_MAX_EQ_CENTER_FREQ  (22000)

/* Define the GEQ/PEQ maximum and minimum gain in 0.1dB */
#define YM_MIXER_MIN_EQ_GAIN         (-240)
#define YM_MIXER_MAX_EQ_GAIN         (+240)

/* Define the value range for threshold/peak/target value in dB */
#define YM_MATRIX_GAIN_THRESHOLD_TARGET_MAX_DB      (0)
#define YM_MATRIX_GAIN_THRESHOLD_TARGET_MIN_DB     (-120)
#define YM_MATRIX_GAIN_COMPR_BOOST_MAX_DB          (+24)
#define YM_MATRIX_GAIN_COMPR_BOOST_MIN_DB          (-24)
#define YM_MATRIX_GAIN_COMPR_BOOST_DEF_DB          (0)
#define YM_MATRIX_GAIN_ATTACK_RELEASE_MAX_MS       (10000)
#define YM_MATRIX_GAIN_ATTACK_RELEASE_MIN_MS       (1)
#define YM_MATRIX_GAIN_DEF_ATTACK_TIME_MS          20
#define YM_MATRIX_GAIN_DEF_RELEASE_TIME_MS         40

/* Default noise Gate level */
#define YM_MIXER_AUX_NG_DEFAULT_LEVEL_DB        (YM_MATRIX_GAIN_THRESHOLD_TARGET_MIN_DB)
#define YM_MIXER_MIC_NG_DEFAULT_LEVEL_DB        (-45)
#define YM_MIXER_MIC_NG_DEF_COMP_GAIN_DB        (-40)
#define YM_MIXER_MIC_NG_MAX_COMP_GAIN_DB        YM_MATRIX_GAIN_THRESHOLD_TARGET_MAX_DB
#define YM_MIXER_MIC_NG_MIN_COMP_GAIN_DB        YM_MATRIX_GAIN_THRESHOLD_TARGET_MIN_DB

/* Define the DRC threshold dB levels */
#define YM_MIXER_DRC_DEF_THRESHOLD_DB       (-20)
#define YM_MIXER_EXPANDER_DEF_THRESHOLD_DB  (-48)
#define YM_MIXER_EXPANDER_DEF_BOOST_DB      (6)
#define YM_MIXER_EXPANDER_MAX_BOOST_DB      (24)
#define YM_MIXER_EXPANDER_MIN_BOOST_DB      (0)
/* Define the DRC compressor ratio levels */
#define YM_MIXER_DRC_EXPAND_MAX_COMPR_RATIO (50)
#define YM_MIXER_DRC_EXPAND_MIN_COMPR_RATIO (1)
#define YM_MIXER_DRC_EXPAND_DEF_COMPR_RATIO (10)

/* AGC params */
#define YM_AGC_COMPR_GAIN_DEF_DB        (6)
#define YM_AGC_TARGET_LEVEL_DEF_DB     (-12)
#define YM_AGC_DEF_ATTACK_TIME_MS       12
#define YM_AGC_DEF_RELEASE_TIME_MS      12
#define YM_AGC_NOISE_LEVEL_DEF_DB      YM_MATRIX_GAIN_THRESHOLD_TARGET_MIN_DB
/* Limiter params */
#define YM_LIMITER_DEF_THRESHOLD_DB           (0)
#define YM_LIMITER_DEF_ATTACK_TIME_MS          55
#define YM_LIMITER_DEF_RELEASE_TIME_MS         220

/* Define the ducker params */
#define YM_DUCK_COMPR_GAIN_MAX_DB           YM_MATRIX_GAIN_THRESHOLD_TARGET_MAX_DB
#define YM_DUCK_COMPR_GAIN_MIN_DB           YM_MATRIX_GAIN_THRESHOLD_TARGET_MIN_DB
#define YM_DUCK_COMPR_GAIN_DEF_DB           (-12)
#define YM_DUCK_THRESHOLD_DEF_DB            (-20)
// Define the ducker attack/release/keep time in milli-second
#define YM_DUCK_DEF_ATTACK_TIME_MS          100
#define YM_DUCK_MAX_KEEP_TIME_MS            10000
#define YM_DUCK_DEF_KEEP_TIME_MS            1000
#define YM_DUCK_DEF_RELEASE_TIME_MS         100

/* Gain control types */
typedef enum YM_MATRIX_GAIN_CONTROL_MODULE_s
{
    YM_MATRIX_MIC_NOISE_GATE, /* Mic Noise Gate */
    YM_MATRIX_AUX_NOISE_GATE, /* Aux Noise Gate, reserved */
    YM_MATRIX_MIC_DRC, /* Mic DRC */
    YM_MATRIX_AUX_DRC, /* Aux DRC, reserved */
    YM_MATRIX_MIC_EXPANDER, /* Mic expander */
    YM_MATRIX_AUX_EXPANDER, /* Aux expander, reserved */
    YM_MATRIX_MIC_AGC, /* Mic AGC */
    YM_MATRIX_AUX_AGC, /* Aux AGC */
    YM_MATRIX_MIC_LIMITER, /* Mic Limiter */
    YM_MATRIX_AUX_LIMITER, /* Aux Limiter */
    YM_MATRIX_SP_LIMITER, /* Speaker Limiter */
    YM_MATRIX_HP_LIMITER, /* headphone Limiter */
    YM_MATRIX_REC_LIMITER, /* Record Limiter */
    YM_MATRIX_AUTO_DUCKER, /* Record Limiter */
    YM_MATRIX_MIC_AEC1_AUTO_MIXER, /* mic auto mixer before AEC1 module */
    YM_MATRIX_MIC_AEC0_AUTO_MIXER, /* mic auto mixer before AEC0 module */
    YM_MATRIX_MAX_GAIN_CONTROL_MODULES /* The max control types */
}YM_MATRIX_GAIN_CONTROL_MODULE;

/* Gain control params */
typedef enum YM_MATRIX_GAIN_CONTROL_PARAMS_ID_s
{
    YM_MATRIX_GAIN_CONTROL_ON_OFF,                  /* module on/off */
    YM_MATRIX_GAIN_CONTROL_THESHOLD_TARGET,         /* The control AGC target level or threshold (for NG/DRC/Expander/Limiter/Ducker) in 0.1dB unit,
                                                    normally range in 10*[YM_MATRIX_GAIN_THRESHOLD_TARGET_MIN_DB, YM_MATRIX_GAIN_THRESHOLD_TARGET_MAX_DB] dB */
    YM_MATRIX_GAIN_CONTROL_MAKEUP_BOOST_SUPPRESS,   /* The control AGC compression gain or noise Gate suppression gain or makeup gain boost (for DRC/Expander/Limiter) in 0.1dB unit,
                                                    normally range in 10*[YM_MATRIX_GAIN_COMPR_BOOST_MIN_DB, YM_MATRIX_GAIN_COMPR_BOOST_MAX_DB
                                                    for ducker/expander/noiseGate, check the module specific range defined */
    YM_MATRIX_GAIN_CONTROL_ATTACKTIME_MS,           /* The control attack time in ms, normally range in [YM_MATRIX_GAIN_ATTACK_RELEASE_MIN_MS, YM_MATRIX_GAIN_ATTACK_RELEASE_MAX_MS] */
    YM_MATRIX_GAIN_CONTROL_RELEASETIME_MS,          /* The control release time in ms, normally range in [YM_MATRIX_GAIN_ATTACK_RELEASE_MIN_MS, YM_MATRIX_GAIN_ATTACK_RELEASE_MAX_MS] */
    YM_MATRIX_GAIN_CONTROL_COMPR_RATIO,             /* The compression ratio for DRC/Expander in 0.1 unit, range in 10*[YM_MIXER_DRC_EXPAND_MIN_COMPR_RATIO, YM_MIXER_DRC_EXPAND_MAX_COMPR_RATIO] */
    YM_MATRIX_GAIN_CONTROL_AGC_NOISE_LEVEL = YM_MATRIX_GAIN_CONTROL_COMPR_RATIO,/* The AGC noise level in 0.1dB, range in 10*[YM_MATRIX_GAIN_THRESHOLD_TARGET_MIN_DB, YM_MATRIX_GAIN_THRESHOLD_TARGET_MAX_DB] dB */
    YM_MATRIX_GAIN_CONTROL_DUCKER_KEEP_TIME = YM_MATRIX_GAIN_CONTROL_COMPR_RATIO,/* The ducker keep time in ms, range in [1, YM_DUCK_MAX_KEEP_TIME_MS] */
    YM_MATRIX_MAX_GAIN_CONTROL_PARAMS /* The max control params */
}YM_MATRIX_GAIN_CONTROL_PARAMS_ID;

/* Define the peq filter type */
typedef enum YM_MIXER_PEQ_FILTER_TYPE_s
{
    YM_PEQ_LOWPASS_FILTER,
    YM_PEQ_HIGHPASS_FILTER,
    YM_PEQ_PEAKEQ_FILTER,
    YM_PEQ_LOW_SHELF_FILTER,
    YM_PEQ_HIGH_SHELF_FILTER,
    YM_PEQ_NOTCH_FILTER,
    YM_PEQ_MAXI_FILTER_TYPES
} YM_MIXER_PEQ_FILTER_TYPE;

/* Define the PEQ maximum and minimum Q-value in 0.001 degree */
#define YM_PEQ_MAX_Q_VALUE     20000
#define YM_PEQ_MIN_Q_VALUE     20
#define YM_PEQ_DEF_Q_VALUE     707
#define YM_PEQ_MAX_BANDS_NUM    8      // the maximum biquad filter bands for PEQ
#define YM_LPF_HPF_MAX_BANDS_NUM    4   // the maximum biquad filter bands for LPF/HPF
#define YM_MATRIX_MAX_AEC_PEQ_MODULES        2   // The maximum number of modules for AEC PEQ
/* Define howling detection maximum peq filter series available, one have at most YM_PEQ_MAX_BANDS_NUM biquad filters */
#define YM_MIXER_ANF_MAX_PEQ_SERIES_NUM 3
#define YM_MIXER_ANF_MAX_PEQ_FILTERS_NUM (YM_MIXER_ANF_MAX_PEQ_SERIES_NUM * YM_PEQ_MAX_BANDS_NUM)
#define YM_MIXER_ANF_DEF_PEQ_FILTERS_NUM ((YM_MIXER_ANF_MAX_PEQ_SERIES_NUM - 1) * YM_PEQ_MAX_BANDS_NUM)
#define YM_MIXER_ANF_MAX_NOTCH_DEPTH    20  /* notch filter depth limit */
#define YM_MIXER_ANF_DEF_NOTCH_DEPTH    10  /* default notch filter depth limit */

#define YM_MATRIX_MAX_INPUT_MIC_CHANS       (16) // the maximum of input Mic channels
#define YM_MATRIX_MAX_INPUT_AUX_CHANS       (16) // the maximum of input AUX channels
#define YM_MATRIX_MAX_OUTPUT_CHANS_PER_TYPE (16) // the maximum of output channels for one output type
#define YM_MATRIX_DELAY_MAX_MS              (255)   // the maximum delay ms for mic/music/output channels
/* Define the switch on/off flag */
enum {
    YM_MATRIX_FALSE=0,
    YM_MATRIX_TRUE=1,
    YM_MATRIX_DISABLE=0,
    YM_MATRIX_ENABLE=1
};
/* Define input and output channel configurations for matrix */
typedef enum YM_MATRIX_INPUT_CHANNEL_ID_s
{
    YM_MATRIX_INPUT_CHANNEL1=0,
    YM_MATRIX_INPUT_CHANNEL2,
    YM_MATRIX_INPUT_CHANNEL3,
    YM_MATRIX_INPUT_CHANNEL4,
    YM_MATRIX_INPUT_CHANNEL5,
    YM_MATRIX_INPUT_CHANNEL6,
    YM_MATRIX_INPUT_CHANNEL7,
    YM_MATRIX_INPUT_CHANNEL8,
    YM_MATRIX_INPUT_CHANNEL9,
    YM_MATRIX_INPUT_CHANNEL10,
    YM_MATRIX_INPUT_CHANNEL11,
    YM_MATRIX_INPUT_CHANNEL12,
    YM_MATRIX_INPUT_CHANNEL13,
    YM_MATRIX_INPUT_CHANNEL14,
    YM_MATRIX_INPUT_CHANNEL15,
    YM_MATRIX_INPUT_CHANNEL16,   /* Input channels up to 16 */
#ifdef YM_BACKEND_RK3308_FLOAT
    YM_MATRIX_INPUT_CHANNEL17,
    YM_MATRIX_INPUT_CHANNEL18,
    YM_MATRIX_INPUT_CHANNEL19,
    YM_MATRIX_INPUT_CHANNEL20,
    YM_MATRIX_INPUT_CHANNEL21,
    YM_MATRIX_INPUT_CHANNEL22,
#endif // #ifdef YM_BACKEND_RK3308_FLOAT
#ifdef SPEAKER_CONF_PLATFORM
    YM_MATRIX_MAX_INPUT_CHANNELS=2,
#elif defined(YM_BACKEND_RK3308_FLOAT)
    YM_MATRIX_MAX_INPUT_CHANNELS=22,
#else //#ifdef SPEAKER_CONF_PLATFORM
    YM_MATRIX_MAX_INPUT_CHANNELS=16,
#endif // #ifdef SPEAKER_CONF_PLATFORM
    YM_MATRIX_INPUT_AEC_OUTPUT_CHANNEL=YM_MATRIX_MAX_INPUT_CHANNELS,  /* AEC output for matrix mixing */
    YM_MATRIX_INPUT_ECHO_OUTPUT_CHANNEL=YM_MATRIX_MAX_INPUT_CHANNELS,  /* Echo output for matrix mixing */
    YM_MATRIX_INPUT_EXTRA_MATRIX_START_CHANNEL=YM_MATRIX_MAX_INPUT_CHANNELS,  /* The first extra channels route to matrix mixing */
    YM_MATRIX_INPUT_AFC_OUTPUT_CHANNEL=YM_MATRIX_MAX_INPUT_CHANNELS+1,   /* AFC output for matrix mixing */
    YM_MATRIX_INPUT_REVERB_OUTPUT_CHANNEL=YM_MATRIX_MAX_INPUT_CHANNELS+1, /* Reverb output for matrix mixing */
    YM_MATRIX_INPUT_EXTRA_MATRIX_END_CHANNEL=YM_MATRIX_MAX_INPUT_CHANNELS+1,  /* The last extra channels route to matrix mixing */
    YM_MATRIX_INPUT_EXTRA_MATRIX_CHANNELS=2   /* extra channels route to matrix mixing */
}YM_MATRIX_INPUT_CHANNEL_ID;

/* Define the input channel AEC mode, as AEC near-end input or reference or not feed into AEC module */
typedef enum YM_MATRIX_INPUT_CHANNEL_AEC_MODE_s
{
    YM_MATRIX_INPUT_AEC_MIC_NONE,    /* Local Mics don't connect with AEC module */
    YM_MATRIX_INPUT_AEC0_MICS,  /* Local Mics for AEC channel 0 */
    YM_MATRIX_INPUT_AEC1_MICS,  /* Local Mics for AEC channel 1 */
    YM_MATRIX_INPUT_AEC_REF,    /* The Reference for AEC module */
    YM_MATRIX_INPUT_AEC_AUX_NONE,    /* Local AUX don't connect with AEC module */
    YM_MATRIX_INPUT_AEC_MIX,    /* The channel already dealt with AEC, wait for the final mixing, when enable the AEC1 automixer will be used for mixing these channels with local AEC output */
    YM_MATRIX_INPUT_AEC_FAREND_REF,    /* The far-end reference for AEC module */
    YM_MATRIX_INPUT_AEC_AFC_REF,       /* The near-end reference generated by AFC local reinforcement */
    YM_MATRIX_INPUT_AEC_AFC_MIX,    /* The channel will be used for both AEC_MIX and AFC_MIX */
    YM_MATRIX_INPUT_AFC_MIX,    /* The channel already dealt with AFC, wait for the final mixing with local AFC output */
    YM_MATRIX_INPUT_AEC0_MICS_PEQ,  /* Local Mics for AEC channel 0 with PEQ applied */
    YM_MATRIX_INPUT_AEC1_MICS_PEQ,  /* Local Mics for AEC channel 1 with PEQ applied */
    YM_MATRIX_INPUT_AEC0_BKG_REF_CHAN,  /* the background reference channel for AEC0 */
    YM_MATRIX_INPUT_AEC_SPK_REF,       /* The speaker reference generated by speaker output */
    YM_MATRIX_INPUT_AEC1_BKG_REF_CHAN,  /* the background reference channel for AEC1 */
    YM_MATRIX_MAX_INPUT_CHANNEL_AEC_MODES
}YM_MATRIX_INPUT_CHANNEL_AEC_MODE_ID;

/* Define the input channel ducker mode, as a ducker reference input or ducker background to be compressed */
typedef enum YM_MATRIX_INPUT_CHANNEL_DUCKER_MODE_s
{
    YM_MATRIX_INPUT_DUCKER_NONE,    /* Don't connect with ducker module */
    YM_MATRIX_INPUT_DUCKER_REF,     /* as a ducker reference to measure the input signal level */
    YM_MATRIX_INPUT_DUCKER_BACKGROUND,  /* Local ducker background to be compressed */
    YM_MATRIX_INPUT_DUCKER_FIRST_DUCKER_MASK,   /* Reserved mode for first ducker mask */
    YM_MATRIX_INPUT_DUCKER_SECOND_DUCKER_START,   /* the second ducker mask */
    YM_MATRIX_INPUT_DUCKER_SECOND_DUCKER_REF_ONLY,   /* the channel used for second ducker ref only */
    YM_MATRIX_INPUT_DUCKER_FIRST_BKG_SECOND_REF,   /* the channel used for background for first ducker and ref for second ducker */
    YM_MATRIX_MAX_INPUT_CHANNEL_DUCKER_MODES
}YM_MATRIX_INPUT_CHANNEL_DUCKER_MODE_ID;

/* Matrix output channel configuration */
typedef enum YM_MATRIX_OUTPUT_CHANNEL_ID_s
{
    YM_MATRIX_OUTPUT_CHANNEL1=0,
    YM_MATRIX_OUTPUT_CHANNEL2,
    YM_MATRIX_OUTPUT_CHANNEL3,
    YM_MATRIX_OUTPUT_CHANNEL4,
    YM_MATRIX_OUTPUT_CHANNEL5,
    YM_MATRIX_OUTPUT_CHANNEL6,
    YM_MATRIX_OUTPUT_CHANNEL7,
    YM_MATRIX_OUTPUT_CHANNEL8,
    YM_MATRIX_OUTPUT_CHANNEL9,
    YM_MATRIX_OUTPUT_CHANNEL10,
    YM_MATRIX_OUTPUT_CHANNEL11,
    YM_MATRIX_OUTPUT_CHANNEL12,
    YM_MATRIX_OUTPUT_CHANNEL13,
    YM_MATRIX_OUTPUT_CHANNEL14,
    YM_MATRIX_OUTPUT_CHANNEL15,
    YM_MATRIX_OUTPUT_CHANNEL16,   /* Output channels up to 16 */
#ifdef SPEAKER_CONF_PLATFORM
    YM_MATRIX_MAX_OUTPUT_CHANNELS=2
#else // #ifdef SPEAKER_CONF_PLATFORM
    YM_MATRIX_MAX_OUTPUT_CHANNELS=16
#endif // #ifdef SPEAKER_CONF_PLATFORM
}YM_MATRIX_OUTPUT_CHANNEL_ID;

/* Matrix output channel type */
typedef enum YM_MATRIX_OUTPUT_CHANNEL_TYPE_s
{
    YM_MATRIX_OUTPUT_CHANNEL_MAIN_SPEAKER,  /* Main speaker output */
    YM_MATRIX_OUTPUT_CHANNEL_SUB_HEADPHONE,   /* Sub speaker or headphone output */
    YM_MATRIX_OUTPUT_CHANNEL_REMOTE_OUT_REC,  /* Remote output for farend or record channel*/
    YM_MATRIX_OUTPUT_CHANNEL_FAREND_REF,  /* The farend reference in the speaker output, no matrix mixing required for this channel */
    YM_MATRIX_OUTPUT_CHANNEL_LOCAL_REF,   /* The local reference in the speaker output, that is the speaker output excluding the far-end ref */
    YM_MATRIX_OUTPUT_CHANNEL_SPK_REF_COPY,   /* The reference in the speaker output, copied from YM_MATRIX_OUTPUT_CHANNEL_MAIN_SPEAKER in ascending order */
    YM_MATRIX_MAX_OUTPUT_CHANNEL_TYPES
}YM_MATRIX_OUTPUT_CHANNEL_TYPE_ID;

/* Define the channel-based control params for matrix */
typedef enum YM_MATRIX_CONTROL_MATRIX_PARAMS_ID_s
{
    YM_MATRIX_SET_MATRIX_MAP_MUTE,              /* To set the input channel to output channel map mute flag, YM_MATRIX_FALSE for no mute; YM_MATRIX_TRUE for mutting */
    YM_MATRIX_SET_MATRIX_MAP_VOL_LEVEL,         /* To set the input channel to output channel volume level map, range in [0, YM_MATRIX_MAX_MATRIX_VOLUME_LEVELS] */
    YM_MATRIX_CONTROL_MAX_MATRIX_PARAMS
}YM_MATRIX_CONTROL_MATRIX_PARAMS_ID;

#define YM_MIXER_ATM_IN_CHAN_MIN_PRIORITY         0           // the auto mixer minimum priority level correspond to -100dB
#define YM_MIXER_ATM_IN_CHAN_DEF_PRIORITY         100         // the auto mixer minimum priority level correspond to +0dB
#define YM_MIXER_ATM_IN_CHAN_MAX_PRIORITY         127         // the auto mixer minimum priority level correspond to +27dB
#define YM_MIXER_ATM_IN_CHAN_MIN_DECAYRATIO        0           // the auto mixer minimum priority level correspond to 0dB
#define YM_MIXER_ATM_IN_CHAN_MAX_DECAYRATIO        100         // the auto mixer minimum priority level correspond to -50dB
#define YM_MIXER_ATM_IN_CHAN_DEF_DECAYRATIO        1           // the auto mixer minimum priority level correspond to -0.5dB
/* Define the channel-based control params for input and output */
typedef enum YM_MATRIX_CONTROL_CHANNEL_MODE_PARAMS_ID_s
{
    YM_MATRIX_SET_INPUT_CHANNEL_VOLUME,         /* To set the input channel volume range in [0, YM_MATRIX_MAX_INPUT_VOLUME_LEVELS] */
    YM_MATRIX_SET_INPUT_CHANNEL_AEC_MODE,       /* To set the input channel AEC mode as defined in YM_MATRIX_INPUT_CHANNEL_AEC_MODE_ID */
    YM_MATRIX_SET_INPUT_CHANNEL_DUCKER_MODE,    /* To set the input channel ducker mode as defined in YM_MATRIX_INPUT_CHANNEL_DUCKER_MODE_ID */
    YM_MATRIX_SET_INPUT_CHANNEL_MUTE,           /* To set the input channel mute: YM_MATRIX_FALSE for no mute; YM_MATRIX_TRUE for mutting */
    YM_MATRIX_SET_INPUT_CHANNEL_DELAY_MS,       /* To set the input channel delay ms: range in [0, YM_MATRIX_DELAY_MAX_MS] */
    YM_MATRIX_SET_OUTPUT_CHANNEL_VOLUME,        /* To set the output channel volume range in [0, YM_MATRIX_MAX_OUTPUT_VOLUME_LEVELS] */
    YM_MATRIX_SET_OUTPUT_CHANNEL_TYPE,          /* To set the output channel type as defined in YM_MATRIX_OUTPUT_CHANNEL_TYPE_ID */
    YM_MATRIX_SET_OUTPUT_CHANNEL_MUTE,          /* To set the output channel mute: YM_MATRIX_FALSE for no mute; YM_MATRIX_TRUE for mutting */
    YM_MATRIX_SET_OUTPUT_CHANNEL_PHASE_INVERT,  /* To set the output channel phase invert: YM_MATRIX_FALSE for no invert; YM_MATRIX_TRUE for inverting */
    YM_MATRIX_SET_OUTPUT_CHANNEL_DELAY_MS,      /* To set the output channel delay ms: range in [0, YM_MATRIX_DELAY_MAX_MS] */
    YM_MATRIX_SET_INPUT_MIC_AUTOMIX_PRIORITY,   /* To set the input mic auto mixer priority level, range in [YM_MIXER_ATM_IN_CHAN_MIN_PRIORITY, YM_MIXER_ATM_IN_CHAN_MAX_PRIORITY] */
    YM_MATRIX_SET_INPUT_MIC_AUTOMIX_DECAY_RATIO,/* To set the input mic auto mixer decay ratio, range in [0, 100], -0.5dB per step, for each automixer, if greatest gain not zero, use the decay ratio setting in each automixer module else use the decay ratio in ascending order for an automixer group */
    YM_MATRIX_CONTROL_MAX_CHANNEL_MODE_PARAMS
}YM_MATRIX_CONTROL_CHANNEL_MODE_PARAMS_ID;

/* Test signal types */
typedef enum YM_MATRIX_NOISEGEN_TYPE_s
{
    YM_MATRIX_NO_TEST_SIGNAL, // no test signal, normal process
    YM_MATRIX_TEST_OUTPUT_SINE_TONE, // output sine tone
    YM_MATRIX_TEST_OUTPUT_WHITE_NOISE, // output white noise
    YM_MATRIX_TEST_OUTPUT_PINK_NOISE, // output pink noise
    YM_MATRIX_TEST_INPUT_SINE_TONE, // input sine tone
    YM_MATRIX_TEST_INPUT_WHITE_NOISE, // input white noise
    YM_MATRIX_TEST_INPUT_PINK_NOISE, // input pink noise
    YM_MATRIX_TEST_MAX_NOISE_TYPES
}YM_MATRIX_NOISEGEN_TYPE;
/* Test signal sine tone default frequency  */
#define YM_MATRIX_TEST_SIGNAL_DEF_FREQ     (1000)
/* Test signal level range in dB */
#define YM_MATRIX_TEST_SIGNAL_MAX_LEVEL_DB     (0)
#define YM_MATRIX_TEST_SIGNAL_MIN_LEVEL_DB     (-120)
#define YM_MATRIX_TEST_SIGNAL_DEF_LEVEL_DB     (-18)

/* Define the noise suppression policy, add the disabled mode as well */
typedef enum YM_MIXER_NS_MODE_s
{
    YM_MIXER_NS_MODE_DISABLE,   /* Disable denoise module */
    YM_MIXER_NS_MODE0,          /* Set denoise policy mode 0, -6dB at most */
    YM_MIXER_NS_MODE1,          /* Set denoise policy mode 1, -12dB at most */
    YM_MIXER_NS_MODE2,          /* Set denoise policy mode 2, -18dB at most */
    YM_MIXER_NS_MODE3,          /* Set denoise policy mode 3, -24dB at most */
    YM_MIXER_NS_MODE4,          /* Set denoise policy mode 4, -30dB at most */
    YM_MIXER_NS_BYPASS_MODE,    /* Set denoise bypass mode, gain set as 0dB only with the low-pass and high-pass */
    YM_MIXER_DUCK_NS_MODE0,     /* Set duck denoise policy mode 0, -6dB at most */
    YM_MIXER_DUCK_NS_MODE1,     /* Set duck denoise policy mode 1, -12dB at most */
    YM_MIXER_DUCK_NS_MODE2,     /* Set duck denoise policy mode 2, -18dB at most */
    YM_MIXER_DUCK_NS_MODE3,     /* Set duck denoise policy mode 3, -24dB at most */
    YM_MIXER_DUCK_NS_MODE4,     /* Set duck denoise policy mode 4, -30dB at most */
    YM_MIXER_DUCK_NS_BYPASS_MODE,    /* Set duck denoise bypass mode, gain set as 0dB only with the low-pass and high-pass */
    YM_MIXER_LOWL_NS_MODE0,     /* Set low latency denoise policy mode 0, -6dB at most */
    YM_MIXER_LOWL_NS_MODE1,     /* Set low latency denoise policy mode 1, -12dB at most */
    YM_MIXER_LOWL_NS_MODE2,     /* Set low latency denoise policy mode 2, -18dB at most */
    YM_MIXER_LOWL_NS_MODE3,     /* Set low latency denoise policy mode 3, -24dB at most */
    YM_MIXER_LOWL_NS_MODE4,     /* Set low latency denoise policy mode 4, -30dB at most */
    YM_MIXER_LOWL_NS_BYPASS_MODE,    /* Set duck denoise bypass mode, gain set as 0dB only with the low-pass and high-pass */
    YM_MIXER_NS_MAX_MODES
}YM_MIXER_NS_MODE;

#define YM_MIXER_NS_DEFAULT_MODE    YM_MIXER_NS_MODE_DISABLE   /* By default, disable */
#define YM_MIXER_NS_MUSIC_DEFAULT_MODE   YM_MIXER_NS_MODE_DISABLE   /* By default, disable */

/* Define AEC modes */
typedef enum YM_MIXER_AEC_MODES_s
{
    YM_MIXER_AEC_DISABLED,      /* Disable AEC */
    YM_MIXER_AEC_ONLY_FILTER,   /* Only Filter without NLP */
    YM_MIXER_AEC_WITHOUT_NLP,   /* run NLP, but no apply */
    YM_MIXER_AEC_DTD_MODE1, /* Enable AEC NLP DTD, DTD_CONSERVATIVE */
    YM_MIXER_AEC_DTD_MODE2,     /* Enable AEC NLP DTD, DTD_MODERATE */
    YM_MIXER_AEC_DTD_MODE3,     /* Enable AEC NLP DTD, DTD_MODERATE */
    YM_MIXER_AEC_DTD_MODE4,   /* Enable AEC NLP DTD, DTD_AGGRESSIVE */
    YM_MIXER_AEC_DTD_MODE5,   /* Enable AEC NLP DTD, DTD_AGGRESSIVE */
    YM_MIXER_AEC_DTD_MODE6,   /* Enable AEC NLP DTD, DTD_AGGRESSIVE */
    YM_MIXER_AEC_MODE1,     /* Enable AEC NLP, CONSERVATIVE */
    YM_MIXER_AEC_MODE2,     /* Enable AEC NLP, MODERATE */
    YM_MIXER_AEC_MODE3,     /* Enable AEC NLP, MODERATE */
    YM_MIXER_AEC_MODE4,     /* Enable AEC NLP, AGGRESSIVE */
    YM_MIXER_AEC_MODE5,     /* Enable AEC NLP, AGGRESSIVE */
    YM_MIXER_AEC_MODE6,     /* Enable AEC NLP, AGGRESSIVE */
    YM_MIXER_AEC_TUNING,        /* Enable AEC NLP Tuning */
    YM_MIXER_AEC_BYPASS_MODE,   /* AEC bypass mode */
    YM_MIXER_AEC_MAX_MODES = YM_MIXER_AEC_BYPASS_MODE      /* AEC Maxi mode */
} YM_MIXER_AEC_MODES;

#define  YM_MIXER_SPEECH_RESTORE_MAX_LEVELS 10  // The speech restoration max level
#define  YM_MIXER_SPEECH_RESTORE_MIN_LEVELS 0   // The speech restoration min level
#define  YM_MIXER_AFC_REPRO_MAX_LEVELS      10  // The AFC maximum reproduction level
#define  YM_MIXER_AFC_REPRO_MIN_LEVELS      0   // The AFC minimum reproduction level
#ifndef ENABLE_TWO_AEC_PATH
#ifndef LIMIT_AEC_PATH_TO_ONE
#define LIMIT_AEC_PATH_TO_ONE
#endif // #ifndef LIMIT_AEC_PATH_TO_ONE
#endif // #ifndef ENABLE_TWO_AEC_PATH
#ifdef LIMIT_AEC_PATH_TO_ONE
#define YM_AEC_MAX_PROCESS_MICS             (1) // maximum mics for AEC signal processing input
#else
#define YM_AEC_MAX_PROCESS_MICS             (2) // maximum mics for AEC signal processing input
#endif // #ifdef LIMIT_AEC_PATH_TO_ONE
#define YM_AEC_MAX_SPEAKERS                 (8) // maximum speakers for AFC output

#define YM_MIXER_AEC_MIN_DELAY      0
#define YM_MIXER_AEC_DEFAULT_DELAY  8
#ifdef SPEAKER_CONF_PLATFORM
#define YM_MIXER_AEC_MAX_DELAY      25
#else // #ifdef SPEAKER_CONF_PLATFORM
#define YM_MIXER_AEC_MAX_DELAY      255
#endif // #ifdef SPEAKER_CONF_PLATFORM

/* AEC filter length in 1ms block */
#define YM_MIXER_AEC_DEFAULT_FILTER_LEN 120
#ifdef AEC_AFC_48K
#define YM_MIXER_AEC_MIN_FILTER_LEN     40
#ifdef SPEAKER_CONF_PLATFORM
#define YM_MIXER_AEC_MAX_FILTER_LEN     220
#else // #ifdef SPEAKER_CONF_PLATFORM
#define YM_MIXER_AEC_MAX_FILTER_LEN     512
#endif // #ifdef SPEAKER_CONF_PLATFORM
#else
#define YM_MIXER_AEC_MIN_FILTER_LEN     8
#ifdef SPEAKER_CONF_PLATFORM
#define YM_MIXER_AEC_MAX_FILTER_LEN     220
#else // #ifdef SPEAKER_CONF_PLATFORM
#define YM_MIXER_AEC_MAX_FILTER_LEN     300
#endif // #ifdef SPEAKER_CONF_PLATFORM
#endif // #ifdef AEC_AFC_48K
#define YM_MIXER_AEC_NOISEGATE_DEF_LEVEL_DB       YM_AGC_NOISE_LEVEL_DEF_DB
#define YM_MIXER_AEC_BKG_MAX_DECAY_DB               20

/* Define howling detection mode */
typedef enum YM_MIXER_CONTROL_HOWLING_DETECTION_ID_s
{
    YM_MIXER_HOWLING_DETECTION_OFF,         /* howling detection off */
    YM_MIXER_HOWLING_DETECTION_ON,          /* howling detection with AEC and denoise off */
    YM_MIXER_HOWLING_DETECTION_NORMAL_ON,   /* howling detection with AEC and denoise on */
    YM_MIXER_HOWLING_DETECTION_NEARFIELD_MIC,   /* howling detection for near-field mics, detect the frequency with peaks */
    YM_MIXER_HOWLING_DETECTION_MAX_MODES
}YM_MIXER_CONTROL_HOWLING_DETECTION;

/* Define the echo/reverb controller params */
typedef enum YM_MIXER_CONTROL_ECHO_REVERB_PARAMS_ID_s
{
    YM_MIXER_ENABLE_HOWLING_DETECTION,          /* Set the howling detection flag: define in YM_MIXER_CONTROL_HOWLING_DETECTION */
    YM_MIXER_SET_AEC_MODE,                      /* Set the aec mode */
    YM_MIXER_SET_AEC_MIC_NUMBER,                /* Set the AFC mic number: 1 to 2 */
    YM_MIXER_SET_AEC_SPEAKER_NUMBER,            /* Set the AFC speaker number: 1 to 4 */
    YM_MIXER_SET_AEC_NOISE_GATE_LEVEL,          /* Set AEC noise level in 0.1dB, available range in 10*[YM_MATRIX_GAIN_THRESHOLD_TARGET_MIN_DB, YM_MATRIX_GAIN_THRESHOLD_TARGET_MAX_DB] dB, similar to YM_MATRIX_GAIN_CONTROL_AGC_NOISE_LEVEL protocol */
    YM_MIXER_SET_HOWLING_FILTER_NUM,            /* Set the howling suppression filter numbers, range in [0, YM_MIXER_ANF_MAX_PEQ_FILTERS_NUM] */
    YM_MIXER_SET_HOWLING_FILTER_DEPTH,          /* Set the howling suppression notch filter depth limit, range in [0, YM_MIXER_ANF_MAX_NOTCH_DEPTH] */
    YM_MIXER_SET_AEC_DELAYLINE_MS_FILTER_LEN_MS,/* Set the AFC estimated delay samples/filter length in ms for up to 2 microphones */
    YM_MIXER_SET_AFC_REPRO_LEVEL,               /* Set AFC reproduction level range in [YM_MIXER_AFC_REPRO_MIN_LEVELS, YM_MIXER_AFC_REPRO_MAX_LEVELS] */
    YM_MIXER_SET_AFC_FREQ_SHIFT_LEVEL,          /* Set AFC frequency shift level in Hz */
    YM_MIXER_CONTROL_MAX_ECHO_REVERB_PARAMS
} YM_MIXER_CONTROL_ECHO_REVERB_PARAMS_ID;

/* Define the module ID to be processed */
typedef enum YM_MIXER_PROCESS_MODULE_ID_s
{
    YM_MIXER_MIC_IN,        /* Input mic */
    YM_MIXER_MUSIC_IN,      /* Input music */
    YM_MIXER_SP_OUT,        /* Speaker output */
    YM_MIXER_HP_OUT,        /* headphone output */
    YM_MIXER_REC_OUT,       /* recorder output */
    YM_MIXER_MIC_HOWLING_SUPPR0,        /* howling suppression 0 */
    YM_MIXER_MIC_HOWLING_SUPPR1,        /* howling suppression 1 */
    YM_MIXER_MIC_HOWLING_SUPPR2,        /* howling suppression 2 */
    YM_MIXER_MIC_AEC0_PEQ,   /* AEC0 PEQ */
    YM_MIXER_MIC_AEC1_PEQ,   /* AEC1 PEQ */
    YM_MIXER_MAX_MODULES,    /* The maximum PEQ modules */
    YM_MIXER_EQ_MODULE_NUM=5    /* the maximum GEQ and HP/LP module number */
}YM_MIXER_PROCESS_MODULE_ID;

/* Define the GEQ, PEQ, HPF, LPF filter params */
typedef enum YM_MIXER_EQ_FILTER_PARAMS_ID_s
{
    YM_MIXER_SET_GEQ_ON_OFF,          /* Set the GEQ on/off */
    YM_MIXER_SET_GEQ_BANDS_NUM,       /* Set the GEQ bands number, range in [YM_MIXER_MIN_GEQ_BANDS, YM_MIXER_MAX_GEQ_BANDS] */
    YM_MIXER_SET_GEQ_BANDS_FREQ,      /* Set the GEQ center frequency in Hz, range in [YM_MIXER_MIN_EQ_CENTER_FREQ, YM_MIXER_MAX_EQ_CENTER_FREQ] */
    YM_MIXER_SET_GEQ_BANDS_GAIN,      /* Set the GEQ gain in 0.1dB, range in [YM_MIXER_MIN_EQ_GAIN, YM_MIXER_MAX_EQ_GAIN] * 0.1dB */
    YM_MIXER_SET_PEQ_ON_OFF,          /* Set the PEQ on/off */
    YM_MIXER_SET_PEQ_BANDS_NUM,       /* Set the PEQ bands number, range in [0, YM_PEQ_MAX_BANDS_NUM] */
    YM_MIXER_SET_PEQ_BANDS_FREQ,      /* Set the PEQ center frequency in Hz, range in [YM_MIXER_MIN_EQ_CENTER_FREQ, YM_MIXER_MAX_EQ_CENTER_FREQ] */
    YM_MIXER_SET_PEQ_BANDS_GAIN,      /* Set the PEQ gain in 0.1dB, range in [YM_MIXER_MIN_EQ_GAIN, YM_MIXER_MAX_EQ_GAIN] * 0.1dB */
    YM_MIXER_SET_PEQ_FILTER_TYPE,     /* Set the PEQ filter type as defined in YM_MIXER_PEQ_FILTER_TYPE */
    YM_MIXER_SET_PEQ_BANDS_QVALUE,    /* Set the PEQ Q (quality factor or slope) value in 0.001 degree */
    YM_MIXER_MAX_EQ_FILTER_PARAMS         /* The maximum params available */
}YM_MIXER_EQ_FILTER_PARAMS_ID;

/* Define the dereverb mode */
typedef enum YM_MATRIX_DEREVERB_ID_s
{
    YM_MATRIX_DEREVERB_OFF=0,   /* Set dereverb policy mode off */
    YM_MATRIX_DEREVERB_MODE1,   /* Set dereverb policy mode 1, small room */
    YM_MATRIX_DEREVERB_MODE2,   /* Set dereverb policy mode 1, medium room */
    YM_MATRIX_DEREVERB_MODE3,   /* Set dereverb policy mode 1, large room */
    YM_MATRIX_DEREVERB_MODE4,   /* Set dereverb policy mode 1, huge room */
    YM_MATRIX_DEREVERB_MODE5,   /* Set dereverb policy mode 5, huge room */
    YM_MATRIX_DEREVERB_MAX_MODES
}YM_MATRIX_DEREVERB_ID;

/* Define the control params for system module */
typedef enum YM_MIXER_CONTROL_SYS_MODULE_PARAMS_ID_s
{
    YM_MATRIX_SET_MIC_INPUT_VOLUME,         /* Set the mic input volume, range in [0, YM_MATRIX_MAX_INPUT_VOLUME_LEVELS] */
    YM_MATRIX_SET_SYSTEM_OUT_VOLUME,        /* Set the system output volume, range in [0, YM_MATRIX_MAX_OUTPUT_VOLUME_LEVELS] */
    YM_MATRIX_SET_MIC_INPUT_MUTE,           /* Set the mic input mute, 0 for unmute, else mute */
    YM_MATRIX_SET_SYSTEM_OUT_MUTE,          /* Set the system output mute, 0 for unmute, else mute */
    YM_MIXER_SET_MIC_DELAY_LEVELS,         /* Time delay for mic input, range in [0, YM_MATRIX_DELAY_MAX_MS], default 0 */
    YM_MIXER_SET_MUSIC_DELAY_LEVELS,       /* Time delay for music input, range in [0, YM_MATRIX_DELAY_MAX_MS], default 0 */
    YM_MIXER_SET_AUX_BGM_DELAY_LEVELS,     /* Time delay for background music input, range in [0, YM_MATRIX_DELAY_MAX_MS], default 0, if set as YM_MATRIX_DELAY_MAX_MS, compensated with automatic delay samples */
    YM_MIXER_SET_MIC_NS_MODE,               /* the denoise mode for mic input, range as defined in YM_MIXER_NS_MODE */
    YM_MIXER_SET_DEREVERB_MODE,             /* Set the dereverb mode define in YM_MATRIX_DEREVERB_ID */
    YM_MIXER_SET_TEST_SINGAL_MODE,          /* Set the test signal mode as in YM_MATRIX_NOISEGEN_TYPE */
    YM_MIXER_SET_TEST_SINGAL_FREQ,          /* Set the test signal center frequency range in [20, 20000], default YM_MATRIX_TEST_SIGNAL_DEF_FREQ */
    YM_MIXER_SET_TEST_SINGAL_LEVELDB,       /* Set the test signal level in 0.1dB unit, range in 10*[YM_MATRIX_TEST_SIGNAL_MIN_LEVEL_DB, YM_MATRIX_TEST_SIGNAL_MAX_LEVEL_DB], default YM_MATRIX_TEST_SIGNAL_DEF_LEVEL_DB */
    YM_MATRIX_SET_SPEAKER_OUT_VOLUME,        /* Set the speaker output volume, range in [0, YM_MATRIX_MAX_OUTPUT_VOLUME_LEVELS] */
    YM_MATRIX_SET_SPEAKER_OUTPUT_MUTE,           /* Set the speaker output mute, 0 for unmute, else mute */
    YM_MIXER_SET_MALE_SPEECH_ENHANCE_LEVEL,       /* Set the speech enhancement level range in [YM_MIXER_SPEECH_RESTORE_MIN_LEVELS, YM_MIXER_SPEECH_RESTORE_MAX_LEVELS] */
    YM_MIXER_SET_SPEECH_RESTORE_LEVEL,       /* Set the speech restoration level range in [YM_MIXER_SPEECH_RESTORE_MIN_LEVELS, YM_MIXER_SPEECH_RESTORE_MAX_LEVELS] */
    YM_MIXER_SET_AEC_REF_DS_MODE,           /* To set the aec ref denoise mode */
    YM_MIXER_SET_AEC2_DS_MODE,           /* To set the aec2 path denoise mode */
#if defined(YM_BACKEND_RK3308_FLOAT)
    YM_MIXER_SET_AEC_BKG_DECAY_DB,           /* To set the aec background channel decay level in dB, range in [0, YM_MIXER_AEC_BKG_MAX_DECAY_DB] */
#endif // #if defined(YM_BACKEND_RK3308_FLOAT)
    YM_MATRIX_SET_AEC_OUT_VOLUME,        /* Set the aec output volume, range in [0, YM_MATRIX_MAX_OUTPUT_VOLUME_LEVELS] */
    YM_MATRIX_SET_AFC_OUT_VOLUME,        /* Set the afc output volume, range in [0, YM_MATRIX_MAX_OUTPUT_VOLUME_LEVELS] */
    YM_MATRIX_SET_AEC_OUT_MUTE,        /* Set the aec output mute flag */
    YM_MATRIX_SET_AFC_OUT_MUTE,        /* Set the afc output mute flag */
    YM_MIXER_SET_AEC1_AEC1_BYPASS_MODE, /* To set the aec1 bypass mic with AEC linear filtering */
    YM_MIXER_CONTROL_MAX_SYS_MODULE_PARAMS
}YM_MIXER_CONTROL_SYS_MODULE_PARAMS_ID;

/* Define the control params for query structure */
typedef enum YM_MIXER_QUERY_PARAMS_ID_s
{
    YM_MIXER_GET_SPEAKER_MUSIC_DELAY,       /* The algorithm delay in samples for the signal path from the music input to speaker output */
    YM_MIXER_GET_SPEAKER_MIC_DELAY,         /* The algorithm delay in samples for the signal path from the mic input to speaker output */
    YM_MIXER_GET_HEADPHONE_MUSIC_DELAY,     /* The algorithm delay in samples for the signal path from the music input to headphone output */
    YM_MIXER_GET_HEADPHONE_MIC_DELAY,       /* The algorithm delay in samples for the signal path from the mic input to headphone output */
    YM_MIXER_GET_RECORD_MUSIC_DELAY,        /* The algorithm delay in samples for the signal path from the music input to record output */
    YM_MIXER_GET_RECORD_MIC_DELAY,          /* The algorithm delay in samples for the signal path from the mic input to record output */
    YM_MIXER_GET_MIC_IN_NUMBER,             /* Get the mic number in the system */
    YM_MIXER_GET_AUX_IN_NUMBER,             /* Get the Aux number in the system */
    YM_MIXER_GET_SP_OUT_NUMBER,             /* Get the speaker number in the system */
    YM_MIXER_GET_HP_OUT_NUMBER,             /* Get the headphone number in the system */
    YM_MIXER_GET_REC_OUT_NUMBER,            /* Get the record output number in the system */
    YM_MIXER_GET_MAX_QUERY_PARAMS
}YM_MIXER_QUERY_PARAMS_ID;

#define YM_MIXER_NO_ERROR              (0x000000)
#define YM_MIXER_WRONG_PARAM           (0x030001)
#define YM_MIXER_WRONG_BLOCKSIZE       (0x030002)
#define YM_MIXER_NULL_POINTER          (0x030003)
#define YM_MIXER_WRONG_CHANNEL_NUM     (0x030004)
#define YM_MIXER_NO_VALID_OUTPUT       (0x030005)   /* When query the result, no valid data available */

// the input block size, must be a multiple of 32
#ifdef AEC_INBLOCK_10_MS
#define MIXER_INPUT_BLOCK_SIZE            (480)
#elif defined(AEC_INBLOCK_11_MS)
#define MIXER_INPUT_BLOCK_SIZE            (512)
#elif defined(AEC_INBLOCK_8_MS)
#define MIXER_INPUT_BLOCK_SIZE            (384)
#elif defined(AEC_INBLOCK_4_MS)
#define MIXER_INPUT_BLOCK_SIZE            (192)
#else // #ifdef AEC_INBLOCK_10_MS
#ifdef AEC_AFC_48K
#ifdef AEC_AFC_48K_LOW_LATENCY
#define MIXER_INPUT_BLOCK_SIZE            (256)
#else // #ifdef AEC_AFC_48K_LOW_LATENCY
#define MIXER_INPUT_BLOCK_SIZE            (1024)
#endif // #ifdef AEC_AFC_48K_LOW_LATENCY
#elif defined(AEC_AFC_24K) // #ifdef AEC_AFC_48K
#ifdef AEC_AFC_24K_HIGH_LATENCY
#define MIXER_INPUT_BLOCK_SIZE            (1024)
#else // #ifdef AEC_AFC_24K_HIGH_LATENCY
#define MIXER_INPUT_BLOCK_SIZE            (256)
#endif // #ifdef AEC_AFC_24K_HIGH_LATENCY
#else // #ifdef AEC_AFC_48K
#if defined(SIMULATE_FEEDBACK_RPE_16K) || defined(MIXER_INPUT_FS_16K)
#define MIXER_INPUT_BLOCK_SIZE            (256 - 6*32)
#else
#define MIXER_INPUT_BLOCK_SIZE            (256 - 2*32)
#endif // #if defined(SIMULATE_FEEDBACK_RPE_16K) || defined(MIXER_INPUT_FS_16K)
#endif // #ifdef AEC_AFC_48K
#endif // #ifdef AEC_INBLOCK_10_MS

// the maxi volume level
#define YM_VOLUME_MAX_LEVELS              (100)

typedef struct ym_mixer_state_s ym_mixer_state;

#if !defined YM_BACKEND
#define YM_RESTRICT
#endif
/* Get the memory requirement for matrix module */
#ifndef ENABLE_MIC_ARRAY_MODULE
/*
** block_size: the block size for processing
** p_fastStaSize: the fast static buffer size
** p_sta_size: the static buffer size
** p_dyn_size: the dynamic memory size
** inMaxChans: the maximum number of input channels, must be even
** outMaxChans: the maximum number of output channels, must be even
** p_in: input channel starting address, in[0] point to the start address for left channel, in[1] point to the start address for right channel etc.
         could be NULL, which means the macro REUSE_INBUF_AS_INTERNAL doesn't function
** return --> could be 0 for no error or any error code above
*/
int ym_matrix_querymem(unsigned int block_size, unsigned int *p_fastStaSize, unsigned int *p_sta_size, unsigned int *p_dyn_size, unsigned int inMaxChans, unsigned int outMaxChans, int *YM_RESTRICT p_in[]);

/* Init the instance for matrix module */
/* sampleRate: the init sampleRate
** block_size: the block size for processing
** p_fastStatic: pointer to the fast static buffer, if NULL, use the p_static buffer instead
** p_static: pointer to the static buffer
** p_scratch: pointer to the dynamic buffer
** inMaxChans: the maximum number of input channels, must be even
** outMaxChans: the maximum number of output channels, must be even
** nTotDynBufBytes: the number of dynamic buffer size in bytes from ym_matrix_querymem
** p_in: input channel starting address, in[0] point to the start address for left channel, in[1] point to the start address for right channel etc.
could be NULL, which means the macro REUSE_INBUF_AS_INTERNAL doesn't function
*/
ym_mixer_state * ym_matrix_open(unsigned int sampleRate, unsigned int block_size, void *p_fastStatic, void *p_static, void *p_scratch, unsigned int inMaxChans, unsigned int outMaxChans, unsigned int nTotDynBufBytes, int *YM_RESTRICT p_in[]);
#else // #ifndef ENABLE_MIC_ARRAY_MODULE
int ym_matrix_querymem(unsigned int block_size, unsigned int *p_fastStaSize, unsigned int *p_sta_size, unsigned int *p_dyn_size, unsigned int inMaxChans, unsigned int outMaxChans, int *YM_RESTRICT p_in[], unsigned int maAecFlag);
ym_mixer_state * ym_matrix_open(unsigned int sampleRate, unsigned int block_size, void *p_fastStatic, void *p_static, void *p_scratch, unsigned int inMaxChans, unsigned int outMaxChans, unsigned int nTotDynBufBytes, int *YM_RESTRICT p_in[], unsigned int maAecFlag);
#endif // #ifndef ENABLE_MIC_ARRAY_MODULE
/* To set the mixer id for logging system */
int mixer_set_mixer_id(ym_mixer_state *YM_RESTRICT p_mixer, unsigned char id);

/* To set the mixer trailer mode */
void mixer_set_trailer_mode(ym_mixer_state *YM_RESTRICT p_mixer, unsigned int mode);
void mixer_set_trailer_volume(ym_mixer_state *YM_RESTRICT p_mixer, unsigned int vol);

// do matrix manipulation for one block of data
/* ** p_mixer: pointer to the initialized ym_mixer_state instance return from ym_matrix_open
** p_in: input channel starting address, in[0] point to the start address for left channel, in[1] point to the start address for right channel etc.
** p_out: output channel starting address, must be stereo, out[0] point to the start address for left channel, out[1] point to the start address for right channel, could be the same as input buffer
** inOffset: the stride for the mixer input, could be 1 (non-interleave), 2 (stereo interleave input) or 4 (two stereo combined), should be 1 if YM_MIXER_INPUT_BUF_NON_INTERLEAVE defined
** outOffset: the stride for the output, could be 1 (non-interleave), 2 (stereo interleave input) or 4 (two stereo combined) or 6 (three stereo combined)
** p_ChanMax: the input/output channel RMS meters for the current frames
** p_inStartAddr: the starting address for the input buffer
** inSize: the total size of the input buffer from p_inStartAddr in bytes
** return --> could be 0 for no error or any error code above
*/
#ifdef _TMS320C6X
int ym_matrix_process(ym_mixer_state *YM_RESTRICT p_mixer, int *YM_RESTRICT p_in[], int *YM_RESTRICT p_out[], int inOffset, int outOffset, unsigned int *YM_RESTRICT p_ChanMax, int *p_inStartAddr, unsigned int inSize);
#ifndef YM_MIXER_INPUT_BUF_NON_INTERLEAVE
// the main API for matrix processing of short input/output
int ym_matrix_processShort(ym_mixer_state *YM_RESTRICT p_mixer, short *YM_RESTRICT p_in[], short *YM_RESTRICT p_out[], int inOffset, int outOffset, unsigned int *YM_RESTRICT p_ChanMax, int *p_inStartAddr, unsigned int inSize);
#endif // #ifndef YM_MIXER_INPUT_BUF_NON_INTERLEAVE
#else // #ifdef _TMS320C6X
int ym_matrix_process(ym_mixer_state *YM_RESTRICT p_mixer, int *YM_RESTRICT p_in[], int *YM_RESTRICT p_out[], int inOffset, int outOffset, unsigned int *YM_RESTRICT p_ChanMax);
#endif // #ifdef _TMS320C6X
/* Set the matrix channel-based input to output mapping, check YM_MATRIX_CONTROL_MATRIX_PARAMS_ID for available control params */
int matrix_set_matrix_map_params(ym_mixer_state *YM_RESTRICT pMixer, YM_MATRIX_CONTROL_MATRIX_PARAMS_ID paramsID, unsigned int inChan, unsigned int outChan, int value);

/* Set the input/output channel mode, such as the input type/input AEC mode/ output type etc, check YM_MATRIX_CONTROL_CHANNEL_MODE_PARAMS_ID for more details */
int matrix_set_channel_mode(ym_mixer_state *YM_RESTRICT pMixer, YM_MATRIX_CONTROL_CHANNEL_MODE_PARAMS_ID paramsID, unsigned int chan, int value);

/* Set the gain control params for NoiseGate/DRC/Expander/AGC/Limiter/Ducker etc, check YM_MATRIX_GAIN_CONTROL_MODULE and YM_MATRIX_GAIN_CONTROL_PARAMS_ID for available input */
int mixer_set_gain_control_params(ym_mixer_state *YM_RESTRICT p_mixer, YM_MATRIX_GAIN_CONTROL_MODULE moduleID, YM_MATRIX_GAIN_CONTROL_PARAMS_ID paramsID, int value);

/* Set the GEQ and filter params for the specified module as in YM_MIXER_PROCESS_MODULE_ID and params as in YM_MIXER_EQ_FILTER_PARAMS_ID */
int mixer_set_geq_filter(ym_mixer_state *YM_RESTRICT pMixer, YM_MIXER_PROCESS_MODULE_ID moduleID, YM_MIXER_EQ_FILTER_PARAMS_ID paramsID, short *value);

/* Set the echo and reverb params for the specified params as in YM_MIXER_CONTROL_ECHO_REVERB_PARAMS_ID */
int mixer_set_echo_reverb_params(ym_mixer_state *YM_RESTRICT pMixer, YM_MIXER_CONTROL_ECHO_REVERB_PARAMS_ID paramsID, short *value);

/* Set the system module params for the specified params as in YM_MIXER_CONTROL_SYS_MODULE_PARAMS_ID */
int mixer_set_sys_module_params(ym_mixer_state *YM_RESTRICT pMixer, YM_MIXER_CONTROL_SYS_MODULE_PARAMS_ID paramsID, int value);

/* Get the system params for the specified params as in YM_MIXER_QUERY_PARAMS_ID */
int mixer_query_system_params(ym_mixer_state *YM_RESTRICT pMixer, YM_MIXER_QUERY_PARAMS_ID paramsID, int *value);

/* Get the lib version */
char* mixer_query_version(void);

// read howling frequency and the corresponding PEQ gain for last group
void mixer_read_last_howling_info(ym_mixer_state *YM_RESTRICT p_mixer, unsigned short *howling_freq, short *howling_gain, short *howling_Q);
#ifndef DISABLE_AEC_NLP_MODULE
// to set the total energy for AEC tuning, used for AFC filter energy balance
void mixerSetMeasuredAECFilterEng(ym_mixer_state *YM_RESTRICT p_mixer, unsigned int iChannel, float totEng);
/* to Print the LMS coefs */
int mixer_print_lms_coefs(ym_mixer_state *YM_RESTRICT p_mixer);
/* Get/Set the LMS coef pointer and coef length */
float * mixer_get_lms_coefPtr(ym_mixer_state *YM_RESTRICT p_mixer, unsigned int micNum, unsigned int *coefLen);

#if !defined(AEC_AFC_48K) && !defined(AEC_AFC_24K_HIGH_LATENCY)
// to check the howling detection done, i.e. the available PEQ filters are occupied
int mixer_check_howling_detection_done(ym_mixer_state *YM_RESTRICT p_mixer, unsigned int *YM_RESTRICT newHowlFreq);
// read howling frequency and the corresponding PEQ/DRC gain.
void mixer_read_howling_info(ym_mixer_state *YM_RESTRICT pMixer, unsigned short *howling_freq, short *howling_gain, short *howling_Q, int *num_howling_freq, int *drcMakeupGain);
// reset the howling detection state and init the DRC gain
void mixer_reset_howling_state(ym_mixer_state *YM_RESTRICT p_mixer);
#endif // #if !defined(AEC_AFC_48K) && !defined(AEC_AFC_24K_HIGH_LATENCY)

/* To return the noise level and SNR in dB, rt60 in 10ms blocks for AEC path range in [0, 1] */
unsigned char mixer_getNoiseLevel_SNR_rt60(ym_mixer_state *YM_RESTRICT p_mixer, unsigned int aecPath, char *nsLevelDB, char *SNR);
#endif // #ifndef DISABLE_AEC_NLP_MODULE

/* To return the calculated THD value */
unsigned char *mixer_get_input_thdDB(ym_mixer_state *YM_RESTRICT p_mixer);

/* To return the input mic index */
unsigned char *mixer_get_mic_input_index(ym_mixer_state *YM_RESTRICT p_mixer, unsigned int *p_micNum);
/* To return the output speaker index */
unsigned char *mixer_get_speaker_output_index(ym_mixer_state *YM_RESTRICT p_mixer, unsigned int *p_spNum);

/* To return the vad flag in denoise */
int mixer_getCurVad(ym_mixer_state *YM_RESTRICT p_mixer);
/* To return the vad probability in AEC */
float mixer_get_aec_vad_prob(ym_mixer_state *YM_RESTRICT p_mixer);
float mixer_get_aec2_vad_prob(ym_mixer_state *YM_RESTRICT p_mixer);

/* To print the AEC downmix information and mode/delay etc */
void mixer_printAecDmxInfo(ym_mixer_state *p_mixer);

/* To print the automixer gain for specified index */
void mixer_printAmxChannelGain(ym_mixer_state *p_mixer, unsigned int idx);

#ifndef DISABLE_AEC_NLP_MODULE
#if !defined(AEC_AFC_48K) && !defined(AEC_AFC_24K_HIGH_LATENCY)
/* To return the afc reference rms^2 (i.e. mse) for beamforming usage */
float mixerGetAfcRefMSE(ym_mixer_state *YM_RESTRICT p_mixer);
#endif // #if !defined(AEC_AFC_48K) && !defined(AEC_AFC_24K_HIGH_LATENCY)
/* To return the far-end reference rms^2 (i.e. mse) for beamforming usage */
float mixerGetFarRefMSE(ym_mixer_state *YM_RESTRICT p_mixer);
/* To return the speaker reference rms^2 (i.e. mse) for beamforming usage */
float mixerGetSpkRefMSE(ym_mixer_state *YM_RESTRICT p_mixer);
/* Get AEC filter block energy information, return the block energy pointer, the total block energy and the maxi block index and the the total blocks */
float *mixerGetAECFilterBlkEng(ym_mixer_state *YM_RESTRICT p_mixer, unsigned int iChannel, unsigned int *maxiBlkIdx, float *totEng, unsigned int *blkNum, unsigned int *startSample);

#ifndef SPEAKER_CONF_PLATFORM
// update the low shelf filter for the system tuning, return the low shelf filter gain in 0.1dB and the center frequency in p_lfCentFreq
short mixer_updateTuningLowShelfFreq(ym_mixer_state *YM_RESTRICT p_mixer, short *p_lfCentFreq);
#endif // #ifndef SPEAKER_CONF_PLATFORM

unsigned int mixer_getAecMicLowShelfCenterFreq(ym_mixer_state *YM_RESTRICT p_mixer);
// to query the aec low shelf gain in decibel
float mixer_getAecMicLowShelfGainDB(ym_mixer_state *YM_RESTRICT p_mixer);

// to set the low-delay denoise gain used for other modules
void ym_mixer_setLowDelayDsGain(ym_mixer_state *YM_RESTRICT p_mixer, float *p_lowDelayDsGain);

/* To reset the AFC quickly, quickMode could be 0/1/2 */
void ym_mixer_setQuickResetAFC(ym_mixer_state *YM_RESTRICT p_mixer, unsigned int quickMode);

// to get the latest howling frequency
unsigned int ym_mixer_getLatestHowlFreq(ym_mixer_state *YM_RESTRICT p_mixer);
#endif // #ifndef DISABLE_AEC_NLP_MODULE

// to set the low shelf freq/gain/q value
void mixer_aecMicLowShelfBiquadsDesign(ym_mixer_state *YM_RESTRICT p_mixer, unsigned short centerFreq, float dbGain, float Qvalue);

// to set the high shelf level range in [0, 10]
void mixer_aecMicHighShelfBiquadsDesign(ym_mixer_state *YM_RESTRICT p_mixer, unsigned int level);

// to set the AEC ref separate denoise mode, 0 to disable, 1 to enable
void mixer_setAecRefSeparateDenoiseMode(ym_mixer_state *YM_RESTRICT p_mixer, unsigned int enableFlag);

// to set the mixer input buffer ptr, p_in points to the address with the specified channel address, the address should be non-interleave buffer ptr
void mixer_setNonInterleaveInbufPtr(ym_mixer_state *YM_RESTRICT p_mixer, int *YM_RESTRICT p_in[]);

#endif // #ifndef _YM_MIXER_H_
