#ifndef BEAM_H
#define BEAM_H

/* Define direction priority type */
typedef enum SSL_DIR_PRIORITY_TYPE_s
{
    SSL_DIR_SHADOW,         // shadow
    SSL_DIR_FAR_END_SHADOW, // far-end shadow
    SSL_DIR_PRIORITY_0, // priority 0
    SSL_DIR_PRIORITY_1, // priority 1
    SSL_DIR_PRIORITY_2, // priority 2
    SSL_DIR_PRIORITY_3, // priority 3
    SSL_DIR_PRIORITY_4, // priority 4
    SSL_DIR_MAXI_TYPES
} SSL_DIR_PRIORITY_TYPE;

/* Define beam mode type */
typedef enum BEAM_MODE_TYPE_s
{
    BEAM_ALL_CLOSE,     // all close
    BEAM_SSS_CLOSE,     // beamformer close
    BEAM_SSL_CLOSE,     // ssl close
    BEAM_NORMAL,        // normal
    BEAM_MAXI_TYPES
} BEAM_MODE_TYPE;

/* Define beam track type */
typedef enum BEAM_TRACK_TYPE_s
{
    BEAM_ADAPTIVE_MODE,     // adaptive
    BEAM_FIXED_MODE,        // fixed
    BEAM_OUTSIDE_AEC_MODE,  // when using outside aec, no movement during remote signal.
    BEAM_MULTI_FIXED_AUTO_MIXER_MODE,  // multi beam fixed auto-mixer
    BEAM_MULTI_FIXED_DOWN_MIX_MODE,    // multi beam fixed down mix
    BEAM_MULTI_OBJECTIVE_MODE, // multi objective
    BEAM_MULTI_OBJECTIVE_ZERO_MODE, // multi objective far-end zero
    BEAM_MULTI_FIXED_SELECT_MODE, // multi fixed beam select a specified number of mixes
    BEAM_TRACK_MAXI_TYPES
} BEAM_TRACK_TYPE;

#define RETURN_PROC_BUSY_ERROR  (-6)

/* Get the version of the "beam" */
/*
** return --> beam version.
*/
char* beam_query_version(void);

/* Get the memory requirement of the "beam" subroutine */
/*
** return --> could be 0 for no error or any error code above.
*/
int beam_query_mem(unsigned int *p_sta_size, unsigned int *p_sta_ddr_size, unsigned int *p_dyn_size);

/* Initialize Beam Process */
/*
** return --> could be the "beam" subroutine.
*/  
void* beam_open(int frameSize, const void *p_static, const void *p_ddr_static, const void *p_scratch);

/* Beam Process */
/*
** p_ssp: is the "beam" subroutine.
** p_in: is the input signal of speech.
** p_out: is the output signal of speech after denoise.
** p_inStartAddr: the starting address for the input buffer
** return --> could be 0 for no error or any error code above.
*/ 
int beam_process(void* p_beam, const int** p_in, unsigned int inOffset, int** p_out, unsigned int outOffset, int *p_inStartAddr);

/* Set Mic Input Gain */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** iMic: input mic channel index, 0~31 is input mic channel.
** value: input mic gain range in [-400, 60] * 0.1dB
*/
int beam_set_in_gain(void* p_beam, int iMic, short value);

/* Set Beam Output Gain */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** value: output gain range in [-400, 60] * 0.1dB
*/
int beam_set_out_gain(void* p_beam, short value);

/* Set Far End Polar Coordinates */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** theta: vertical angle range in [0, 90] * 1 degree
** phi: horizontal angle range in [0, 360] * 1 degree
*/
int beam_set_far_end_polar(void* p_beam, int theta, int phi);

/* Set Fixed Beam Polar Coordinates */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** theta: vertical angle range in [0, 90] * 1 degree
** phi: horizontal angle range in [0, 360] * 1 degree
*/
int beam_set_fixed_beamformer_polar(void* p_beam, int theta, int phi);

/* Set Adaptive Beam Enable */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
*/
int beam_set_adaptive_beamformer(void* p_beam);

/* Set Outside Aec Beam Polar Coordinates */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** theta: vertical angle range in [0, 90] * 1 degree
** phi: horizontal angle range in [0, 360] * 1 degree
*/
int beam_set_outside_aec_beamformer_polar(void* p_beam, int theta, int phi);

/* Set Beamformer Fixed Distance */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** distance: beamformer distance range in [0, 1500] * 0.01 m
*/
int beam_set_fixed_distance(void* p_beam, int distance);

/* Set Beamformer Adaptive Distance */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
*/
int beam_set_adaptive_distance(void* p_beam);

/* Set Beamformer Outside Aec Distance */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** distance: beamformer distance range in [0, 1500] * 0.01 m
*/
int beam_set_outside_aec_distance(void* p_beam, int distance);

/* Get Sound Source Location Polar Coordinates */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** theta: vertical angle range in [0, 90] * 1 degree
** phi: horizontal angle range in [0, 360] * 1 degree
*/
int beam_get_sound_source_location_polar(void* p_beam, int* theta, int* phi);

/* Get Sound Source Distance */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** distance: sound source distance range in [0, 1500] * 0.01 m
*/
int beam_get_sound_source_location_distance(void* p_beam, int* distance);

/* Get Beamformer Polar Coordinates */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** theta: vertical angle range in [0, 90] * 1 degree
** phi: horizontal angle range in [0, 360] * 1 degree
*/
int beam_get_beamformer_polar(void* p_beam, int* theta, int* phi);

/* Get Beamformer Distance */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** distance: beamformer distance range in [0, 1500] * 0.01 m
*/
int beam_get_beamformer_distance(void* p_beam, int* distance);

/* Set Beam Output Channel Index */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** out0: 0 output channel index, 0~31 is input channel, 32 is beamformer, 33 is model.
** out1: 1 output channel index, 0~31 is input channel, 32 is beamformer, 33 is model.
*/
int beam_set_out_id(void* p_beam, int out0, int out1);

/* Set Beam Mic High */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** micHigh : microphone installation height, range in [0, 1000] * 0.01 m
*/
int beam_set_mic_high(void* p_beam, int micHigh);

/* Set Beam Fixed Beam Info */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** iBeam : index of beam, range in [0, 15]
** enable: 1 is enable, 0 is disable.
** theta: vertical angle range in [0, 90] * 1 degree
** phi: horizontal angle range in [0, 360] * 1 degree
** r: beamformer distance range in [0, 15000] * 0.01 m
** width: beam width range in [0, 5]
** resEnabled : residual suppresssing enable
** resLimitGain: residual suppresssing limit gain range in [0, 400] * -0.1dB
** expander_th: expander threshold range in [0, 400] * -0.1dB
** expander_ratio: expander ratio range in [10, 400] * 0.1
** tuning_range: tuning range in [0, 100] * 0.1m, 0 is disable
*/
int beam_set_fixed_beam_info(void* p_beam,
                             int iBeam,
                             int enabled,
                             int theta,
                             int phi,
                             int r,
                             int width);

/* Get Beam Fixed Beam Info */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** iBeam : index of beam, range in [0, 15]
** enable: 1 is enable, 0 is disable.
** theta: vertical angle range in [0, 90] * 1 degree
** phi: horizontal angle range in [0, 360] * 1 degree
** r: beamformer distance range in [0, 15000] * 0.01 m
** width: beam width range in [0, 5]
** resEnabled : residual suppresssing enable
** resLimitGain: residual suppresssing limit gain range in [0, 400] * -0.1dB
** expander_th: expander threshold range in [0, 400] * -0.1dB
** expander_ratio: expander ratio range in [10, 400] * 0.1
** tuning_range: tuning range in [0, 100] * 0.1m, 0 is disable
*/
int beam_get_fixed_beam_info(void* p_beam,
                             int iBeam,
                             int* enabled,
                             int* theta,
                             int* phi,
                             int* r,
                             int* width,
                             int* resEnabled,
                             int* resLimitGain,
                             int* expander_th,
                             int* expander_ratio,
                             int* tuning_range);

/* Set Beam Fixed Beam Width */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** iBeam : index of beam, range in [0, 15]
** beamWidth: beam width range in [1, 12]
*/
int beam_set_fixed_beam_width(void* p_beam, int iBeam, int beamWidth);

/* Set Area Beam Width */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** iArea : index of area, range in [0, 7], [0, 3] represents a fixed area, with only one beam in the fixed area,
**         and [4, 7] represents a dynamic area where there may be 1-3 beams.
** beamWidth: beam width range in [1, 12]
*/
int beam_set_area_beam_width(void* p_beam, int iArea, int beamWidth);

/* Set Area Beam Width */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** iArea : index of area, range in [0, 7], [0, 3] represents a fixed area, with only one beam in the fixed area,
**         and [4, 7] represents a dynamic area where there may be 1-3 beams.
** tuning_range: tuning range in [0, 100] * 0.1m, 0 is disable
*/
int beam_set_area_tuning_range(void* p_beam, int iArea, int tuning_range);

/* Set Beam Area Bisection Mode */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** mode: 0 is rectangle bisection, 1 is elliptical bisection, default is 0
*/
int beam_set_area_bisection_mode(void* p_beam, int mode);

/* Set Beam Fixed Beam Area XYZ*/
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** iArea : index of area, range in [0, 7], [0, 3] represents a fixed area, with only one beam in the fixed area,
**         and [4, 7] represents a dynamic area where there may be 1-3 beams.
** enable: 1 is enable, 0 is disable.
** xLU: the x-coordinate of the upper left point range in [-2000, 2000] * 0.01 m
** yLU: the y-coordinate of the upper left point range in [-2000, 2000] * 0.01 m
** xRD: the x-coordinate of the bottom right point range in [-2000, 2000] * 0.01 m
** yRD: the y-coordinate of the bottom right point range in [-2000, 2000] * 0.01 m
*/
int beam_set_fixed_beam_area(void* p_beam,
                             int iArea,
                             int enabled,
                             int xLU, int yLU,
                             int xRD, int yRD);

/* Get Beam Fixed Beam Area XYZ*/
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** iArea : index of area, range in [0, 7], [0, 3] represents a fixed area, with only one beam in the fixed area,
**         and [4, 7] represents a dynamic area where there may be 1-3 beams.
** enable: 1 is enable, 0 is disable.
** beamNum: the number of beam, when it is 0, the area is disable.
** beamWidth: beam width range in [1, 12]
** xLU: the x-coordinate of the upper left point range in [-2000, 2000] * 0.01 m
** yLU: the y-coordinate of the upper left point range in [-2000, 2000] * 0.01 m
** xRD: the x-coordinate of the bottom right point range in [-2000, 2000] * 0.01 m
** yRD: the y-coordinate of the bottom right point range in [-2000, 2000] * 0.01 m
*/
int beam_get_fixed_beam_area(void* p_beam,
                             int iArea,
                             int* enabled,
                             int* beamNum,
                             int* width,
                             int* xLU, int* yLU,
                             int* xRD, int* yRD);

/* Get Area The Number Of Beam */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** iArea : index of area, range in [0, 7], [0, 3] represents a fixed area, with only one beam in the fixed area,
**         and [4, 7] represents a dynamic area where there may be 1-3 beams.
** beamNum: the number of beam, when it is 0, the area is disable.
*/
int beam_get_area_beam_number(void* p_beam, int iArea, int* beamNum);

/* Get The Total Number Of Beam */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** beamNum: the total number of beam, when it is 0, the area is disable.
*/
int beam_get_total_beam_number(void* p_beam, int* beamNum);

/* Set Beam Auto Mixer Attack Gain */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** attackMs: automixer attack gain, range in [0, 3000] * 1ms
*/
int beam_set_auto_mixer_attack_gain(void* p_beam, int attackMs);

/* Set Beam Auto Mixer Release Gain */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** releaseMs: automixer release gain, range in [0, 3000] * 1ms
*/
int beam_set_auto_mixer_release_gain(void* p_beam, int releaseMs);

/* Set Beam Auto Mixer In Eng Smooth Mode */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** value: auto mixer input energy smooth factor mode, range in [0, 10], default as 10, the smaller the level, the fast the smooth
*/
int beam_set_autoMixer_InEngSmoothMode(void* p_beam, unsigned int value);

/* Set Beam Auto Mixer Speech Level */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** spLevel: The speech level in 0.1dB unit
*/
int beam_set_autoMixer_SpeechLevel(void* p_beam, int spLevel);

/* Set Beam Auto Mixer Decay */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** iBeam : index of beam, range in [0, 15]
** mixerDecay: automixer decay ratio, range in [0, 100] * -0.5dB
*/
int beam_set_auto_mixer_decay(void* p_beam, int iBeam, unsigned char mixerDecay);

/* Set Area Auto Mixer Decay */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** iArea : index of area, range in [0, 7], [0, 3] represents a fixed area, with only one beam in the fixed area,
**         and [4, 7] represents a dynamic area where there may be 1-3 beams.
** mixerDecay: automixer decay ratio, range in [0, 100] * -0.5dB
*/
int beam_area_set_auto_mixer_decay(void* p_beam, int iArea, unsigned char mixerDecay);
int beam_area_get_auto_mixer_decay(void* p_beam, int iArea, unsigned char* mixerDecay);

/* Set Beam Auto Mixer Decay */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** iBeam : index of beam, range in [0, 15]
** mixerPriority: automixer priority, range in [0, 256]
*/
int beam_set_auto_mixer_priority(void* p_beam, int iBeam, unsigned char mixerPriority);

/* Set Area Auto Mixer Decay */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** iArea : index of area, range in [0, 7], [0, 3] represents a fixed area, with only one beam in the fixed area,
**         and [4, 7] represents a dynamic area where there may be 1-3 beams.
** mixerPriority: automixer priority, range in [0, 256]
*/
int beam_area_set_auto_mixer_priority(void* p_beam, int iArea, unsigned char mixerPriority);

/* Set Beam Auto Mixer Gain */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** iBeam : index of beam, range in [0, 15]
** mixerGain: automixer input gain, range in [-120, 120] * -0.1dB
*/
int beam_set_auto_mixer_gain(void* p_beam, int iBeam, signed char mixerGain);

/* Set Area Auto Mixer Gain */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** iArea : index of area, range in [0, 7], [0, 3] represents a fixed area, with only one beam in the fixed area,
**         and [4, 7] represents a dynamic area where there may be 1-3 beams.
** mixerGain: automixer input gain, range in [-120, 120] * -0.1dB
*/
int beam_area_set_auto_mixer_gain(void* p_beam, int iArea, signed char mixerGain);

/* Print Beam Fixed Beam Info */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
*/
void beam_print_fixed_beam_info(void* p_beam);

/* Print Beam Area */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
*/
void beam_print_area(void* p_beam);

/* Get Direction Priority */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** areaId: index of direction area, range in [0, 31]. area 0 is base area, the scope is the entire space and is always enabled.
** enable: 0 is area disabled, 1 is area enabled.
** priority_type: direction priority type in SSL_DIR_PRIORITY_TYPE
** theta_min: minimum vertical angle range in [0, 90] * 1 degree
** theta_max: maximum vertical angle range in [0, 90] * 1 degree
** phi_min: minimum horizontal angle range in [-180, 540] * 1 degree
** phi_max: maximum horizontal angle range in [-180, 540] * 1 degree
*/
int beam_get_direction_area(void* p_beam,
                            int areaId,
                            int* enable,
                            SSL_DIR_PRIORITY_TYPE* priority_type,
                            int* theta_min,
                            int* theta_max,
                            int* phi_min,
                            int* phi_max);

/* Set Direction Priority */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** areaId: index of direction area, range in [0, 31]. area 0 is base area, the scope is the entire space and is always enabled.
** enable: 0 is area disabled, 1 is area enabled.
** priority_type: direction priority type in SSL_DIR_PRIORITY_TYPE
** theta_min: minimum vertical angle range in [0, 90] * 1 degree
** theta_max: maximum vertical angle range in [0, 90] * 1 degree
** phi_min: minimum horizontal angle range in [-180, 540] * 1 degree
** phi_max: maximum horizontal angle range in [-180, 540] * 1 degree
*/
int beam_set_direction_area(void* p_beam,
                            int areaId,
                            int enable,
                            SSL_DIR_PRIORITY_TYPE priority_type,
                            int theta_min,
                            int theta_max,
                            int phi_min,
                            int phi_max);

/* Set Beam Mode */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** mode: beam mode in BEAM_MODE_TYPE
*/
int beam_set_mode(void* p_beam, BEAM_MODE_TYPE mode);

/* Set Beam Afc Mode */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** mode: 1 is afc, 0 is no afc
*/
int beam_set_afc_mode(void* p_beam, int mode);

/* Set Beam Track Mode */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** mode: beam mode in BEAM_TRACK_TYPE
*/
int beam_set_track_mode(void* p_beam, BEAM_TRACK_TYPE track_mode);

/* Set Beam Mode */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** width: beam width range in [0, 5]
*/
int beam_set_width(void* p_beam, int width);

/* Set Mic Noise Level */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** noise_level: mic noise level rms range in [0, 960] * -0.1 dB
*/
int beam_set_mic_noise_level(void* p_beam, int noise_level);

/* Set Far-End Reference Level */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** reference_level: far-end reference level rms range in [0, 960] * -0.1 dB
*/
int beam_set_far_end_reference_level(void* p_beam, int reference_level);

/* Set Far-End Hold Time */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** time: far-end hold time range in [0, 65535] * 1 ms
*/
int beam_set_far_end_hold_time(void* p_beam, int time);

/* Set Far-End Smooth Factor */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** time: far-end smooth time range in [0, 65535] * 1 ms
*/
int beam_set_far_end_smooth_factor(void* p_beam, int time);

/* Set Multi Objs Release Smooth Factor */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** time: far-end smooth time range in [0, 65535] * 1 ms
*/
int beam_set_multi_objs_release_time(void* p_beam, int time);

/* Set Multi Objs Attack Smooth Factor */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** time: far-end smooth time range in [0, 65535] * 1 ms
*/
int beam_set_multi_objs_attack_time(void* p_beam, int time);

/* Set Far-End Angle Min Step Length */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** angle_min_step: far-end angle min step length range in [0, 36000] * 0.01 degree
*/
int beam_set_far_end_angle_min_step_length(void* p_beam, int angle_min_step);

/* Set Far-End Angle Max Step Length */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** angle_min_step: far-end angle max step length range in [0, 36000] * 0.01 degree
*/
int beam_set_far_end_angle_max_step_length(void* p_beam, int angle_max_step);

/* Set Far-End Voice Flag */
/*
** level: far-end max volume range in [0, 2147483647]
** return: level dB range in [0, 255] * -1dB
*/
unsigned char far_end_reference_to_dB(int level);

/* Set Far-End Voice Flag */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** level: level dB range in [0, 255] * -1dB
*/
int beam_set_far_end_flag(void* p_beam, unsigned char level);

/* Set Far-End Voice Signal */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** p_in: pointer to Far-End Voice Signal
** chNum: number of input channels, range in [1, 2]
** inOffset: offset of pointer
** blkSize: block size
*/
int beam_set_far_end_signal(void* p_beam, const int** p_in, unsigned int chNum, unsigned int inOffset, int blkSize);

/* Set Far-End Voice Binary Flag */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** binary_flag: 1 is far-end voice, 0 is non far-end voice.
*/
int beam_set_far_end_binary_flag(void* p_beam, unsigned char binary_flag);

/* Get Far-End Voice Binary Flag */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** level: level dB range in [0, 255] * -1dB
** binary_flag: 1 is far-end voice, 0 is non far-end voice.
*/
int beam_get_far_end_binary_flag(void* p_beam, unsigned char level, unsigned char* binary_flag);

/* Get Input Channel Volume */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** p_rms_dB: input channel rms volume range in [-160, 0] * 1 dB
*/
int beam_get_ch_vol(void* p_beam, int* p_rms_dB);

/* Get Vad Flag */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** vad_flag: 1 is voice, 0 is non voice
*/
int beam_get_vad_flag(void* p_beam, int* vad_flag);

/* Set Vad Prob */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** vad_prob: 1 is voice, 0 is non voice
*/
int beam_set_vad_prob(void* p_beam, float vad_prob);

/* Get AEC Rapid Changing Flag */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** aec_rapid_changing_flag: 1 is aec rapid changing, 0 is non aec rapid changing
*/
int beam_get_aec_rapid_changing_flag(void* p_beam, int* aec_rapid_changing_flag);

/* Get Sound Source In Area Flag */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** flag: 1 is the first two sound sources within area, 0 is others
*/
int beam_get_ss_in_area_flag(void* p_beam, int* flag);

/* Get Sound Source In Area Flag */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** gain: linear gain for compensating distance
*/
int beam_get_distance_gain(void* p_beam, float* gain);

#define BEAM_EQ
#ifdef BEAM_EQ
/* Define the peq filter type */
typedef enum BEAM_PEQ_FILTER_TYPE_s
{
    BEAM_PEQ_LOWPASS_FILTER,
    BEAM_PEQ_HIGHPASS_FILTER,
    BEAM_PEQ_PEAKEQ_FILTER,
    BEAM_PEQ_LOW_SHELF_FILTER,
    BEAM_PEQ_HIGH_SHELF_FILTER,
    BEAM_PEQ_NOTCH_FILTER,
    BEAM_PEQ_MAXI_FILTER_TYPES
} BEAM_PEQ_FILTER_TYPE;

/* Define the EQ maximum and minimum center frequency in Hz */
#define BEAM_MIN_EQ_CENTER_FREQ  (20)
#define BEAM_MAX_EQ_CENTER_FREQ  (7900)

/* Define the GEQ/PEQ maximum and minimum gain in 0.1dB */
#define BEAM_MIN_EQ_GAIN         (-240)
#define BEAM_MAX_EQ_GAIN         (+240)

#define BEAM_PEQ_MAX_Q_VALUE     20000
#define BEAM_PEQ_MIN_Q_VALUE     20
#define BEAM_PEQ_DEF_Q_VALUE     707
#define BEAM_PEQ_MAX_BANDS_NUM   (8)
#define BEAM_PEQ_MAX_CHAN_NUM    (2)

/* Set the EQ processing channels */
/*
** handle: pointer to the initialized "beam" instance return from beam_open()
** nChannels: total audio channels
*/
int beam_set_peq_channels(void* handle, unsigned int nChannels);

/* Set the EQ processing band numbers */
/*
** handle: pointer to the initialized "beam" instance return from beam_open()
** setValue: range in [1, BEAM_PEQ_MAX_BANDS_NUM]
*/
int beam_set_peq_bandNum(void* handle, unsigned int setValue);

/* Set the EQ processing on or off */
/*
** handle: pointer to the initialized "beam" instance return from beam_open()
** setValue: 0 is off, 1 is on
*/
int beam_set_peq_onOff(void* handle, unsigned int setValue);

/* Set the PEQ filter type */
/*
** handle: pointer to the initialized "beam" instance return from beam_open()
** value: filter type in BEAM_PEQ_FILTER_TYPE
** nBand: the idx of filter bands
*/
int beam_set_peq_filterType(void* handle, short value, unsigned int nBand);

/* Set the PEQ center frequency */
/*
** handle: pointer to the initialized "beam" instance return from beam_open()
** value: center frequency range in [20, 7900] Hz
** nBand: the idx of filter bands
*/
int beam_set_peq_bandFreqs(void* handle, short value, unsigned int nBand);

/* Set the PEQ filter gain */
/*
** handle: pointer to the initialized "beam" instance return from beam_open()
** value: filter gain range in [-240, +240] * 0.1dB
** nBand: the idx of filter bands
*/
int beam_set_peq_bandGains(void* handle, short value, unsigned int nBand);

/* Set the PEQ Quality factor */
/*
** handle: pointer to the initialized "beam" instance return from beam_open()
** value: quality factor range in [20, 20000] * 0.001 degree
** nBand: the idx of filter bands
*/
int beam_set_peq_Qvalue(void* handle, short value, unsigned int nBand);
#endif // #ifdef BEAM_EQ

#define BEAM_MIC_VOLUME_TUNING
#ifdef BEAM_MIC_VOLUME_TUNING
/* Get Mic Tuning Noise */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** p_noise: mic tuning noise, unit is 0.1 dB
*/
int beam_get_mic_tuning_noise(void* p_beam, int* p_noise);
/* Get Mic Tuning Volume And Gain */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** p_volume: mic tuning volume, unit is 0.1 dB
** p_gain: mic tuning gain, unit is 0.1 dB
*/
int beam_get_mic_tuning_volume_gain(void* p_beam, int* p_volume, int* p_gain);

/* Get Mic Error Flag */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** mic_error_flag: 0 is no error, others is mic error
*/
int beam_get_mic_error_flag(void* p_beam, int* mic_error_flag);
#endif // #ifdef BEAM_MIC_VOLUME_TUNING

/* Get Beam Run Error Location */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** run_error_id: index of running status
*/
int beam_get_run_error_location(void* p_beam, int* run_error_id);

/* Get Beam Low Delay Ds Gain Pointer */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** return: low delay ds gain pointer
*/
float* beam_get_lowDelayDsGain(void* p_beam);

/* Get Beam Low Delay Ds Gain On Off */
/*
** p_beam: pointer to the initialized "beam" instance return from beam_open()
** flag: 0 is Off, other is On
*/
int beam_set_lowDelayDsGain_on_off(void* p_beam, int flag);

#endif // #ifndef BEAM_H
