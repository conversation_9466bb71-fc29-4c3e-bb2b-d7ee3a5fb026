/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-09-06 10:13:18
 * @LastEditTime: 2024-10-22 18:26:45
 * @LastEditors: Anzhichao
 * @Description:
 * @FilePath: /audio_mixer_rk3308/include/ym_mixer_param_index.h
 * Copyright (c) 2006-2024, Yinkman Development Team
 */
#ifndef __YM_MIXER_PARAM_ms_index__H__
#define __YM_MIXER_PARAM_ms_index__H__

typedef enum
{
    cpu_load_ms_index,

    mixer_input_channel1_sig_db_ms_unit = 24,
    mixer_input_channel1_sig_db_ms_index = cpu_load_ms_index + 1, // mixer max sig level,DB
    mixer_input_channel2_sig_db_ms_index,
    mixer_input_channel3_sig_db_ms_index,
    mixer_input_channel4_sig_db_ms_index,
    mixer_input_channel5_sig_db_ms_index,
    mixer_input_channel6_sig_db_ms_index,
    mixer_input_channel7_sig_db_ms_index,
    mixer_input_channel8_sig_db_ms_index,
    mixer_input_channel9_sig_db_ms_index,
    mixer_input_channel10_sig_db_ms_index,
    mixer_input_channel11_sig_db_ms_index,
    mixer_input_channel12_sig_db_ms_index,
    mixer_input_channel13_sig_db_ms_index,
    mixer_input_channel14_sig_db_ms_index,
    mixer_input_channel15_sig_db_ms_index,
    mixer_input_channel16_sig_db_ms_index,
    mixer_input_channel17_sig_db_ms_index,
    mixer_input_channel18_sig_db_ms_index,
    mixer_input_channel19_sig_db_ms_index,
    mixer_input_channel20_sig_db_ms_index,
    mixer_input_channel21_sig_db_ms_index,
    mixer_input_channel22_sig_db_ms_index,
    mixer_input_channel23_sig_db_ms_index,
    mixer_input_channel24_sig_db_ms_index,
    mixer_input_channel25_sig_db_ms_index,
    mixer_input_channel26_sig_db_ms_index,
    mixer_input_channel27_sig_db_ms_index,
    mixer_input_channel28_sig_db_ms_index,
    mixer_input_channel29_sig_db_ms_index,
    mixer_input_channel30_sig_db_ms_index,
    mixer_input_channel31_sig_db_ms_index,
    mixer_input_channel32_sig_db_ms_index,

    mixer_output_channel1_sig_db_ms_unit = 16,
    mixer_output_channel1_sig_db_ms_index = mixer_input_channel32_sig_db_ms_index + 1,
    mixer_output_channel2_sig_db_ms_index,
    mixer_output_channel3_sig_db_ms_index,
    mixer_output_channel4_sig_db_ms_index,
    mixer_output_channel5_sig_db_ms_index,
    mixer_output_channel6_sig_db_ms_index,
    mixer_output_channel7_sig_db_ms_index,
    mixer_output_channel8_sig_db_ms_index,
    mixer_output_channel9_sig_db_ms_index,
    mixer_output_channel10_sig_db_ms_index,
    mixer_output_channel11_sig_db_ms_index,
    mixer_output_channel12_sig_db_ms_index,
    mixer_output_channel13_sig_db_ms_index,
    mixer_output_channel14_sig_db_ms_index,
    mixer_output_channel15_sig_db_ms_index,
    mixer_output_channel16_sig_db_ms_index,

    output_channel_vol1_ms_index,
    output_channel_vol2_ms_index,
    output_channel_vol3_ms_index,
    output_channel_vol4_ms_index,
    output_channel_vol5_ms_index,
    output_channel_vol6_ms_index,
    output_channel_vol7_ms_index,
    output_channel_vol8_ms_index,
    output_channel_vol9_ms_index,
    output_channel_vol10_ms_index,
    output_channel_vol11_ms_index,
    output_channel_vol12_ms_index,
    output_channel_vol13_ms_index,
    output_channel_vol14_ms_index,
    output_channel_vol15_ms_index,
    output_channel_vol16_ms_index,

    // 通道aec类型，0：无 AEC，1：AEC0通道，2：AEC1通道，3：AEC参考，4：直通
    input_channel1_aec_mode_ms_index,
    input_channel2_aec_mode_ms_index,
    input_channel3_aec_mode_ms_index,
    input_channel4_aec_mode_ms_index,
    input_channel5_aec_mode_ms_index,
    input_channel6_aec_mode_ms_index,
    input_channel7_aec_mode_ms_index,
    input_channel8_aec_mode_ms_index,
    input_channel9_aec_mode_ms_index,
    input_channel10_aec_mode_ms_index,
    input_channel11_aec_mode_ms_index,
    input_channel12_aec_mode_ms_index,
    input_channel13_aec_mode_ms_index,
    input_channel14_aec_mode_ms_index,
    input_channel15_aec_mode_ms_index,
    input_channel16_aec_mode_ms_index,
    input_channel17_aec_mode_ms_index,
    input_channel18_aec_mode_ms_index,
    input_channel19_aec_mode_ms_index,
    input_channel20_aec_mode_ms_index,
    input_channel21_aec_mode_ms_index,
    input_channel22_aec_mode_ms_index,
    input_channel23_aec_mode_ms_index,
    input_channel24_aec_mode_ms_index,
    input_channel25_aec_mode_ms_index,
    input_channel26_aec_mode_ms_index,
    input_channel27_aec_mode_ms_index,
    input_channel28_aec_mode_ms_index,
    input_channel29_aec_mode_ms_index,
    input_channel30_aec_mode_ms_index,
    input_channel31_aec_mode_ms_index,
    input_channel32_aec_mode_ms_index,

    output_channel1_mute_ms_index,
    output_channel2_mute_ms_index,
    output_channel3_mute_ms_index,
    output_channel4_mute_ms_index,
    output_channel5_mute_ms_index,
    output_channel6_mute_ms_index,
    output_channel7_mute_ms_index,
    output_channel8_mute_ms_index,
    output_channel9_mute_ms_index,
    output_channel10_mute_ms_index,
    output_channel11_mute_ms_index,
    output_channel12_mute_ms_index,
    output_channel13_mute_ms_index,
    output_channel14_mute_ms_index,
    output_channel15_mute_ms_index,
    output_channel16_mute_ms_index,

    // 通道自动混音 [-100 27]dB
    input_channel1_atm_mode_ms_index,
    input_channel2_atm_mode_ms_index,
    input_channel3_atm_mode_ms_index,
    input_channel4_atm_mode_ms_index,
    input_channel5_atm_mode_ms_index,
    input_channel6_atm_mode_ms_index,
    input_channel7_atm_mode_ms_index,
    input_channel8_atm_mode_ms_index,
    input_channel9_atm_mode_ms_index,
    input_channel10_atm_mode_ms_index,
    input_channel11_atm_mode_ms_index,
    input_channel12_atm_mode_ms_index,
    input_channel13_atm_mode_ms_index,
    input_channel14_atm_mode_ms_index,
    input_channel15_atm_mode_ms_index,
    input_channel16_atm_mode_ms_index,
    input_channel17_atm_mode_ms_index,
    input_channel18_atm_mode_ms_index,
    input_channel19_atm_mode_ms_index,
    input_channel20_atm_mode_ms_index,
    input_channel21_atm_mode_ms_index,
    input_channel22_atm_mode_ms_index,
    input_channel23_atm_mode_ms_index,
    input_channel24_atm_mode_ms_index,
    input_channel25_atm_mode_ms_index,
    input_channel26_atm_mode_ms_index,
    input_channel27_atm_mode_ms_index,
    input_channel28_atm_mode_ms_index,
    input_channel29_atm_mode_ms_index,
    input_channel30_atm_mode_ms_index,
    input_channel31_atm_mode_ms_index,
    input_channel32_atm_mode_ms_index,

    input_channel1_atm_decay_ms_index,
    input_channel2_atm_decay_ms_index,
    input_channel3_atm_decay_ms_index,
    input_channel4_atm_decay_ms_index,
    input_channel5_atm_decay_ms_index,
    input_channel6_atm_decay_ms_index,
    input_channel7_atm_decay_ms_index,
    input_channel8_atm_decay_ms_index,
    input_channel9_atm_decay_ms_index,
    input_channel10_atm_decay_ms_index,
    input_channel11_atm_decay_ms_index,
    input_channel12_atm_decay_ms_index,
    input_channel13_atm_decay_ms_index,
    input_channel14_atm_decay_ms_index,
    input_channel15_atm_decay_ms_index,
    input_channel16_atm_decay_ms_index,
    input_channel17_atm_decay_ms_index,
    input_channel18_atm_decay_ms_index,
    input_channel19_atm_decay_ms_index,
    input_channel20_atm_decay_ms_index,
    input_channel21_atm_decay_ms_index,
    input_channel22_atm_decay_ms_index,
    input_channel23_atm_decay_ms_index,
    input_channel24_atm_decay_ms_index,
    input_channel25_atm_decay_ms_index,
    input_channel26_atm_decay_ms_index,
    input_channel27_atm_decay_ms_index,
    input_channel28_atm_decay_ms_index,
    input_channel29_atm_decay_ms_index,
    input_channel30_atm_decay_ms_index,
    input_channel31_atm_decay_ms_index,
    input_channel32_atm_decay_ms_index,

    input_channel1_delay_ms_index,
    input_channel2_delay_ms_index,
    input_channel3_delay_ms_index,
    input_channel4_delay_ms_index,
    input_channel5_delay_ms_index,
    input_channel6_delay_ms_index,
    input_channel7_delay_ms_index,
    input_channel8_delay_ms_index,
    input_channel9_delay_ms_index,
    input_channel10_delay_ms_index,
    input_channel11_delay_ms_index,
    input_channel12_delay_ms_index,
    input_channel13_delay_ms_index,
    input_channel14_delay_ms_index,
    input_channel15_delay_ms_index,
    input_channel16_delay_ms_index,
    input_channel17_delay_ms_index,
    input_channel18_delay_ms_index,
    input_channel19_delay_ms_index,
    input_channel20_delay_ms_index,
    input_channel21_delay_ms_index,
    input_channel22_delay_ms_index,
    input_channel23_delay_ms_index,
    input_channel24_delay_ms_index,
    input_channel25_delay_ms_index,
    input_channel26_delay_ms_index,
    input_channel27_delay_ms_index,
    input_channel28_delay_ms_index,
    input_channel29_delay_ms_index,
    input_channel30_delay_ms_index,
    input_channel31_delay_ms_index,
    input_channel32_delay_ms_index,

    output_channel1_delay_ms_index,
    output_channel2_delay_ms_index,
    output_channel3_delay_ms_index,
    output_channel4_delay_ms_index,
    output_channel5_delay_ms_index,
    output_channel6_delay_ms_index,
    output_channel7_delay_ms_index,
    output_channel8_delay_ms_index,
    output_channel9_delay_ms_index,
    output_channel10_delay_ms_index,
    output_channel11_delay_ms_index,
    output_channel12_delay_ms_index,
    output_channel13_delay_ms_index,
    output_channel14_delay_ms_index,
    output_channel15_delay_ms_index,
    output_channel16_delay_ms_index,

    // 静音矩阵，行为输入通道，列为输出通道，1表示静音
    /*
     * | Line1 | Line2 | Line3 | Line4 | USB L | USB R | BP  | BFM1 | BFM2 | BFM3 | BFM4 | BFM5 | AEC | AFC |       |
     * | ----- | ----- | ----- | ----- | ----- | ----- | --- | ---- | ---- | ---- | ---- | ---- | --- | --- | ----- |
     * |       |       |       |       |       |       |     |      |      |      |      |      |     |     | SP1 L |
     * |       |       |       |       |       |       |     |      |      |      |      |      |     |     | SP1 R |
     * |       |       |       |       |       |       |     |      |      |      |      |      |     |     | SP2 L |
     * |       |       |       |       |       |       |     |      |      |      |      |      |     |     | SP2 R |
     * |       |       |       |       |       |       |     |      |      |      |      |      |     |     | Line1 |
     * |       |       |       |       |       |       |     |      |      |      |      |      |     |     | Line2 |
     * |       |       |       |       |       |       |     |      |      |      |      |      |     |     | Line3 |
     * |       |       |       |       |       |       |     |      |      |      |      |      |     |     | Line4 |
     * |       |       |       |       |       |       |     |      |      |      |      |      |     |     | USB L |
     * |       |       |       |       |       |       |     |      |      |      |      |      |     |     | USB R |
     */
    ochn1_ichn_mute_ms_index,
    ochn2_ichn_mute_ms_index,
    ochn3_ichn_mute_ms_index,
    ochn4_ichn_mute_ms_index,
    ochn5_ichn_mute_ms_index,
    ochn6_ichn_mute_ms_index,
    ochn7_ichn_mute_ms_index,
    ochn8_ichn_mute_ms_index,
    ochn9_ichn_mute_ms_index,
    ochn10_ichn_mute_ms_index,
    ochn11_ichn_mute_ms_index,
    ochn12_ichn_mute_ms_index,
    ochn13_ichn_mute_ms_index,
    ochn14_ichn_mute_ms_index,
    ochn15_ichn_mute_ms_index,
    ochn16_ichn_mute_ms_index,

    // mixer通道音量
    ochn1_ichn1_vol_unit_count = 24,
    ochn1_ichn1_vol_ms_index = ochn16_ichn_mute_ms_index + 1,
    ochn1_ichn2_vol_ms_index,
    ochn1_ichn3_vol_ms_index,
    ochn1_ichn4_vol_ms_index,
    ochn1_ichn5_vol_ms_index,
    ochn1_ichn6_vol_ms_index,
    ochn1_ichn7_vol_ms_index,
    ochn1_ichn8_vol_ms_index,
    ochn1_ichn9_vol_ms_index,
    ochn1_ichn10_vol_ms_index,
    ochn1_ichn11_vol_ms_index,
    ochn1_ichn12_vol_ms_index,
    ochn1_ichn13_vol_ms_index,
    ochn1_ichn14_vol_ms_index,
    ochn1_ichn15_vol_ms_index,
    ochn1_ichn16_vol_ms_index,
    ochn1_ichn17_vol_ms_index,
    ochn1_ichn18_vol_ms_index,
    ochn1_ichn19_vol_ms_index,
    ochn1_ichn20_vol_ms_index,
    ochn1_ichn21_vol_ms_index,
    ochn1_ichn22_vol_ms_index,
    ochn1_ichn23_vol_ms_index,
    ochn1_ichn24_vol_ms_index,
    ochn1_ichn25_vol_ms_index,
    ochn1_ichn26_vol_ms_index,
    ochn1_ichn27_vol_ms_index,
    ochn1_ichn28_vol_ms_index,
    ochn1_ichn29_vol_ms_index,
    ochn1_ichn30_vol_ms_index,
    ochn1_ichn31_vol_ms_index,
    ochn1_ichn32_vol_ms_index,

    ochn2_ichn1_vol_ms_index,
    ochn2_ichn2_vol_ms_index,
    ochn2_ichn3_vol_ms_index,
    ochn2_ichn4_vol_ms_index,
    ochn2_ichn5_vol_ms_index,
    ochn2_ichn6_vol_ms_index,
    ochn2_ichn7_vol_ms_index,
    ochn2_ichn8_vol_ms_index,
    ochn2_ichn9_vol_ms_index,
    ochn2_ichn10_vol_ms_index,
    ochn2_ichn11_vol_ms_index,
    ochn2_ichn12_vol_ms_index,
    ochn2_ichn13_vol_ms_index,
    ochn2_ichn14_vol_ms_index,
    ochn2_ichn15_vol_ms_index,
    ochn2_ichn16_vol_ms_index,
    ochn2_ichn17_vol_ms_index,
    ochn2_ichn18_vol_ms_index,
    ochn2_ichn19_vol_ms_index,
    ochn2_ichn20_vol_ms_index,
    ochn2_ichn21_vol_ms_index,
    ochn2_ichn22_vol_ms_index,
    ochn2_ichn23_vol_ms_index,
    ochn2_ichn24_vol_ms_index,
    ochn2_ichn25_vol_ms_index,
    ochn2_ichn26_vol_ms_index,
    ochn2_ichn27_vol_ms_index,
    ochn2_ichn28_vol_ms_index,
    ochn2_ichn29_vol_ms_index,
    ochn2_ichn30_vol_ms_index,
    ochn2_ichn31_vol_ms_index,
    ochn2_ichn32_vol_ms_index,

    ochn3_ichn1_vol_ms_index,
    ochn3_ichn2_vol_ms_index,
    ochn3_ichn3_vol_ms_index,
    ochn3_ichn4_vol_ms_index,
    ochn3_ichn5_vol_ms_index,
    ochn3_ichn6_vol_ms_index,
    ochn3_ichn7_vol_ms_index,
    ochn3_ichn8_vol_ms_index,
    ochn3_ichn9_vol_ms_index,
    ochn3_ichn10_vol_ms_index,
    ochn3_ichn11_vol_ms_index,
    ochn3_ichn12_vol_ms_index,
    ochn3_ichn13_vol_ms_index,
    ochn3_ichn14_vol_ms_index,
    ochn3_ichn15_vol_ms_index,
    ochn3_ichn16_vol_ms_index,
    ochn3_ichn17_vol_ms_index,
    ochn3_ichn18_vol_ms_index,
    ochn3_ichn19_vol_ms_index,
    ochn3_ichn20_vol_ms_index,
    ochn3_ichn21_vol_ms_index,
    ochn3_ichn22_vol_ms_index,
    ochn3_ichn23_vol_ms_index,
    ochn3_ichn24_vol_ms_index,
    ochn3_ichn25_vol_ms_index,
    ochn3_ichn26_vol_ms_index,
    ochn3_ichn27_vol_ms_index,
    ochn3_ichn28_vol_ms_index,
    ochn3_ichn29_vol_ms_index,
    ochn3_ichn30_vol_ms_index,
    ochn3_ichn31_vol_ms_index,
    ochn3_ichn32_vol_ms_index,

    ochn4_ichn1_vol_ms_index,
    ochn4_ichn2_vol_ms_index,
    ochn4_ichn3_vol_ms_index,
    ochn4_ichn4_vol_ms_index,
    ochn4_ichn5_vol_ms_index,
    ochn4_ichn6_vol_ms_index,
    ochn4_ichn7_vol_ms_index,
    ochn4_ichn8_vol_ms_index,
    ochn4_ichn9_vol_ms_index,
    ochn4_ichn10_vol_ms_index,
    ochn4_ichn11_vol_ms_index,
    ochn4_ichn12_vol_ms_index,
    ochn4_ichn13_vol_ms_index,
    ochn4_ichn14_vol_ms_index,
    ochn4_ichn15_vol_ms_index,
    ochn4_ichn16_vol_ms_index,
    ochn4_ichn17_vol_ms_index,
    ochn4_ichn18_vol_ms_index,
    ochn4_ichn19_vol_ms_index,
    ochn4_ichn20_vol_ms_index,
    ochn4_ichn21_vol_ms_index,
    ochn4_ichn22_vol_ms_index,
    ochn4_ichn23_vol_ms_index,
    ochn4_ichn24_vol_ms_index,
    ochn4_ichn25_vol_ms_index,
    ochn4_ichn26_vol_ms_index,
    ochn4_ichn27_vol_ms_index,
    ochn4_ichn28_vol_ms_index,
    ochn4_ichn29_vol_ms_index,
    ochn4_ichn30_vol_ms_index,
    ochn4_ichn31_vol_ms_index,
    ochn4_ichn32_vol_ms_index,

    ochn5_ichn1_vol_ms_index,
    ochn5_ichn2_vol_ms_index,
    ochn5_ichn3_vol_ms_index,
    ochn5_ichn4_vol_ms_index,
    ochn5_ichn5_vol_ms_index,
    ochn5_ichn6_vol_ms_index,
    ochn5_ichn7_vol_ms_index,
    ochn5_ichn8_vol_ms_index,
    ochn5_ichn9_vol_ms_index,
    ochn5_ichn10_vol_ms_index,
    ochn5_ichn11_vol_ms_index,
    ochn5_ichn12_vol_ms_index,
    ochn5_ichn13_vol_ms_index,
    ochn5_ichn14_vol_ms_index,
    ochn5_ichn15_vol_ms_index,
    ochn5_ichn16_vol_ms_index,
    ochn5_ichn17_vol_ms_index,
    ochn5_ichn18_vol_ms_index,
    ochn5_ichn19_vol_ms_index,
    ochn5_ichn20_vol_ms_index,
    ochn5_ichn21_vol_ms_index,
    ochn5_ichn22_vol_ms_index,
    ochn5_ichn23_vol_ms_index,
    ochn5_ichn24_vol_ms_index,
    ochn5_ichn25_vol_ms_index,
    ochn5_ichn26_vol_ms_index,
    ochn5_ichn27_vol_ms_index,
    ochn5_ichn28_vol_ms_index,
    ochn5_ichn29_vol_ms_index,
    ochn5_ichn30_vol_ms_index,
    ochn5_ichn31_vol_ms_index,
    ochn5_ichn32_vol_ms_index,

    ochn6_ichn1_vol_ms_index,
    ochn6_ichn2_vol_ms_index,
    ochn6_ichn3_vol_ms_index,
    ochn6_ichn4_vol_ms_index,
    ochn6_ichn5_vol_ms_index,
    ochn6_ichn6_vol_ms_index,
    ochn6_ichn7_vol_ms_index,
    ochn6_ichn8_vol_ms_index,
    ochn6_ichn9_vol_ms_index,
    ochn6_ichn10_vol_ms_index,
    ochn6_ichn11_vol_ms_index,
    ochn6_ichn12_vol_ms_index,
    ochn6_ichn13_vol_ms_index,
    ochn6_ichn14_vol_ms_index,
    ochn6_ichn15_vol_ms_index,
    ochn6_ichn16_vol_ms_index,
    ochn6_ichn17_vol_ms_index,
    ochn6_ichn18_vol_ms_index,
    ochn6_ichn19_vol_ms_index,
    ochn6_ichn20_vol_ms_index,
    ochn6_ichn21_vol_ms_index,
    ochn6_ichn22_vol_ms_index,
    ochn6_ichn23_vol_ms_index,
    ochn6_ichn24_vol_ms_index,
    ochn6_ichn25_vol_ms_index,
    ochn6_ichn26_vol_ms_index,
    ochn6_ichn27_vol_ms_index,
    ochn6_ichn28_vol_ms_index,
    ochn6_ichn29_vol_ms_index,
    ochn6_ichn30_vol_ms_index,
    ochn6_ichn31_vol_ms_index,
    ochn6_ichn32_vol_ms_index,

    ochn7_ichn1_vol_ms_index,
    ochn7_ichn2_vol_ms_index,
    ochn7_ichn3_vol_ms_index,
    ochn7_ichn4_vol_ms_index,
    ochn7_ichn5_vol_ms_index,
    ochn7_ichn6_vol_ms_index,
    ochn7_ichn7_vol_ms_index,
    ochn7_ichn8_vol_ms_index,
    ochn7_ichn9_vol_ms_index,
    ochn7_ichn10_vol_ms_index,
    ochn7_ichn11_vol_ms_index,
    ochn7_ichn12_vol_ms_index,
    ochn7_ichn13_vol_ms_index,
    ochn7_ichn14_vol_ms_index,
    ochn7_ichn15_vol_ms_index,
    ochn7_ichn16_vol_ms_index,
    ochn7_ichn17_vol_ms_index,
    ochn7_ichn18_vol_ms_index,
    ochn7_ichn19_vol_ms_index,
    ochn7_ichn20_vol_ms_index,
    ochn7_ichn21_vol_ms_index,
    ochn7_ichn22_vol_ms_index,
    ochn7_ichn23_vol_ms_index,
    ochn7_ichn24_vol_ms_index,
    ochn7_ichn25_vol_ms_index,
    ochn7_ichn26_vol_ms_index,
    ochn7_ichn27_vol_ms_index,
    ochn7_ichn28_vol_ms_index,
    ochn7_ichn29_vol_ms_index,
    ochn7_ichn30_vol_ms_index,
    ochn7_ichn31_vol_ms_index,
    ochn7_ichn32_vol_ms_index,

    ochn8_ichn1_vol_ms_index,
    ochn8_ichn2_vol_ms_index,
    ochn8_ichn3_vol_ms_index,
    ochn8_ichn4_vol_ms_index,
    ochn8_ichn5_vol_ms_index,
    ochn8_ichn6_vol_ms_index,
    ochn8_ichn7_vol_ms_index,
    ochn8_ichn8_vol_ms_index,
    ochn8_ichn9_vol_ms_index,
    ochn8_ichn10_vol_ms_index,
    ochn8_ichn11_vol_ms_index,
    ochn8_ichn12_vol_ms_index,
    ochn8_ichn13_vol_ms_index,
    ochn8_ichn14_vol_ms_index,
    ochn8_ichn15_vol_ms_index,
    ochn8_ichn16_vol_ms_index,
    ochn8_ichn17_vol_ms_index,
    ochn8_ichn18_vol_ms_index,
    ochn8_ichn19_vol_ms_index,
    ochn8_ichn20_vol_ms_index,
    ochn8_ichn21_vol_ms_index,
    ochn8_ichn22_vol_ms_index,
    ochn8_ichn23_vol_ms_index,
    ochn8_ichn24_vol_ms_index,
    ochn8_ichn25_vol_ms_index,
    ochn8_ichn26_vol_ms_index,
    ochn8_ichn27_vol_ms_index,
    ochn8_ichn28_vol_ms_index,
    ochn8_ichn29_vol_ms_index,
    ochn8_ichn30_vol_ms_index,
    ochn8_ichn31_vol_ms_index,
    ochn8_ichn32_vol_ms_index,

    ochn9_ichn1_vol_ms_index,
    ochn9_ichn2_vol_ms_index,
    ochn9_ichn3_vol_ms_index,
    ochn9_ichn4_vol_ms_index,
    ochn9_ichn5_vol_ms_index,
    ochn9_ichn6_vol_ms_index,
    ochn9_ichn7_vol_ms_index,
    ochn9_ichn8_vol_ms_index,
    ochn9_ichn9_vol_ms_index,
    ochn9_ichn10_vol_ms_index,
    ochn9_ichn11_vol_ms_index,
    ochn9_ichn12_vol_ms_index,
    ochn9_ichn13_vol_ms_index,
    ochn9_ichn14_vol_ms_index,
    ochn9_ichn15_vol_ms_index,
    ochn9_ichn16_vol_ms_index,
    ochn9_ichn17_vol_ms_index,
    ochn9_ichn18_vol_ms_index,
    ochn9_ichn19_vol_ms_index,
    ochn9_ichn20_vol_ms_index,
    ochn9_ichn21_vol_ms_index,
    ochn9_ichn22_vol_ms_index,
    ochn9_ichn23_vol_ms_index,
    ochn9_ichn24_vol_ms_index,
    ochn9_ichn25_vol_ms_index,
    ochn9_ichn26_vol_ms_index,
    ochn9_ichn27_vol_ms_index,
    ochn9_ichn28_vol_ms_index,
    ochn9_ichn29_vol_ms_index,
    ochn9_ichn30_vol_ms_index,
    ochn9_ichn31_vol_ms_index,
    ochn9_ichn32_vol_ms_index,

    ochn10_ichn1_vol_ms_index,
    ochn10_ichn2_vol_ms_index,
    ochn10_ichn3_vol_ms_index,
    ochn10_ichn4_vol_ms_index,
    ochn10_ichn5_vol_ms_index,
    ochn10_ichn6_vol_ms_index,
    ochn10_ichn7_vol_ms_index,
    ochn10_ichn8_vol_ms_index,
    ochn10_ichn9_vol_ms_index,
    ochn10_ichn10_vol_ms_index,
    ochn10_ichn11_vol_ms_index,
    ochn10_ichn12_vol_ms_index,
    ochn10_ichn13_vol_ms_index,
    ochn10_ichn14_vol_ms_index,
    ochn10_ichn15_vol_ms_index,
    ochn10_ichn16_vol_ms_index,
    ochn10_ichn17_vol_ms_index,
    ochn10_ichn18_vol_ms_index,
    ochn10_ichn19_vol_ms_index,
    ochn10_ichn20_vol_ms_index,
    ochn10_ichn21_vol_ms_index,
    ochn10_ichn22_vol_ms_index,
    ochn10_ichn23_vol_ms_index,
    ochn10_ichn24_vol_ms_index,
    ochn10_ichn25_vol_ms_index,
    ochn10_ichn26_vol_ms_index,
    ochn10_ichn27_vol_ms_index,
    ochn10_ichn28_vol_ms_index,
    ochn10_ichn29_vol_ms_index,
    ochn10_ichn30_vol_ms_index,
    ochn10_ichn31_vol_ms_index,
    ochn10_ichn32_vol_ms_index,

    ochn11_ichn1_vol_ms_index,
    ochn11_ichn2_vol_ms_index,
    ochn11_ichn3_vol_ms_index,
    ochn11_ichn4_vol_ms_index,
    ochn11_ichn5_vol_ms_index,
    ochn11_ichn6_vol_ms_index,
    ochn11_ichn7_vol_ms_index,
    ochn11_ichn8_vol_ms_index,
    ochn11_ichn9_vol_ms_index,
    ochn11_ichn10_vol_ms_index,
    ochn11_ichn11_vol_ms_index,
    ochn11_ichn12_vol_ms_index,
    ochn11_ichn13_vol_ms_index,
    ochn11_ichn14_vol_ms_index,
    ochn11_ichn15_vol_ms_index,
    ochn11_ichn16_vol_ms_index,
    ochn11_ichn17_vol_ms_index,
    ochn11_ichn18_vol_ms_index,
    ochn11_ichn19_vol_ms_index,
    ochn11_ichn20_vol_ms_index,
    ochn11_ichn21_vol_ms_index,
    ochn11_ichn22_vol_ms_index,
    ochn11_ichn23_vol_ms_index,
    ochn11_ichn24_vol_ms_index,
    ochn11_ichn25_vol_ms_index,
    ochn11_ichn26_vol_ms_index,
    ochn11_ichn27_vol_ms_index,
    ochn11_ichn28_vol_ms_index,
    ochn11_ichn29_vol_ms_index,
    ochn11_ichn30_vol_ms_index,
    ochn11_ichn31_vol_ms_index,
    ochn11_ichn32_vol_ms_index,

    ochn12_ichn1_vol_ms_index,
    ochn12_ichn2_vol_ms_index,
    ochn12_ichn3_vol_ms_index,
    ochn12_ichn4_vol_ms_index,
    ochn12_ichn5_vol_ms_index,
    ochn12_ichn6_vol_ms_index,
    ochn12_ichn7_vol_ms_index,
    ochn12_ichn8_vol_ms_index,
    ochn12_ichn9_vol_ms_index,
    ochn12_ichn10_vol_ms_index,
    ochn12_ichn11_vol_ms_index,
    ochn12_ichn12_vol_ms_index,
    ochn12_ichn13_vol_ms_index,
    ochn12_ichn14_vol_ms_index,
    ochn12_ichn15_vol_ms_index,
    ochn12_ichn16_vol_ms_index,
    ochn12_ichn17_vol_ms_index,
    ochn12_ichn18_vol_ms_index,
    ochn12_ichn19_vol_ms_index,
    ochn12_ichn20_vol_ms_index,
    ochn12_ichn21_vol_ms_index,
    ochn12_ichn22_vol_ms_index,
    ochn12_ichn23_vol_ms_index,
    ochn12_ichn24_vol_ms_index,
    ochn12_ichn25_vol_ms_index,
    ochn12_ichn26_vol_ms_index,
    ochn12_ichn27_vol_ms_index,
    ochn12_ichn28_vol_ms_index,
    ochn12_ichn29_vol_ms_index,
    ochn12_ichn30_vol_ms_index,
    ochn12_ichn31_vol_ms_index,
    ochn12_ichn32_vol_ms_index,

    ochn13_ichn1_vol_ms_index,
    ochn13_ichn2_vol_ms_index,
    ochn13_ichn3_vol_ms_index,
    ochn13_ichn4_vol_ms_index,
    ochn13_ichn5_vol_ms_index,
    ochn13_ichn6_vol_ms_index,
    ochn13_ichn7_vol_ms_index,
    ochn13_ichn8_vol_ms_index,
    ochn13_ichn9_vol_ms_index,
    ochn13_ichn10_vol_ms_index,
    ochn13_ichn11_vol_ms_index,
    ochn13_ichn12_vol_ms_index,
    ochn13_ichn13_vol_ms_index,
    ochn13_ichn14_vol_ms_index,
    ochn13_ichn15_vol_ms_index,
    ochn13_ichn16_vol_ms_index,
    ochn13_ichn17_vol_ms_index,
    ochn13_ichn18_vol_ms_index,
    ochn13_ichn19_vol_ms_index,
    ochn13_ichn20_vol_ms_index,
    ochn13_ichn21_vol_ms_index,
    ochn13_ichn22_vol_ms_index,
    ochn13_ichn23_vol_ms_index,
    ochn13_ichn24_vol_ms_index,
    ochn13_ichn25_vol_ms_index,
    ochn13_ichn26_vol_ms_index,
    ochn13_ichn27_vol_ms_index,
    ochn13_ichn28_vol_ms_index,
    ochn13_ichn29_vol_ms_index,
    ochn13_ichn30_vol_ms_index,
    ochn13_ichn31_vol_ms_index,
    ochn13_ichn32_vol_ms_index,

    ochn14_ichn1_vol_ms_index,
    ochn14_ichn2_vol_ms_index,
    ochn14_ichn3_vol_ms_index,
    ochn14_ichn4_vol_ms_index,
    ochn14_ichn5_vol_ms_index,
    ochn14_ichn6_vol_ms_index,
    ochn14_ichn7_vol_ms_index,
    ochn14_ichn8_vol_ms_index,
    ochn14_ichn9_vol_ms_index,
    ochn14_ichn10_vol_ms_index,
    ochn14_ichn11_vol_ms_index,
    ochn14_ichn12_vol_ms_index,
    ochn14_ichn13_vol_ms_index,
    ochn14_ichn14_vol_ms_index,
    ochn14_ichn15_vol_ms_index,
    ochn14_ichn16_vol_ms_index,
    ochn14_ichn17_vol_ms_index,
    ochn14_ichn18_vol_ms_index,
    ochn14_ichn19_vol_ms_index,
    ochn14_ichn20_vol_ms_index,
    ochn14_ichn21_vol_ms_index,
    ochn14_ichn22_vol_ms_index,
    ochn14_ichn23_vol_ms_index,
    ochn14_ichn24_vol_ms_index,
    ochn14_ichn25_vol_ms_index,
    ochn14_ichn26_vol_ms_index,
    ochn14_ichn27_vol_ms_index,
    ochn14_ichn28_vol_ms_index,
    ochn14_ichn29_vol_ms_index,
    ochn14_ichn30_vol_ms_index,
    ochn14_ichn31_vol_ms_index,
    ochn14_ichn32_vol_ms_index,

    ochn15_ichn1_vol_ms_index,
    ochn15_ichn2_vol_ms_index,
    ochn15_ichn3_vol_ms_index,
    ochn15_ichn4_vol_ms_index,
    ochn15_ichn5_vol_ms_index,
    ochn15_ichn6_vol_ms_index,
    ochn15_ichn7_vol_ms_index,
    ochn15_ichn8_vol_ms_index,
    ochn15_ichn9_vol_ms_index,
    ochn15_ichn10_vol_ms_index,
    ochn15_ichn11_vol_ms_index,
    ochn15_ichn12_vol_ms_index,
    ochn15_ichn13_vol_ms_index,
    ochn15_ichn14_vol_ms_index,
    ochn15_ichn15_vol_ms_index,
    ochn15_ichn16_vol_ms_index,
    ochn15_ichn17_vol_ms_index,
    ochn15_ichn18_vol_ms_index,
    ochn15_ichn19_vol_ms_index,
    ochn15_ichn20_vol_ms_index,
    ochn15_ichn21_vol_ms_index,
    ochn15_ichn22_vol_ms_index,
    ochn15_ichn23_vol_ms_index,
    ochn15_ichn24_vol_ms_index,
    ochn15_ichn25_vol_ms_index,
    ochn15_ichn26_vol_ms_index,
    ochn15_ichn27_vol_ms_index,
    ochn15_ichn28_vol_ms_index,
    ochn15_ichn29_vol_ms_index,
    ochn15_ichn30_vol_ms_index,
    ochn15_ichn31_vol_ms_index,
    ochn15_ichn32_vol_ms_index,

    ochn16_ichn1_vol_ms_index,
    ochn16_ichn2_vol_ms_index,
    ochn16_ichn3_vol_ms_index,
    ochn16_ichn4_vol_ms_index,
    ochn16_ichn5_vol_ms_index,
    ochn16_ichn6_vol_ms_index,
    ochn16_ichn7_vol_ms_index,
    ochn16_ichn8_vol_ms_index,
    ochn16_ichn9_vol_ms_index,
    ochn16_ichn10_vol_ms_index,
    ochn16_ichn11_vol_ms_index,
    ochn16_ichn12_vol_ms_index,
    ochn16_ichn13_vol_ms_index,
    ochn16_ichn14_vol_ms_index,
    ochn16_ichn15_vol_ms_index,
    ochn16_ichn16_vol_ms_index,
    ochn16_ichn17_vol_ms_index,
    ochn16_ichn18_vol_ms_index,
    ochn16_ichn19_vol_ms_index,
    ochn16_ichn20_vol_ms_index,
    ochn16_ichn21_vol_ms_index,
    ochn16_ichn22_vol_ms_index,
    ochn16_ichn23_vol_ms_index,
    ochn16_ichn24_vol_ms_index,
    ochn16_ichn25_vol_ms_index,
    ochn16_ichn26_vol_ms_index,
    ochn16_ichn27_vol_ms_index,
    ochn16_ichn28_vol_ms_index,
    ochn16_ichn29_vol_ms_index,
    ochn16_ichn30_vol_ms_index,
    ochn16_ichn31_vol_ms_index,
    ochn16_ichn32_vol_ms_index,

    mic_ans_mode_ms_index,
    dereverb_level_ms_index,

    aec2_denoise_ms_index,

    // test sig
    test_sig_type_ms_index,
    test_sig_freq_ms_index,
    test_sig_amp_ms_index,

    peq_anf_0_onoff_ms_index,
    peq_anf_0_cnt_ms_index,
    peq_anf_0_freq1_ms_index,
    peq_anf_0_freq2_ms_index,
    peq_anf_0_freq3_ms_index,
    peq_anf_0_freq4_ms_index,
    peq_anf_0_freq5_ms_index,
    peq_anf_0_freq6_ms_index,
    peq_anf_0_freq7_ms_index,
    peq_anf_0_freq8_ms_index,
    peq_anf_0_gain1_ms_index,
    peq_anf_0_gain2_ms_index,
    peq_anf_0_gain3_ms_index,
    peq_anf_0_gain4_ms_index,
    peq_anf_0_gain5_ms_index,
    peq_anf_0_gain6_ms_index,
    peq_anf_0_gain7_ms_index,
    peq_anf_0_gain8_ms_index,
    peq_anf_0_filter1_ms_index,
    peq_anf_0_filter2_ms_index,
    peq_anf_0_filter3_ms_index,
    peq_anf_0_filter4_ms_index,
    peq_anf_0_filter5_ms_index,
    peq_anf_0_filter6_ms_index,
    peq_anf_0_filter7_ms_index,
    peq_anf_0_filter8_ms_index,
    peq_anf_0_qval1_ms_index,
    peq_anf_0_qval2_ms_index,
    peq_anf_0_qval3_ms_index,
    peq_anf_0_qval4_ms_index,
    peq_anf_0_qval5_ms_index,
    peq_anf_0_qval6_ms_index,
    peq_anf_0_qval7_ms_index,
    peq_anf_0_qval8_ms_index,

    peq_anf_1_onoff_ms_index,
    peq_anf_1_cnt_ms_index,
    peq_anf_1_freq1_ms_index,
    peq_anf_1_freq2_ms_index,
    peq_anf_1_freq3_ms_index,
    peq_anf_1_freq4_ms_index,
    peq_anf_1_freq5_ms_index,
    peq_anf_1_freq6_ms_index,
    peq_anf_1_freq7_ms_index,
    peq_anf_1_freq8_ms_index,
    peq_anf_1_gain1_ms_index,
    peq_anf_1_gain2_ms_index,
    peq_anf_1_gain3_ms_index,
    peq_anf_1_gain4_ms_index,
    peq_anf_1_gain5_ms_index,
    peq_anf_1_gain6_ms_index,
    peq_anf_1_gain7_ms_index,
    peq_anf_1_gain8_ms_index,
    peq_anf_1_filter1_ms_index,
    peq_anf_1_filter2_ms_index,
    peq_anf_1_filter3_ms_index,
    peq_anf_1_filter4_ms_index,
    peq_anf_1_filter5_ms_index,
    peq_anf_1_filter6_ms_index,
    peq_anf_1_filter7_ms_index,
    peq_anf_1_filter8_ms_index,
    peq_anf_1_qval1_ms_index,
    peq_anf_1_qval2_ms_index,
    peq_anf_1_qval3_ms_index,
    peq_anf_1_qval4_ms_index,
    peq_anf_1_qval5_ms_index,
    peq_anf_1_qval6_ms_index,
    peq_anf_1_qval7_ms_index,
    peq_anf_1_qval8_ms_index,

    peq_anf_2_onoff_ms_index,
    peq_anf_2_cnt_ms_index,
    peq_anf_2_freq1_ms_index,
    peq_anf_2_freq2_ms_index,
    peq_anf_2_freq3_ms_index,
    peq_anf_2_freq4_ms_index,
    peq_anf_2_freq5_ms_index,
    peq_anf_2_freq6_ms_index,
    peq_anf_2_freq7_ms_index,
    peq_anf_2_freq8_ms_index,
    peq_anf_2_gain1_ms_index,
    peq_anf_2_gain2_ms_index,
    peq_anf_2_gain3_ms_index,
    peq_anf_2_gain4_ms_index,
    peq_anf_2_gain5_ms_index,
    peq_anf_2_gain6_ms_index,
    peq_anf_2_gain7_ms_index,
    peq_anf_2_gain8_ms_index,
    peq_anf_2_filter1_ms_index,
    peq_anf_2_filter2_ms_index,
    peq_anf_2_filter3_ms_index,
    peq_anf_2_filter4_ms_index,
    peq_anf_2_filter5_ms_index,
    peq_anf_2_filter6_ms_index,
    peq_anf_2_filter7_ms_index,
    peq_anf_2_filter8_ms_index,
    peq_anf_2_qval1_ms_index,
    peq_anf_2_qval2_ms_index,
    peq_anf_2_qval3_ms_index,
    peq_anf_2_qval4_ms_index,
    peq_anf_2_qval5_ms_index,
    peq_anf_2_qval6_ms_index,
    peq_anf_2_qval7_ms_index,
    peq_anf_2_qval8_ms_index,

    peq_aec_1_onoff_ms_index,
    peq_aec_1_cnt_ms_index,
    peq_aec_1_freq1_ms_index,
    peq_aec_1_freq2_ms_index,
    peq_aec_1_freq3_ms_index,
    peq_aec_1_freq4_ms_index,
    peq_aec_1_freq5_ms_index,
    peq_aec_1_freq6_ms_index,
    peq_aec_1_freq7_ms_index,
    peq_aec_1_freq8_ms_index,
    peq_aec_1_gain1_ms_index,
    peq_aec_1_gain2_ms_index,
    peq_aec_1_gain3_ms_index,
    peq_aec_1_gain4_ms_index,
    peq_aec_1_gain5_ms_index,
    peq_aec_1_gain6_ms_index,
    peq_aec_1_gain7_ms_index,
    peq_aec_1_gain8_ms_index,
    peq_aec_1_filter1_ms_index,
    peq_aec_1_filter2_ms_index,
    peq_aec_1_filter3_ms_index,
    peq_aec_1_filter4_ms_index,
    peq_aec_1_filter5_ms_index,
    peq_aec_1_filter6_ms_index,
    peq_aec_1_filter7_ms_index,
    peq_aec_1_filter8_ms_index,
    peq_aec_1_qval1_ms_index,
    peq_aec_1_qval2_ms_index,
    peq_aec_1_qval3_ms_index,
    peq_aec_1_qval4_ms_index,
    peq_aec_1_qval5_ms_index,
    peq_aec_1_qval6_ms_index,
    peq_aec_1_qval7_ms_index,
    peq_aec_1_qval8_ms_index,

    peq_aec_2_onoff_ms_index,
    peq_aec_2_cnt_ms_index,
    peq_aec_2_freq1_ms_index,
    peq_aec_2_freq2_ms_index,
    peq_aec_2_freq3_ms_index,
    peq_aec_2_freq4_ms_index,
    peq_aec_2_freq5_ms_index,
    peq_aec_2_freq6_ms_index,
    peq_aec_2_freq7_ms_index,
    peq_aec_2_freq8_ms_index,
    peq_aec_2_gain1_ms_index,
    peq_aec_2_gain2_ms_index,
    peq_aec_2_gain3_ms_index,
    peq_aec_2_gain4_ms_index,
    peq_aec_2_gain5_ms_index,
    peq_aec_2_gain6_ms_index,
    peq_aec_2_gain7_ms_index,
    peq_aec_2_gain8_ms_index,
    peq_aec_2_filter1_ms_index,
    peq_aec_2_filter2_ms_index,
    peq_aec_2_filter3_ms_index,
    peq_aec_2_filter4_ms_index,
    peq_aec_2_filter5_ms_index,
    peq_aec_2_filter6_ms_index,
    peq_aec_2_filter7_ms_index,
    peq_aec_2_filter8_ms_index,
    peq_aec_2_qval1_ms_index,
    peq_aec_2_qval2_ms_index,
    peq_aec_2_qval3_ms_index,
    peq_aec_2_qval4_ms_index,
    peq_aec_2_qval5_ms_index,
    peq_aec_2_qval6_ms_index,
    peq_aec_2_qval7_ms_index,
    peq_aec_2_qval8_ms_index,

    howling_enable_ms_index,
    mic_aec_mode_ms_index,
    aec_mic_num_ms_index,
    aec_spk_num_ms_index,
    anf_filter_num_ms_index,
    anf_filter_dep_ms_index,
    aec_comfor_noise_ms_index,
    aec_dwmx_mode_ms_index,
    afc_path_mode_ms_index,
    afc_freq_shift_ms_index,
    aec_noise_level_ms_index,
    aec_0_energy_ms_index,
    aec_1_energy_ms_index,
    aec0_delay_ms_index,
    aec0_length_ms_index,
    aec1_delay_ms_index,
    aec1_length_ms_index,

    aec_bkg_decay_ms_index,
    aec_bypass_ms_index,

    low_shelf_freq_ms_index,
    low_shelf_gain_ms_index,
    low_shelf_qval_ms_index,
    aec_highshelf_level_ms_index,

    mixer_process_tick_ms_index,
    mixer_process_count_ms_index,

    YM_MIXER_PARAM_MAX,
} ym_mixer_param_def;

#endif
