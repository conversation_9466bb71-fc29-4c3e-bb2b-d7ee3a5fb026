/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-06-28 15:21:22
 * @LastEditTime: 2024-11-18 14:15:02
 * @LastEditors: Anzhi<PERSON>o
 * @Description:
 * @FilePath: /audio_mixer_rk3308/include/ym_database.h
 * Copyright (c) 2006-2023, Yinkman Development Team
 */
#ifndef __DATABASE_H__
#define __DATABASE_H__

#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include "ym_database_index.h"

// 数据库数据内容，数据元素都采用32位保存
typedef union
{
    int i32;
    uint32_t u32;
    float f32;
} database_value;

#define DATABASE_VALUE_UNIT sizeof(database_value)

/**
 * @description: 获取database参数地址，只能读该地址，不要强制写入，强制写入会破坏database机制。
 * @param {database_index} index
 * @return {*} NULL fail
 * @use:
 */
static inline const database_value *database_get(int index)
{
    extern database_value database_meta_value[DATABASE_INDEX_MAX];
    return &database_meta_value[index];
}

static inline const database_value *database_get_from_shot(int index)
{
    extern database_value database_meta_value_shot[DATABASE_INDEX_MAX];
    return &database_meta_value_shot[index];
}

/**
 * @description:
 * @param {uint32_t} count,表明要读取的参数个数
 * @param {database_rw_def *} rw_def,读写参数的结构体
 * @return {int} 0,成功;其他失败
 * @use:
 */
static inline database_value database_read(int index)
{
    extern database_value database_meta_value[DATABASE_INDEX_MAX];
    return database_meta_value[index];
}

/**
 * @description:写入数据库
 * @param {uint32_t} count,表明要写入的参数个数
 * @param {database_rw_def *} rw_def,读写参数的结构体
 * @return {int} 0,成功;其他失败
 * @use:
 */
int database_write(int index, database_value d);

int database_write2(int index, const database_value *pvalue, uint32_t count);

static inline int database_write_no_callback_to_shot(int index, database_value d)
{
    extern database_value database_meta_value_shot[DATABASE_INDEX_MAX];
    database_meta_value_shot[index] = d;
    return 0;
}

static inline int database_write2_no_callback_to_shot(int index, const database_value *pvalue, uint32_t count)
{
    extern database_value database_meta_value_shot[DATABASE_INDEX_MAX];
    memcpy(database_meta_value_shot, pvalue, sizeof(database_value) * count);
    return 0;
}

int database_write_no_callback(int index, database_value d);

int database_write2_no_callback(int index, const database_value *pvalue, uint32_t count);

/**
 * @description:注册数据库参数修改回调函数
 * @param {database_index} index,数据库索引
 * @param {value_write_callback} callback,回调函数指针
 * @return {int} 0,成功;其他失败
 */
int database_register(int index,
                      void (*value_write_callback)(int index, database_value old_data, database_value new_data));

/**
 * @description:注册数据库参数修改检测回调函数
 * @param {database_index} index,数据库索引
 * @param {value_write_callback} callback,回调函数指针
 * @return {database_value} 返回合法参数值
 */
int database_register_check(int index,
                            database_value (*value_write_callback)(int index, database_value data));

void database_take_shot(void);

void database_restore_frome_shot(void);

void database_lowlevel_init(void);

#endif
