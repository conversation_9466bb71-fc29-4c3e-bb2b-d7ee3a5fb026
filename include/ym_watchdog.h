#ifndef _YM_WATCHDOG_H_
#define _YM_WATCHDOG_H_

#include <stdint.h>

/**
 * @brief Initialize the watchdog timer.
 *
 * @param timeout The timeout value in milliseconds.
 */
void watchdog_init(int32_t timeout);

/**
 * @brief Feed the watchdog timer.
 *
 * This function should be called periodically to prevent the watchdog timer from
 * expiring.
 */
void watchdog_feed(void);

#endif /* _YM_WATCHDOG_H_ */
