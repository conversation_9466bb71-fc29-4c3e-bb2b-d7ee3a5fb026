#ifndef __YM_LOG_TRANS_H__
#define __YM_LOG_TRANS_H__

#include <stdint.h>

/**
 * @brief 启动日志传输
 *
 * @return int32_t 0:成功 -1:失败
 */
int32_t ym_log_trans_start(void);

/**
 * @brief 停止日志传输
 *
 * @return int32_t 0:成功 -1:失败
 */
int32_t ym_log_trans_stop(void);

/**
 * @brief 获取日志
 *
 * @param buf 日志缓存
 * @param len 日志缓存长度
 * @return uint8_t 实际获取到的日志长度
 */
uint8_t ym_log_trans_get(uint8_t *buf, uint32_t len);

#endif /* __YM_LOG_TRANS_H__ */
