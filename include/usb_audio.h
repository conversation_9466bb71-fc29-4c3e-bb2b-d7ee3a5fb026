/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-03-26 14:08:47
 * @LastEditTime: 2024-10-11 15:50:33
 * @LastEditors: Anzhichao
 * @Description:
 * @FilePath: /audio_mixer_rk3308/include/usb_audio.h
 * Copyright (c) 2006-2024, Yinkman Development Team
 */
#ifndef _USB_AUDIO_H_
#define _USB_AUDIO_H_

#include <alsa/asoundlib.h>
#include <semaphore.h>

#include "ym_stream.h"
#include "ym_resamplerate.h"
#include "ym_ring_buffer.h"

#define USB_AUDIO_CHNS 2
#define USB_DATA32_LEN (FRAMES * USB_AUDIO_CHNS * sizeof(int))
#define USB_DATA16_LEN (FRAMES * USB_AUDIO_CHNS * sizeof(short))

typedef void (*usb_audio_constructor_callback)(void *userdata);

typedef void (*usb_audio_deconstructor_callback)(void *userdata);

typedef struct _usb_host_audio
{
    int card;
    int device;
    int rate;
    int channels;
    int period_size;
    int period_count;
    int format;
    int format_bit;
    int type;
    uint32_t xrun_count;

    uint16_t vid;
    uint16_t pid;
    int alloca_channel;

    snd_pcm_t *pcm;

    struct yinkman_stream *stream;
    sem_t stream_sem;
    void *thread_pid;

    ring_buffer_t usb_host_l_rb;
    ring_buffer_t usb_host_r_rb;
    pthread_mutex_t usb_host_rb_mutex;

    samplerate_p p_samplerate;

    struct sliding_average_filter *intervel_filter;
    float intervel_tick;

    usb_audio_constructor_callback constructor_callback;
    usb_audio_deconstructor_callback deconstructor_callback;

    struct _usb_host_audio *next, *prev;

} usb_host_audio_t, *usb_host_audio_p;

void get_pcm_type(const char *s, int *card, int *device, char *type);
int set_hwparams(snd_pcm_t *handle,
                 snd_pcm_hw_params_t *params,
                 snd_pcm_access_t __access,
                 snd_pcm_uframes_t buffer_size,
                 snd_pcm_uframes_t period_size);
int set_swparams(snd_pcm_t *handle,
                 snd_pcm_sw_params_t *swparams,
                 int start,
                 int stop,
                 int min);

int capture_xrun_recovery(snd_pcm_t *handle);
int play_xrun_recovery(snd_pcm_t *handle);

ssize_t usb_pcm_read(snd_pcm_t *handle, uint8_t *data, size_t rcount);
ssize_t usb_pcm_write(snd_pcm_t *handle, uint8_t *data, size_t count);

// 白名单中的VID和PID
typedef struct
{
    const char *vid;
    const char *pid;
    const char tpye;
    usb_audio_constructor_callback constructor_callback;
    usb_audio_deconstructor_callback deconstructor_callback;
} USBWhiteListEntry;

extern USBWhiteListEntry whitelist[];

// int uac_capture_vol_get_int(void);
int uac_vol_int2per(int vol, int is_macos);
void uac_host_os_set(int is_macos);

#endif
