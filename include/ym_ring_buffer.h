#ifndef _YM_RING_BUFFER_H_
#define _YM_RING_BUFFER_H_

#include <stdbool.h>
#include <stdint.h>

typedef struct ring_buffer
{
    char *buffer; // 环形缓冲区的内存空间
    uint32_t head;  // 写入指针
    uint32_t tail;  // 读取指针
    uint32_t max;   // 缓冲区的最大容量
    bool full;     // 缓冲区是否已满
} ring_buffer_t, *ring_buffer_p;

/**
 * @brief: 创建环形缓冲区
 * @param rb: 环形缓冲区指针
 * @param size: 环形缓冲区大小
 */
void ring_buffer_create(ring_buffer_p *rb, uint32_t size);

/**
 * @brief: 初始化环形缓冲区
 * @param rb: 环形缓冲区指针
 * @param size: 环形缓冲区大小
 */
void ring_buffer_init(ring_buffer_p rb, uint32_t size);

/**
 * @brief: 释放环形缓冲区
 * @param rb: 环形缓冲区指针
 */
void ring_buffer_free(ring_buffer_p rb);

/**
 * @brief: 清空环形缓冲区
 * @param rb: 环形缓冲区指针
 */
void ring_buffer_clear(ring_buffer_p rb);

/**
 * @brief: 写入数据到环形缓冲区
 * @param rb: 环形缓冲区指针
 * @param data: 写入数据指针
 * @param size: 写入数据大小
 */
void ring_buffer_write(ring_buffer_p rb, const char *data, uint32_t size);

/**
 * @brief: 从环形缓冲区读取数据
 * @param rb: 环形缓冲区指针
 * @param data: 读取数据指针
 * @param size: 读取数据大小
 * @return: 实际读取数据大小
 */
uint32_t ring_buffer_read(ring_buffer_p rb, char *data, uint32_t size);

/**
 * @brief: 检查环形缓冲区是否为空
 * @param rb: 环形缓冲区指针
 * @return: true 为空，false 不为空
 */
bool ring_buffer_empty(ring_buffer_p rb);

/**
 * @brief: 检查环形缓冲区是否已满
 * @param rb: 环形缓冲区指针
 * @return: true 已满，false 未满
 */
bool ring_buffer_full(ring_buffer_p rb);

/**
 * @brief: 获取环形缓冲区可用数据大小
 * @param rb: 环形缓冲区指针
 * @return: 可用数据大小
 */
uint32_t ring_buffer_available_data(ring_buffer_p rb);

/**
 * @brief: 销毁环形缓冲区
 * @param rb: 环形缓冲区指针
 */
void ring_buffer_destroy(ring_buffer_p rb);

#endif /* _YM_RING_BUFFER_H_ */
