#ifndef _YM_MEM_POOL_H_
#define _YM_MEM_POOL_H_

#include <stdint.h>

/*
 * @brief Initialize memory pool
 *
 * @param pool_sz Memory pool size
 * @param item_sz Memory pool item size
 * @return void* Memory pool pointer
 */
void *ym_mem_pool_init(uint32_t pool_sz, uint32_t item_sz);

/**
 * @brief Allocate memory from memory pool
 *
 * @param pool_id Memory pool pointer
 * @return void* Memory pointer
 */
void *ym_mem_pool_alloc(void *pool_id);

/**
 * @brief Free memory pool memory
 *
 * @param pool_id Memory pool pointer
 * @param block Memory pointer
 */
void ym_mem_pool_free(void *pool_id, void *block);

/**
 * @brief Destroy memory pool
 *
 * @param pool_id Memory pool pointer
 */
void ym_mem_pool_destroy(void *pool_id);

/**
 * @brief Clear memory pool
 *
 * @param pool_id Memory pool pointer
 */
void ym_mem_pool_clear(void *pool_id);

#endif /* _YM_MEM_POOL_H_ */
