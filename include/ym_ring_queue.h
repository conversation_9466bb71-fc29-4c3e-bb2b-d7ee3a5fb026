#ifndef _YM_RING_QUEUE_H_
#define _YM_RING_QUEUE_H_

#include <stdint.h>

typedef struct ring_queue
{
    int32_t *buffer;
    uint32_t front;
    uint32_t rear;
    uint32_t size;
} ring_queue_t, *ring_queue_p;

/**
 * @brief 创建一个环形队列
 *
 * @param size 队列大小
 * @return ring_queue_p 队列指针
 */
ring_queue_p ring_queue_create(uint32_t size);

/**
 * @brief 入队
 *
 * @param q 队列指针
 * @param d 数据指针
 * @param n 数据长度
 * @return uint32_t 入队长度
 */
uint32_t ring_queue_push(ring_queue_p q, int32_t *d, uint32_t n);

/**
 * @brief 出队
 *
 * @param q 队列指针
 * @param d 数据指针
 * @param n 数据长度
 * @return int32_t 出队长度
 */
int32_t ring_queue_pull(ring_queue_p q, int32_t *d, uint32_t n);

/**
 * @brief 获取队列长度
 *
 * @param q 队列指针
 * @return uint32_t 队列长度
 */
uint32_t ring_queue_len(ring_queue_p q);

/**
 * @brief 销毁队列
 *
 * @param q 队列指针
 */
void ringqueueDestroy(ring_queue_p q);

#endif /* _YM_RING_QUEUE_H_ */
