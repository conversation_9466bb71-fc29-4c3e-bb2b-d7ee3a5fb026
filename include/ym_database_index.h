#ifndef _DATABASE_INDEX_H_
#define _DATABASE_INDEX_H_

#include "ym_mixer_param_index.h"
#include "ym_beam_param_index.h"

#define DATABASE_VERSION (0x20250716)

typedef enum
{
    database_version_index,
    soc_temp_index,
    hwid_adc_subversion_index,
    hwid_text_index,
    a2b_slave_nodes_index,
    a2b_slave_upgrade_index,        // 表示是否升级中
    a2b_slave_upgrade_status_index, // 表示升级进度

    usb_flash_record_status_index,

    firmware_upgrading_index,
#if 0
    ym_wmic_pair_onoff_index,
    ym_wmic_mic1_lock_pair_index,
    ym_wmic_mic2_lock_pair_index,
    ym_wmic_mic1_lock_stat_index,
    ym_wmic_mic2_lock_stat_index,
    ym_wmic_mic1_curr_freq_index,
    ym_wmic_mic2_curr_freq_index,
    ym_wmic_mic1_battery_index,
    ym_wmic_mic2_battery_index,
    ym_wmic_mic1_volume_index,
    ym_wmic_mic2_volume_index,
    ym_wmic_mic1_pair_stat_index,
    ym_wmic_mic2_pair_stat_index,
    ym_wmic_mic1_vol_level_index,
    ym_wmic_mic2_vol_level_index,
    ym_wmic_mic1_link_stat_index,
    ym_wmic_mic2_link_stat_index,
    ym_wmic_mic1_rf_rssi_index,
    ym_wmic_mic2_rf_rssi_index,
    ym_wmic_release_chn_index,
    ym_wmic_mic1_specify_chn_index,
    ym_wmic_mic2_specify_chn_index,
    ym_wmic_firmware_ver_index,
    ym_wmic_firmware_date_index,
    ym_wmic_firmware_time_index,
#endif
    start_timestamp_index,     // 记录软件启动时间戳
    frame_period_index,        // 记录系统处理帧数
    is_ont_touch_tuning_index, // 是否为一键调音
    tuning_result_index,       // 调音结果
    usb_ppm_index,

    // hardware gain
    input1_gain_index,
    input2_gain_index,
    input3_gain_index,
    input4_gain_index,
    input5_gain_index,
    input6_gain_index,
    input7_gain_index,
    input8_gain_index,
    input9_gain_index,
    input10_gain_index,
    input11_gain_index,
    input12_gain_index,
    input13_gain_index,
    input14_gain_index,
    input15_gain_index,
    input16_gain_index,
    input17_gain_index,
    input18_gain_index,
    input19_gain_index,
    input20_gain_index,
    input21_gain_index,
    input22_gain_index,
    input23_gain_index,
    input24_gain_index,
    input25_gain_index,
    input26_gain_index,
    input27_gain_index,
    input28_gain_index,
    input29_gain_index,
    input30_gain_index,
    input31_gain_index,
    input32_gain_index,

    mic_48v_gate1_index,
    mic_48v_gate2_index,
    mic_48v_gate3_index,
    mic_48v_gate4_index,
    mic_48v_gate5_index,
    mic_48v_gate6_index,
    mic_48v_gate7_index,
    mic_48v_gate8_index,

    power_232_index,
    wen_xiang_bt_state_index,
    wen_xiang_bt2_state_index,
    wen_xiang_bt_count_index,

    mic_boost_gain1_index,
    mic_boost_gain2_index,
    mic_boost_gain3_index,
    mic_boost_gain4_index,
    mic_boost_gain5_index,
    mic_boost_gain6_index,
    mic_boost_gain7_index,
    mic_boost_gain8_index,

    /* max:119
     * | dac_gain1  | dac_gain2  | dac_gain3  | dac_gain4  |
     * | dac_gain5  | dac_gain6  | dac_gain7  | dac_gain8  |
     */
    dac_gain1_index,
    dac_gain2_index,
    dac_gain3_index,
    dac_gain4_index,

    // 通道属性
    ichn1_attr_index,
    ichn2_attr_index,
    ichn3_attr_index,
    ichn4_attr_index,
    ichn5_attr_index,
    ichn6_attr_index,
    ichn7_attr_index,
    ichn8_attr_index,
    ichn9_attr_index,
    ichn10_attr_index,
    ichn11_attr_index,
    ichn12_attr_index,
    ichn13_attr_index,
    ichn14_attr_index,
    ichn15_attr_index,
    ichn16_attr_index,
    ichn17_attr_index,
    ichn18_attr_index,
    ichn19_attr_index,
    ichn20_attr_index,
    ichn21_attr_index,
    ichn22_attr_index,
    ichn23_attr_index,
    ichn24_attr_index,
    ichn25_attr_index,
    ichn26_attr_index,
    ichn27_attr_index,
    ichn28_attr_index,
    ichn29_attr_index,
    ichn30_attr_index,
    ichn31_attr_index,
    ichn32_attr_index,
    ochn1_attr_index,
    ochn2_attr_index,
    ochn3_attr_index,
    ochn4_attr_index,
    ochn5_attr_index,
    ochn6_attr_index,
    ochn7_attr_index,
    ochn8_attr_index,
    ochn9_attr_index,
    ochn10_attr_index,
    ochn11_attr_index,
    ochn12_attr_index,
    ochn13_attr_index,
    ochn14_attr_index,
    ochn15_attr_index,
    ochn16_attr_index,

    pcm_dummy1_out_delay_index,
    pcm_dummy2_out_delay_index,
    pcm_acodec_out_delay_index,
    pcm_a2b_out_delay_index,

    pcm_usb_in_avail_index,
    pcm_usb_in_delay_index,
    pcm_usb_in_interval_index,
    pcm_usb_in_diff_index,
    pcm_usb_out_avail_index,
    pcm_usb_out_delay_index,
    pcm_usb_out_interval_index,
    pcm_usb_out_diff_index,

    // delay
    /**
     * mic delay
     * aux delay
     * BGM delay
     * output delay
     * input delay
     */
    global_mic_delay_index,
    global_aux_delay_index,
    global_bgm_delay_index,

    mic_global_sys_vol_index,
    global_sys_out_vol_index,
    global_skp_out_vol_index,
    global_tune_mode_vol_index,
    global_aec_out_vol_index,
    global_afc_out_vol_index,

    global_mic_mute_index,
    global_out_mute_index,
    global_skp_mute_index,
    global_aec_out_mute_index,
    global_afc_out_mute_index,
    global_hw_pa_mute_index,

    teacker_mic_mute_index,
    student_mic_mute_index,

    aec_ref_denoise_index,

    speech_enhance_level_index,
    speech_restore_level_index,

    /* gain control params */
    mic_noise_gate_gain_control_on_off_index,
    mic_noise_gate_gain_control_theshold_target_index,
    mic_noise_gate_gain_control_makeup_boost_suppress_index,
    mic_noise_gate_gain_control_attacktime_index,
    mic_noise_gate_gain_control_releasetime_index,
    mic_noise_gate_gain_control_compr_ratio_index,

    aux_noise_gate_gain_control_on_off_index,
    aux_noise_gate_gain_control_theshold_target_index,
    aux_noise_gate_gain_control_makeup_boost_suppress_index,
    aux_noise_gate_gain_control_attacktime_index,
    aux_noise_gate_gain_control_releasetime_index,
    aux_noise_gate_gain_control_compr_ratio_index,

    mic_drc_gain_control_on_off_index,
    mic_drc_gain_control_theshold_target_index,
    mic_drc_gain_control_makeup_boost_suppress_index,
    mic_drc_gain_control_attacktime_index,
    mic_drc_gain_control_releasetime_index,
    mic_drc_gain_control_compr_ratio_index,

    aux_drc_gain_control_on_off_index,
    aux_drc_gain_control_theshold_target_index,
    aux_drc_gain_control_makeup_boost_suppress_index,
    aux_drc_gain_control_attacktime_index,
    aux_drc_gain_control_releasetime_index,
    aux_drc_gain_control_compr_ratio_index,

    mic_expander_gain_control_on_off_index,
    mic_expander_gain_control_theshold_target_index,
    mic_expander_gain_control_makeup_boost_suppress_index,
    mic_expander_gain_control_attacktime_index,
    mic_expander_gain_control_releasetime_index,
    mic_expander_gain_control_compr_ratio_index,

    aux_expander_gain_control_on_off_index,
    aux_expander_gain_control_theshold_target_index,
    aux_expander_gain_control_makeup_boost_suppress_index,
    aux_expander_gain_control_attacktime_index,
    aux_expander_gain_control_releasetime_index,
    aux_expander_gain_control_compr_ratio_index,

    mic_agc_gain_control_on_off_index,
    mic_agc_gain_control_theshold_target_index,
    mic_agc_gain_control_makeup_boost_suppress_index,
    mic_agc_gain_control_attacktime_index,
    mic_agc_gain_control_releasetime_index,
    mic_agc_gain_control_compr_ratio_index,

    aux_agc_gain_control_on_off_index,
    aux_agc_gain_control_theshold_target_index,
    aux_agc_gain_control_makeup_boost_suppress_index,
    aux_agc_gain_control_attacktime_index,
    aux_agc_gain_control_releasetime_index,
    aux_agc_gain_control_compr_ratio_index,

    mic_limiter_gain_control_on_off_index,
    mic_limiter_gain_control_theshold_target_index,
    mic_limiter_gain_control_makeup_boost_suppress_index,
    mic_limiter_gain_control_attacktime_index,
    mic_limiter_gain_control_releasetime_index,
    mic_limiter_gain_control_compr_ratio_index,

    aux_limiter_gain_control_on_off_index,
    aux_limiter_gain_control_theshold_target_index,
    aux_limiter_gain_control_makeup_boost_suppress_index,
    aux_limiter_gain_control_attacktime_index,
    aux_limiter_gain_control_releasetime_index,
    aux_limiter_gain_control_compr_ratio_index,

    sp_limiter_gain_control_on_off_index,
    sp_limiter_gain_control_theshold_target_index,
    sp_limiter_gain_control_makeup_boost_suppress_index,
    sp_limiter_gain_control_attacktime_index,
    sp_limiter_gain_control_releasetime_index,
    sp_limiter_gain_control_compr_ratio_index,

    hp_limiter_gain_control_on_off_index,
    hp_limiter_gain_control_theshold_target_index,
    hp_limiter_gain_control_makeup_boost_suppress_index,
    hp_limiter_gain_control_attacktime_index,
    hp_limiter_gain_control_releasetime_index,
    hp_limiter_gain_control_compr_ratio_index,

    rec_limiter_gain_control_on_off_index,
    rec_limiter_gain_control_theshold_target_index,
    rec_limiter_gain_control_makeup_boost_suppress_index,
    rec_limiter_gain_control_attacktime_index,
    rec_limiter_gain_control_releasetime_index,
    rec_limiter_gain_control_compr_ratio_index,

    auto_ducker_gain_control_on_off_index,
    auto_ducker_gain_control_theshold_target_index,
    auto_ducker_gain_control_makeup_boost_suppress_index,
    auto_ducker_gain_control_attacktime_index,
    auto_ducker_gain_control_releasetime_index,
    auto_ducker_gain_control_compr_ratio_index,

    mic_aec1_auto_mixer_gain_control_on_off_index,
    mic_aec1_auto_mixer_gain_control_theshold_target_index,
    mic_aec1_auto_mixer_gain_control_makeup_boost_suppress_index,
    mic_aec1_auto_mixer_gain_control_attacktime_index,
    mic_aec1_auto_mixer_gain_control_releasetime_index,
    mic_aec1_auto_mixer_gain_control_compr_ratio_index,

    mic_aec0_auto_mixer_gain_control_on_off_index,
    mic_aec0_auto_mixer_gain_control_theshold_target_index,
    mic_aec0_auto_mixer_gain_control_makeup_boost_suppress_index,
    mic_aec0_auto_mixer_gain_control_attacktime_index,
    mic_aec0_auto_mixer_gain_control_releasetime_index,
    mic_aec0_auto_mixer_gain_control_compr_ratio_index,

    // 0：无闪避,1：闪避参考信号,2：被闪避信号
    input_channel1_ducker_index,
    input_channel2_ducker_index,
    input_channel3_ducker_index,
    input_channel4_ducker_index,
    input_channel5_ducker_index,
    input_channel6_ducker_index,
    input_channel7_ducker_index,
    input_channel8_ducker_index,
    input_channel9_ducker_index,
    input_channel10_ducker_index,
    input_channel11_ducker_index,
    input_channel12_ducker_index,
    input_channel13_ducker_index,
    input_channel14_ducker_index,
    input_channel15_ducker_index,
    input_channel16_ducker_index,
    input_channel17_ducker_index,
    input_channel18_ducker_index,
    input_channel19_ducker_index,
    input_channel20_ducker_index,
    input_channel21_ducker_index,
    input_channel22_ducker_index,
    input_channel23_ducker_index,
    input_channel24_ducker_index,
    input_channel25_ducker_index,
    input_channel26_ducker_index,
    input_channel27_ducker_index,
    input_channel28_ducker_index,
    input_channel29_ducker_index,
    input_channel30_ducker_index,
    input_channel31_ducker_index,
    input_channel32_ducker_index,

    // mixer输入通道音量 max:220
    input_channel_vol1_index,
    input_channel_vol2_index,
    input_channel_vol3_index,
    input_channel_vol4_index,
    input_channel_vol5_index,
    input_channel_vol6_index,
    input_channel_vol7_index,
    input_channel_vol8_index,
    input_channel_vol9_index,
    input_channel_vol10_index,
    input_channel_vol11_index,
    input_channel_vol12_index,
    input_channel_vol13_index,
    input_channel_vol14_index,
    input_channel_vol15_index,
    input_channel_vol16_index,
    input_channel_vol17_index,
    input_channel_vol18_index,
    input_channel_vol19_index,
    input_channel_vol20_index,
    input_channel_vol21_index,
    input_channel_vol22_index,
    input_channel_vol23_index,
    input_channel_vol24_index,
    input_channel_vol25_index,
    input_channel_vol26_index,
    input_channel_vol27_index,
    input_channel_vol28_index,
    input_channel_vol29_index,
    input_channel_vol30_index,
    input_channel_vol31_index,
    input_channel_vol32_index,

    input_channel1_mute_index,
    input_channel2_mute_index,
    input_channel3_mute_index,
    input_channel4_mute_index,
    input_channel5_mute_index,
    input_channel6_mute_index,
    input_channel7_mute_index,
    input_channel8_mute_index,
    input_channel9_mute_index,
    input_channel10_mute_index,
    input_channel11_mute_index,
    input_channel12_mute_index,
    input_channel13_mute_index,
    input_channel14_mute_index,
    input_channel15_mute_index,
    input_channel16_mute_index,
    input_channel17_mute_index,
    input_channel18_mute_index,
    input_channel19_mute_index,
    input_channel20_mute_index,
    input_channel21_mute_index,
    input_channel22_mute_index,
    input_channel23_mute_index,
    input_channel24_mute_index,
    input_channel25_mute_index,
    input_channel26_mute_index,
    input_channel27_mute_index,
    input_channel28_mute_index,
    input_channel29_mute_index,
    input_channel30_mute_index,
    input_channel31_mute_index,
    input_channel32_mute_index,

    // mixer output channel type
    output_channel1_type_index,
    output_channel2_type_index,
    output_channel3_type_index,
    output_channel4_type_index,
    output_channel5_type_index,
    output_channel6_type_index,
    output_channel7_type_index,
    output_channel8_type_index,
    output_channel9_type_index,
    output_channel10_type_index,
    output_channel11_type_index,
    output_channel12_type_index,
    output_channel13_type_index,
    output_channel14_type_index,
    output_channel15_type_index,
    output_channel16_type_index,

    // 0：无反向,1：反向
    output_channel1_phase_invert_index,
    output_channel2_phase_invert_index,
    output_channel3_phase_invert_index,
    output_channel4_phase_invert_index,
    output_channel5_phase_invert_index,
    output_channel6_phase_invert_index,
    output_channel7_phase_invert_index,
    output_channel8_phase_invert_index,
    output_channel9_phase_invert_index,
    output_channel10_phase_invert_index,
    output_channel11_phase_invert_index,
    output_channel12_phase_invert_index,
    output_channel13_phase_invert_index,
    output_channel14_phase_invert_index,
    output_channel15_phase_invert_index,
    output_channel16_phase_invert_index,

    // GEQ
    geq_mic_onoff_index,
    geq_mic_cnt_index,
    geq_mic_freq1_index,
    geq_mic_freq2_index,
    geq_mic_freq3_index,
    geq_mic_freq4_index,
    geq_mic_freq5_index,
    geq_mic_freq6_index,
    geq_mic_freq7_index,
    geq_mic_freq8_index,
    geq_mic_freq9_index,
    geq_mic_freq10_index,
    geq_mic_freq11_index,
    geq_mic_freq12_index,
    geq_mic_freq13_index,
    geq_mic_freq14_index,
    geq_mic_freq15_index,
    geq_mic_freq16_index,
    geq_mic_freq17_index,
    geq_mic_freq18_index,
    geq_mic_freq19_index,
    geq_mic_freq20_index,
    geq_mic_freq21_index,
    geq_mic_freq22_index,
    geq_mic_freq23_index,
    geq_mic_freq24_index,
    geq_mic_freq25_index,
    geq_mic_freq26_index,
    geq_mic_freq27_index,
    geq_mic_freq28_index,
    geq_mic_freq29_index,
    geq_mic_freq30_index,
    geq_mic_freq31_index,
    geq_mic_freq32_index,
    geq_mic_gain1_index,
    geq_mic_gain2_index,
    geq_mic_gain3_index,
    geq_mic_gain4_index,
    geq_mic_gain5_index,
    geq_mic_gain6_index,
    geq_mic_gain7_index,
    geq_mic_gain8_index,
    geq_mic_gain9_index,
    geq_mic_gain10_index,
    geq_mic_gain11_index,
    geq_mic_gain12_index,
    geq_mic_gain13_index,
    geq_mic_gain14_index,
    geq_mic_gain15_index,
    geq_mic_gain16_index,
    geq_mic_gain17_index,
    geq_mic_gain18_index,
    geq_mic_gain19_index,
    geq_mic_gain20_index,
    geq_mic_gain21_index,
    geq_mic_gain22_index,
    geq_mic_gain23_index,
    geq_mic_gain24_index,
    geq_mic_gain25_index,
    geq_mic_gain26_index,
    geq_mic_gain27_index,
    geq_mic_gain28_index,
    geq_mic_gain29_index,
    geq_mic_gain30_index,
    geq_mic_gain31_index,
    geq_mic_gain32_index,
    geq_aux_onoff_index,
    geq_aux_cnt_index,
    geq_aux_freq1_index,
    geq_aux_freq2_index,
    geq_aux_freq3_index,
    geq_aux_freq4_index,
    geq_aux_freq5_index,
    geq_aux_freq6_index,
    geq_aux_freq7_index,
    geq_aux_freq8_index,
    geq_aux_freq9_index,
    geq_aux_freq10_index,
    geq_aux_freq11_index,
    geq_aux_freq12_index,
    geq_aux_freq13_index,
    geq_aux_freq14_index,
    geq_aux_freq15_index,
    geq_aux_freq16_index,
    geq_aux_freq17_index,
    geq_aux_freq18_index,
    geq_aux_freq19_index,
    geq_aux_freq20_index,
    geq_aux_freq21_index,
    geq_aux_freq22_index,
    geq_aux_freq23_index,
    geq_aux_freq24_index,
    geq_aux_freq25_index,
    geq_aux_freq26_index,
    geq_aux_freq27_index,
    geq_aux_freq28_index,
    geq_aux_freq29_index,
    geq_aux_freq30_index,
    geq_aux_freq31_index,
    geq_aux_freq32_index,
    geq_aux_gain1_index,
    geq_aux_gain2_index,
    geq_aux_gain3_index,
    geq_aux_gain4_index,
    geq_aux_gain5_index,
    geq_aux_gain6_index,
    geq_aux_gain7_index,
    geq_aux_gain8_index,
    geq_aux_gain9_index,
    geq_aux_gain10_index,
    geq_aux_gain11_index,
    geq_aux_gain12_index,
    geq_aux_gain13_index,
    geq_aux_gain14_index,
    geq_aux_gain15_index,
    geq_aux_gain16_index,
    geq_aux_gain17_index,
    geq_aux_gain18_index,
    geq_aux_gain19_index,
    geq_aux_gain20_index,
    geq_aux_gain21_index,
    geq_aux_gain22_index,
    geq_aux_gain23_index,
    geq_aux_gain24_index,
    geq_aux_gain25_index,
    geq_aux_gain26_index,
    geq_aux_gain27_index,
    geq_aux_gain28_index,
    geq_aux_gain29_index,
    geq_aux_gain30_index,
    geq_aux_gain31_index,
    geq_aux_gain32_index,
    geq_spk_onoff_index,
    geq_spk_cnt_index,
    geq_spk_freq1_index,
    geq_spk_freq2_index,
    geq_spk_freq3_index,
    geq_spk_freq4_index,
    geq_spk_freq5_index,
    geq_spk_freq6_index,
    geq_spk_freq7_index,
    geq_spk_freq8_index,
    geq_spk_freq9_index,
    geq_spk_freq10_index,
    geq_spk_freq11_index,
    geq_spk_freq12_index,
    geq_spk_freq13_index,
    geq_spk_freq14_index,
    geq_spk_freq15_index,
    geq_spk_freq16_index,
    geq_spk_freq17_index,
    geq_spk_freq18_index,
    geq_spk_freq19_index,
    geq_spk_freq20_index,
    geq_spk_freq21_index,
    geq_spk_freq22_index,
    geq_spk_freq23_index,
    geq_spk_freq24_index,
    geq_spk_freq25_index,
    geq_spk_freq26_index,
    geq_spk_freq27_index,
    geq_spk_freq28_index,
    geq_spk_freq29_index,
    geq_spk_freq30_index,
    geq_spk_freq31_index,
    geq_spk_freq32_index,
    geq_spk_gain1_index,
    geq_spk_gain2_index,
    geq_spk_gain3_index,
    geq_spk_gain4_index,
    geq_spk_gain5_index,
    geq_spk_gain6_index,
    geq_spk_gain7_index,
    geq_spk_gain8_index,
    geq_spk_gain9_index,
    geq_spk_gain10_index,
    geq_spk_gain11_index,
    geq_spk_gain12_index,
    geq_spk_gain13_index,
    geq_spk_gain14_index,
    geq_spk_gain15_index,
    geq_spk_gain16_index,
    geq_spk_gain17_index,
    geq_spk_gain18_index,
    geq_spk_gain19_index,
    geq_spk_gain20_index,
    geq_spk_gain21_index,
    geq_spk_gain22_index,
    geq_spk_gain23_index,
    geq_spk_gain24_index,
    geq_spk_gain25_index,
    geq_spk_gain26_index,
    geq_spk_gain27_index,
    geq_spk_gain28_index,
    geq_spk_gain29_index,
    geq_spk_gain30_index,
    geq_spk_gain31_index,
    geq_spk_gain32_index,
    geq_hp_onoff_index,
    geq_hp_cnt_index,
    geq_hp_freq1_index,
    geq_hp_freq2_index,
    geq_hp_freq3_index,
    geq_hp_freq4_index,
    geq_hp_freq5_index,
    geq_hp_freq6_index,
    geq_hp_freq7_index,
    geq_hp_freq8_index,
    geq_hp_freq9_index,
    geq_hp_freq10_index,
    geq_hp_freq11_index,
    geq_hp_freq12_index,
    geq_hp_freq13_index,
    geq_hp_freq14_index,
    geq_hp_freq15_index,
    geq_hp_freq16_index,
    geq_hp_freq17_index,
    geq_hp_freq18_index,
    geq_hp_freq19_index,
    geq_hp_freq20_index,
    geq_hp_freq21_index,
    geq_hp_freq22_index,
    geq_hp_freq23_index,
    geq_hp_freq24_index,
    geq_hp_freq25_index,
    geq_hp_freq26_index,
    geq_hp_freq27_index,
    geq_hp_freq28_index,
    geq_hp_freq29_index,
    geq_hp_freq30_index,
    geq_hp_freq31_index,
    geq_hp_freq32_index,
    geq_hp_gain1_index,
    geq_hp_gain2_index,
    geq_hp_gain3_index,
    geq_hp_gain4_index,
    geq_hp_gain5_index,
    geq_hp_gain6_index,
    geq_hp_gain7_index,
    geq_hp_gain8_index,
    geq_hp_gain9_index,
    geq_hp_gain10_index,
    geq_hp_gain11_index,
    geq_hp_gain12_index,
    geq_hp_gain13_index,
    geq_hp_gain14_index,
    geq_hp_gain15_index,
    geq_hp_gain16_index,
    geq_hp_gain17_index,
    geq_hp_gain18_index,
    geq_hp_gain19_index,
    geq_hp_gain20_index,
    geq_hp_gain21_index,
    geq_hp_gain22_index,
    geq_hp_gain23_index,
    geq_hp_gain24_index,
    geq_hp_gain25_index,
    geq_hp_gain26_index,
    geq_hp_gain27_index,
    geq_hp_gain28_index,
    geq_hp_gain29_index,
    geq_hp_gain30_index,
    geq_hp_gain31_index,
    geq_hp_gain32_index,
    geq_rec_onoff_index,
    geq_rec_cnt_index,
    geq_rec_freq1_index,
    geq_rec_freq2_index,
    geq_rec_freq3_index,
    geq_rec_freq4_index,
    geq_rec_freq5_index,
    geq_rec_freq6_index,
    geq_rec_freq7_index,
    geq_rec_freq8_index,
    geq_rec_freq9_index,
    geq_rec_freq10_index,
    geq_rec_freq11_index,
    geq_rec_freq12_index,
    geq_rec_freq13_index,
    geq_rec_freq14_index,
    geq_rec_freq15_index,
    geq_rec_freq16_index,
    geq_rec_freq17_index,
    geq_rec_freq18_index,
    geq_rec_freq19_index,
    geq_rec_freq20_index,
    geq_rec_freq21_index,
    geq_rec_freq22_index,
    geq_rec_freq23_index,
    geq_rec_freq24_index,
    geq_rec_freq25_index,
    geq_rec_freq26_index,
    geq_rec_freq27_index,
    geq_rec_freq28_index,
    geq_rec_freq29_index,
    geq_rec_freq30_index,
    geq_rec_freq31_index,
    geq_rec_freq32_index,
    geq_rec_gain1_index,
    geq_rec_gain2_index,
    geq_rec_gain3_index,
    geq_rec_gain4_index,
    geq_rec_gain5_index,
    geq_rec_gain6_index,
    geq_rec_gain7_index,
    geq_rec_gain8_index,
    geq_rec_gain9_index,
    geq_rec_gain10_index,
    geq_rec_gain11_index,
    geq_rec_gain12_index,
    geq_rec_gain13_index,
    geq_rec_gain14_index,
    geq_rec_gain15_index,
    geq_rec_gain16_index,
    geq_rec_gain17_index,
    geq_rec_gain18_index,
    geq_rec_gain19_index,
    geq_rec_gain20_index,
    geq_rec_gain21_index,
    geq_rec_gain22_index,
    geq_rec_gain23_index,
    geq_rec_gain24_index,
    geq_rec_gain25_index,
    geq_rec_gain26_index,
    geq_rec_gain27_index,
    geq_rec_gain28_index,
    geq_rec_gain29_index,
    geq_rec_gain30_index,
    geq_rec_gain31_index,
    geq_rec_gain32_index,

    // PEQ
    peq_mic_onoff_index,
    peq_mic_cnt_index,
    peq_mic_freq1_index,
    peq_mic_freq2_index,
    peq_mic_freq3_index,
    peq_mic_freq4_index,
    peq_mic_freq5_index,
    peq_mic_freq6_index,
    peq_mic_freq7_index,
    peq_mic_freq8_index,
    peq_mic_gain1_index,
    peq_mic_gain2_index,
    peq_mic_gain3_index,
    peq_mic_gain4_index,
    peq_mic_gain5_index,
    peq_mic_gain6_index,
    peq_mic_gain7_index,
    peq_mic_gain8_index,
    peq_mic_filter1_index,
    peq_mic_filter2_index,
    peq_mic_filter3_index,
    peq_mic_filter4_index,
    peq_mic_filter5_index,
    peq_mic_filter6_index,
    peq_mic_filter7_index,
    peq_mic_filter8_index,
    peq_mic_qval1_index,
    peq_mic_qval2_index,
    peq_mic_qval3_index,
    peq_mic_qval4_index,
    peq_mic_qval5_index,
    peq_mic_qval6_index,
    peq_mic_qval7_index,
    peq_mic_qval8_index,

    peq_aux_onoff_index,
    peq_aux_cnt_index,
    peq_aux_freq1_index,
    peq_aux_freq2_index,
    peq_aux_freq3_index,
    peq_aux_freq4_index,
    peq_aux_freq5_index,
    peq_aux_freq6_index,
    peq_aux_freq7_index,
    peq_aux_freq8_index,
    peq_aux_gain1_index,
    peq_aux_gain2_index,
    peq_aux_gain3_index,
    peq_aux_gain4_index,
    peq_aux_gain5_index,
    peq_aux_gain6_index,
    peq_aux_gain7_index,
    peq_aux_gain8_index,
    peq_aux_filter1_index,
    peq_aux_filter2_index,
    peq_aux_filter3_index,
    peq_aux_filter4_index,
    peq_aux_filter5_index,
    peq_aux_filter6_index,
    peq_aux_filter7_index,
    peq_aux_filter8_index,
    peq_aux_qval1_index,
    peq_aux_qval2_index,
    peq_aux_qval3_index,
    peq_aux_qval4_index,
    peq_aux_qval5_index,
    peq_aux_qval6_index,
    peq_aux_qval7_index,
    peq_aux_qval8_index,

    peq_spk_onoff_index,
    peq_spk_cnt_index,
    peq_spk_freq1_index,
    peq_spk_freq2_index,
    peq_spk_freq3_index,
    peq_spk_freq4_index,
    peq_spk_freq5_index,
    peq_spk_freq6_index,
    peq_spk_freq7_index,
    peq_spk_freq8_index,
    peq_spk_gain1_index,
    peq_spk_gain2_index,
    peq_spk_gain3_index,
    peq_spk_gain4_index,
    peq_spk_gain5_index,
    peq_spk_gain6_index,
    peq_spk_gain7_index,
    peq_spk_gain8_index,
    peq_spk_filter1_index,
    peq_spk_filter2_index,
    peq_spk_filter3_index,
    peq_spk_filter4_index,
    peq_spk_filter5_index,
    peq_spk_filter6_index,
    peq_spk_filter7_index,
    peq_spk_filter8_index,
    peq_spk_qval1_index,
    peq_spk_qval2_index,
    peq_spk_qval3_index,
    peq_spk_qval4_index,
    peq_spk_qval5_index,
    peq_spk_qval6_index,
    peq_spk_qval7_index,
    peq_spk_qval8_index,

    peq_hp_onoff_index,
    peq_hp_cnt_index,
    peq_hp_freq1_index,
    peq_hp_freq2_index,
    peq_hp_freq3_index,
    peq_hp_freq4_index,
    peq_hp_freq5_index,
    peq_hp_freq6_index,
    peq_hp_freq7_index,
    peq_hp_freq8_index,
    peq_hp_gain1_index,
    peq_hp_gain2_index,
    peq_hp_gain3_index,
    peq_hp_gain4_index,
    peq_hp_gain5_index,
    peq_hp_gain6_index,
    peq_hp_gain7_index,
    peq_hp_gain8_index,
    peq_hp_filter1_index,
    peq_hp_filter2_index,
    peq_hp_filter3_index,
    peq_hp_filter4_index,
    peq_hp_filter5_index,
    peq_hp_filter6_index,
    peq_hp_filter7_index,
    peq_hp_filter8_index,
    peq_hp_qval1_index,
    peq_hp_qval2_index,
    peq_hp_qval3_index,
    peq_hp_qval4_index,
    peq_hp_qval5_index,
    peq_hp_qval6_index,
    peq_hp_qval7_index,
    peq_hp_qval8_index,

    peq_rec_onoff_index,
    peq_rec_cnt_index,
    peq_rec_freq1_index,
    peq_rec_freq2_index,
    peq_rec_freq3_index,
    peq_rec_freq4_index,
    peq_rec_freq5_index,
    peq_rec_freq6_index,
    peq_rec_freq7_index,
    peq_rec_freq8_index,
    peq_rec_gain1_index,
    peq_rec_gain2_index,
    peq_rec_gain3_index,
    peq_rec_gain4_index,
    peq_rec_gain5_index,
    peq_rec_gain6_index,
    peq_rec_gain7_index,
    peq_rec_gain8_index,
    peq_rec_filter1_index,
    peq_rec_filter2_index,
    peq_rec_filter3_index,
    peq_rec_filter4_index,
    peq_rec_filter5_index,
    peq_rec_filter6_index,
    peq_rec_filter7_index,
    peq_rec_filter8_index,
    peq_rec_qval1_index,
    peq_rec_qval2_index,
    peq_rec_qval3_index,
    peq_rec_qval4_index,
    peq_rec_qval5_index,
    peq_rec_qval6_index,
    peq_rec_qval7_index,
    peq_rec_qval8_index,

    GENERAL_PARAM_MAX,
    MIXER1_BASE_INDEX = GENERAL_PARAM_MAX,
    MIXER2_BASE_INDEX = MIXER1_BASE_INDEX + YM_MIXER_PARAM_MAX,
    BEAM_BASE_INDEX = MIXER2_BASE_INDEX + YM_MIXER_PARAM_MAX,
    BEAM2_BASE_INDEX = BEAM_BASE_INDEX + BEAM_PARAM_MAX,
    BEAM3_BASE_INDEX = BEAM2_BASE_INDEX + BEAM_PARAM_MAX,
    BEAM4_BASE_INDEX = BEAM3_BASE_INDEX + BEAM_PARAM_MAX,
    BEAM5_BASE_INDEX = BEAM4_BASE_INDEX + BEAM_PARAM_MAX,
    BEAM6_BASE_INDEX = BEAM5_BASE_INDEX + BEAM_PARAM_MAX,
    BEAM7_BASE_INDEX = BEAM6_BASE_INDEX + BEAM_PARAM_MAX,
    BEAM8_BASE_INDEX = BEAM7_BASE_INDEX + BEAM_PARAM_MAX,
    DATABASE_INDEX_MAX = BEAM8_BASE_INDEX + BEAM_PARAM_MAX,
} database_index;

#endif
