/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-06-16 16:12:50
 * @LastEditTime: 2024-11-25 14:56:41
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @Description:
 * @FilePath: /audio_mixer_rk3308/include/ym_utilities.h
 * Copyright (c) 2006-2024, Yinkman Development Team
 */
#ifndef __UTILITIES__MY__
#define __UTILITIES__MY__

#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#define UNUSED(x) (void)(x)

#define POSSIBLY_UNUSED __attribute__((unused))

#define __weak __attribute__((weak))

#define ARRAY_SIZE(x) (sizeof(x) / sizeof((x)[0]))

extern uint32_t tune_key_status;
extern uint32_t disable_key_tune;
int tune_key_init(void);

void *aligned_malloc(size_t required_bytes, size_t alignment);
void aligned_free(void *p2);

typedef enum
{
    STOP_TUNE = 0,
    AEC_ENERGY,
    AEC_TUNE,
    AFC_TUNE,
    AEC_AFC_TUNE
} AEC_AFC_TUNE_MODE;



int store_param(void);

int store_mode(void);

int set_mixer_mode(int mode);

int mode_index_set(int mode);
int mode_index_get(void);

int nonvolatile_global_cfg_save(void);
int nonvolatile_global_cfg_read(void);

void master_mixer_lib_param_default(void);

void slave_mixer_lib_param_default(void);

void mastre_mixer_matrix_param_default(void);

void slave_mixer_matrix_param_default(void);

void mixer_params_restore_default(void);



#define MIXER_PARAM_MODE_NAME_LEN (16)
#define MIXER_PARAM_SCREEN_NAME_LEN  (24)

typedef enum
{
    YM_MIXER_ONLY_AEC_MODE,
    YM_MIXER_AEC_AND_AFC_MODE,
    YM_MIXER_ONLY_AFC_MODE,
    YM_MIXER_AUDIO_CARD_MODE,
    YM_MIXER_USR1_MODE,
    YM_MIXER_USR2_MODE,
    YM_MIXER_USR3_MODE,
    YM_MIXER_USR4_MODE,
    YM_MIXER_MAX_MODE
} mixerListenMode_t;

typedef struct __attribute__((packed))
{
    uint32_t database_version;
    uint32_t mode;
    uint32_t tune_mode_loop_max;
} config_ctrl_def;

typedef struct tagnonvolatile_global_cfg_t
{
    uint8_t mode_name[YM_MIXER_MAX_MODE][MIXER_PARAM_MODE_NAME_LEN];
    uint8_t touch_screen_manu_name[MIXER_PARAM_SCREEN_NAME_LEN];
    uint8_t touch_screen_school_name[MIXER_PARAM_SCREEN_NAME_LEN];
    uint8_t usb_audio_class_name[MIXER_PARAM_SCREEN_NAME_LEN];
} nonvolatile_global_cfg_t, *pnonvolatile_global_cfg_t;

extern config_ctrl_def gconfig_ctrl;
#define NONVOLATILE_GLOBAL_CFG_PATH    "/userdata/nonvolatile_global_cfg"
extern nonvolatile_global_cfg_t nonvolatile_global_cfg;

void resume_mixer(void);
void pause_mixer(void);

typedef enum
{
    tuning_fms_idle_state = 1,         // 空闲状态
    tuning_fms_check_mic_state,        // 检测mic状态
    tuning_fms_check_spk_state,        // 检测spk状态
    tuning_fms_just_spk_gain_state,    // 调整spk相位状态1
    tuning_fms_just_phase_state,       // 调整spk相位状态
    tuning_fms_mixer_aec_state,        // mixer库测量aec延迟状态
    tuning_fms_mixer_aec_energy_state, // mixer库测量aec延迟状态
    tuning_fms_mixer_afc_state,        // mixer库afc状态
} tuning_fms_state_id;

typedef enum
{
    tuning_stop_event,
    tuning_mic_check_event,
    tuning_spk_check_event,
    tuning_just_spk_gain_event,
    tuning_just_spk_phase_event,
    tuning_aec_event,
    tuning_aec_energy_event,
    tuning_afc_event,
    tuning_done_event,
    fms_evnet_timeout = 0xffffffff,
} tuning_fms_event;

typedef enum
{
    tuning_result_success = 0x00,
    tuning_result_no_mic = 0x01,
    tuning_result_dac_fail = 0x02,
    tuning_result_no_spk = 0x04,
    tuning_result_aec_fail = 0x20,
    tuning_result_in_tuning = 0x40,
    tuning_result_afc_fail = 0x80,
} tuning_result;

void mixer_lib_lock(int ms);
void mixer_lib_unlock(int ms);

void led_toggle(void);

void update_slave_via_a2b(void);

uint16_t drv_crc_16_ccitt_init_value(uint16_t crc, const uint8_t *data, uint32_t num);

void padFile(const char *filePath, size_t alignSize);

void printProgressBar(int progress, int total);

int get_uac_card(void);

int is_that_card(int card, const char *name);

typedef enum
{
    led_gate,

#ifndef SPK_MUTE_GATE_DISABLED
    spk_mute_gate,
#endif

#ifndef HP_MUTE_GATE_DISABLED
    hp_mute_gate,
#endif

#ifndef REC_MUTE_GATE_DISABLED
    rec_mute_gate,
#endif

#ifdef PA_MUTE_GATE_ENABLED
    pa_mute_gate,
#endif

    gpio_max,
} gpio_index_def;

void set_gpio(gpio_index_def index, int value);
int get_gpio(gpio_index_def index);



// enum
// {
//     BEAM_TRACK_MODE_FIX,
//     BEAM_TRACK_MODE_AUTO,
//     BEAM_TRACK_MODE_AEC_SINGLE,
//     BEAM_TRACK_MODE_AUTO_MIXER,
//     BEAM_TRACK_MODE_DOWN_MIX,
//     BEAM_TRACK_MODE_MULTI_OBJ,
//     BEAM_TRACK_MODE_MAX,
// };

// int check_upgrade_file(void);

void get_mac_addr(uint8_t *data);
void get_net_config(uint8_t *data);

void recored_start_count(void);

// void dwin_test(void);

// uint16_t get_dwin_test_flag(void);
// uint8_t dw_name_play(uint8_t type, uint8_t* buf, uint8_t len);
// int dw_update_256k(void *dw_write_256k);
// uint8_t dw_update_display(void);
// uint8_t send_cmd_reset_dwin(void);
// uint8_t dw_version_display(uint8_t *buf);
// void send_read_dwin_version_cmd(void);


int set_fd_noblock(int fd);



#endif
