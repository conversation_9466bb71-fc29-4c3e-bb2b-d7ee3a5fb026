/*
 Copyright 2022 Yinkman Technology (Beijing)
*/

#ifndef MCU_RESAMPLE_INCLUDED
#define MCU_RESAMPLE_INCLUDED

#ifdef __cplusplus
extern "C" {
#endif  /* __cplusplus */
 
#define MCU_RESAMPLE_MAX_CHANNEL_NUM       (8)

#define MCU_RESAMPLE_MAX_SRC_RATIO         (1.1f)
#define MCU_RESAMPLE_MIN_SRC_RATIO         (0.9f)

#define MCU_RESAMPLE_WRONG_PARAMS          (-1)
#define MCU_RESAMPLE_NULL_POINTER          (-2)

/* Get the memory requirement for resample module */
/* block_size: the input block size for processing
** channel_num: number of channels
** p_sta_size: the static buffer size
** p_dyn_size: the dynamic memory size
** return --> could be 0 for no error or any error code above
*/
int mcu_resample_querymem(unsigned int block_size, unsigned int channel_num, unsigned int *p_sta_size, unsigned int *p_dyn_size);

/* Init the instance for resample module */
/* highQuality: the quality variable, non-zero for high quality
** block_size: the input block size for processing
** channel_num: number of channels
** p_static: pointer to the static buffer
** p_scratch: pointer to the dynamic buffer
*/
void *mcu_resample_open(int highQuality, unsigned int block_size, unsigned int channel_num, void *p_static, void *p_scratch);

/* Get the maximum filter sample width */
int mcu_resample_get_filter_width(const void *handle);

/* do resample for one block of data
** handle: the resample instance handle
** inBuffer:  point to the start address for one block input
** outBuffer:  point to the start address for one block output
** outBufferLen: output buffer size
** inOffset: the offset between the input channel samples, i.e. the samples between the neighboring left channel samples
** outOffset: the offset between the output channel samples, i.e. the samples between the neighboring left channel samples
** return --> could be 0 for no error or any error code above
*/
int mcu_resample_process(void* handle,
                         int*  inBuffer,
                         int*  outBuffer,
                         unsigned int outBufferLen,
                         int inOffset,
                         int outOffset);

int mcu_resample_process_short(void* handle,
                               short*  inBuffer,
                               short*  outBuffer,
                               unsigned int outBufferLen,
                               int inOffset,
                               int outOffset);

/* Update the resample ratio 
** factor: the resample ratio between 0.9 and 1.1
** handle: the resample instance handle
** return zero for no error case, else error code
*/
int mcu_setSrcFactor(float factor, void *handle);

/* Clear the residual buffer
** handle: the resample instance handle
** return zero for no error case, else error code
*/
int mcu_Src_clear_residual(void *handle);

/* Copy Src Factor
** handle: the resample instance handle
** return zero for no error case, else error code
*/
int ym_copySrcFactor(void *handle);

#ifdef __cplusplus
}  /* extern "C" */
#endif  /* __cplusplus */

#endif /* MCU_RESAMPLE_INCLUDED */

