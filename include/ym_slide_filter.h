#ifndef _YM_SLIDE_FILTER_H_
#define _YM_SLIDE_FILTER_H_

#include <stdint.h>

typedef struct sliding_average_filter
{
    int32_t w_size;
    int32_t head;
    int32_t count;
    float sum;
    float *cache;
} sliding_average_filter_t, *sliding_average_filter_p;

/**
 * @brief: 初始化滑动平均滤波器
 *
 * @param filter 滤波器指针
 * @param w_size 窗口大小
 * @return int32_t 0: 成功 -1: 失败
 */
int32_t sliding_average_filter_init(sliding_average_filter_p filter, int32_t w_size);

/**
 * @brief: 创建滑动平均滤波器
 *
 * @param w_size 窗口大小
 * @return sliding_average_filter_p 滤波器指针
 */
sliding_average_filter_p sliding_average_filter_create(int32_t w_size);

/**
 * @brief: 销毁滑动平均滤波器
 *
 * @param filter 滤波器指针
 */
void sliding_average_filter_destroy(sliding_average_filter_p filter);

/**
 * @brief: 滑动平均滤波器
 *
 * @param filter 滤波器指针
 * @param k 新数据
 * @return float 滤波后数据
 */
float sliding_average_filter(sliding_average_filter_p filter, float k);

/**
 * @brief: 重置滑动平均滤波器
 *
 * @param filter 滤波器指针
 */
void sliding_average_filter_reset(sliding_average_filter_p filter);

#endif /* _YM_SLIDE_FILTER_H_ */
