#ifndef _YM_MIXER_PLATFORM_H_
#define _YM_MIXER_PLATFORM_H_

#include <semaphore.h>
#include <stdatomic.h>
#include <stdint.h>

#include "ym_stream.h"
#include "ym_startup.h"
#include "ym_utilities.h"
#include "ym_mixer_beam.h"
#include "ym_slide_filter.h"

typedef int32_t (*pstream_chn_remap)(int32_t **p_ch, int32_t num);
typedef int32_t (*pbeam_proc_callback)(int32_t argc, void *argv);

typedef struct mixer_stream_out
{
    const int32_t channels;
    const int32_t interleaved;
    const char *name;
    int32_t offset;
    struct yinkman_stream *stream;
    struct mixer_stream_out *next, *prev;
} mixer_stream_out_t, *mixer_stream_out_p;

typedef struct mixer_stream_in
{
    const char *name;
    const int32_t channels;
    int32_t offset;
    struct yinkman_stream *stream;
    uint8_t *data;
    uint32_t mask;

    pthread_mutex_t *mutex;
    atomic_uint *input_mask;
    sem_t *notify_sem;

    struct mixer_stream_in *next, *prev;
} mixer_stream_in_t, *mixer_stream_in_p;

typedef struct mixer_def
{
    const char *name;
    const int32_t ichns;
    const int32_t ochns;
    void *handle;            // lib handle
    pthread_mutex_t pmutex;  // lib mutex
    int32_t **p_mixer_in;        // input channel point
    int32_t **p_mixer_out;       // output channel point
    uint32_t database_base_index; // base index of database
    atomic_uint input_mask;
    sem_t input_notify_sem;
    mixer_stream_in_p stream_in;
    mixer_stream_out_p stream_out;
    struct yinkman_stream *audio_out_stream;
    sliding_average_filter_p load_filter;
    sliding_average_filter_p input_sig_filter;
    sliding_average_filter_p output_sig_filter;
    pstream_chn_remap stream_in_chn_remap;
    pstream_chn_remap stream_out_chn_remap;
    pbeam_proc_callback beam_proc_callback;
    pbeam_proc_callback beam_sem_callback;

    database_value *max_sig_level;
    int32_t **p_mixer_in_bak;
    int32_t **p_mixer_out_bak;
    latency_e latency_id;
} mixer_def_t, *mixer_def_p;

#define MIXER_DEF_INIT(x, _icnhs, _ochns)    \
    struct mixer_def x = {                   \
        .name = #x,                          \
        .ichns = (_icnhs),                   \
        .ochns = (_ochns),                   \
        .handle = NULL,                      \
        .pmutex = PTHREAD_MUTEX_INITIALIZER, \
        .p_mixer_in = NULL,                  \
        .p_mixer_out = NULL,                 \
        .database_base_index = 0,            \
        .input_mask = 0,                     \
        .input_notify_sem = {{0}},           \
        .stream_in = NULL,                   \
        .stream_out = NULL,                  \
        .audio_out_stream = NULL,            \
        .load_filter = NULL,                 \
        .input_sig_filter = NULL,            \
        .output_sig_filter = NULL,           \
        .stream_in_chn_remap = NULL,         \
        .stream_out_chn_remap = NULL,        \
        .max_sig_level = NULL,               \
        .p_mixer_in_bak = NULL,              \
        .p_mixer_out_bak = NULL,             \
    }

extern mixer_def_t master_mixer;
extern mixer_def_t slave_mixer;

void mixer_stream_in_init(mixer_stream_in_p pstream, mixer_def_p pmixer);

void mixer_wait_audio_in(mixer_def_p pmixer);
void mixer_audio_in_free(mixer_def_p pmixer);

int32_t mixer_out_stream_alloc(mixer_def_p pmixer);

void mixer_stream_in_chn_remap_register(pstream_chn_remap callback);
void mixer_stream_out_chn_remap_register(pstream_chn_remap callback);
void beam_process_callback_register(pbeam_proc_callback callback, int32_t m_s);
void beam_semphore_callback_register(pbeam_proc_callback callback, int32_t m_s);

#endif /* _YM_MIXER_PLATFORM_H_ */
