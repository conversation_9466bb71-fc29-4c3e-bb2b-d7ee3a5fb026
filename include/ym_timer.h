#ifndef _YM_TIMER_H_
#define _YM_TIMER_H_

#include <stdint.h>
#include <time.h>
#include <pthread.h>
#include <stdbool.h>

/**
 * @brief timer callback function
 *
 * @param parameter timer parameter
 */
typedef void (*ym_timer_cb_t)(void *parameter);

/**
 * @brief timer structure
 *
 * @param name timer name
 * @param timeout_func timeout function
 * @param parameter timeout function parameter
 * @param init_tick timer init tick
 * @param timeout_tick timer timeout tick
 * @param is_one_shot timer one shot or not
 * @param is_actieved timer actieve or not
 */
typedef struct ym_timer
{
    const char *name;                      /**< timeout name */
    ym_timer_cb_t timeout_func;            /**< timeout function */
    void *parameter;                       /**< timeout function's parameter */
    uint64_t init_tick;                    /**< timer init tick */
    uint64_t timeout_tick;                 /**< timer timeout tick */
    uint32_t is_one_shot;                  /**< timer one shot or not */
    bool is_actieved;                  /**< timer actieve or not */
    pthread_mutex_t mutex;
    struct ym_timer *next, *prev;
    struct ym_timer *all_next, *all_prev;
} ym_timer_t, *ym_timer_p;

/**
 * @brief create a timer
 *
 * @param name timer name
 * @param timeout_func timeout function
 * @param param timeout function parameter
 * @param is_one_shot one shot or not
 * @param timeout_tick_ms timeout tick ms
 * @return ym_timer_p timer pointer
 */
ym_timer_p ym_timer_create(const char *name,
                           ym_timer_cb_t timeout_func,
                           void *param,
                           uint32_t is_one_shot,
                           uint32_t timeout_tick_ms);

/**
 * @brief start a timer
 *
 * @param ptimer timer pointer
 * @return int32_t 0: success, -1: fail
 */
int32_t ym_timer_start(ym_timer_p ptimer);

/**
 * @brief stop a timer
 *
 * @param ptimer timer pointer
 * @return int32_t 0: success, -1: fail
 */
int32_t ym_timer_stop(ym_timer_p ptimer);

/**
 * @brief destory a timer
 *
 * @param ptimer timer pointer
 */
void ym_timer_destory(ym_timer_p ptimer);

/**
 * @brief set timer timeout tick
 *
 * @param ptimer timer pointer
 * @param tick timeout tick ms
 * @return int32_t 0: success, -1: fail
 */
int32_t ym_timer_set_time(ym_timer_p ptimer, uint32_t tick);

/**
 * @brief get current tick
 *
 * @return uint64_t current tick
 */
uint64_t ym_tick_get(void);

/**
 * @brief get current tick us
 *
 * @return uint64_t current tick us
 */
uint64_t ym_tick_get_us(void);

/**
 * @brief get current tick ns
 *
 * @return uint64_t current tick ns
 */
uint64_t ym_tick_get_ns(void);

/**
 * @brief convert date to string
 *
 * @param inputDate input date
 * @param outputDate output date
 */
void convertDate(const char *inputDate, char *outputDate);

/**
 * @brief convert date time to struct tm
 *
 * @param dateStr date string
 * @param timeStr time string
 * @return struct tm* struct tm pointer
 */
struct tm *convertDateTime(const char *dateStr, const char *timeStr);

/**
 * @brief timer thread handle
 *
 * @param arg thread argument
 * @return void* thread return value
 */
void *ym_timer_thread_handle(void *arg);

#endif /* _YM_TIMER_H_ */
