#ifndef _YM_PID_CONTROL_H_
#define _YM_PID_CONTROL_H_

typedef struct pid_control
{
    double p;
    double i;
    double d;
    double integral;
    double err_last;
    double target;    // 目标值
    double threshold; // 小于该阈值，不调节
    double just_area; // 调节范围，范围之外不调节
} pid_control_t, *pid_control_p;

/**
 * @brief: 创建pid控制对象
 *
 * @param p 比例系数
 * @param i 积分系数
 * @param d 微分系数
 * @param target 目标值
 * @param threshold 阈值
 * @param just_area 调节范围
 * @return pid_control_t* pid控制对象
 */
pid_control_p pid_control_create(double p, double i, double d, double target, double threshold, double just_area);

/**
 * @brief: 处理pid控制
 *
 * @param ppid_control pid控制对象
 * @param d 当前值
 * @return double 输出值
 */

double pid_control_process(pid_control_p ppid_control, double d);
/**
 * @brief: 重置pid控制对象
 *
 * @param ppid_control pid控制对象
 */
void pid_control_reset(pid_control_p ppid_control);

/**
 * @brief: 销毁pid控制对象
 *
 * @param ppid_control pid控制对象
 */
void pid_control_destroy(pid_control_p ppid_control);

#endif /* _YM_PID_CONTROL_H_ */
