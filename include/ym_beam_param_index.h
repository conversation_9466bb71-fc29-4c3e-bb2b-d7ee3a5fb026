/*
 * @file: ym_beam_param_index.h
 * @brief: beam parameters database index
 * @copyright: Copyright (c) 2025, Yinkman Inc.
 */

#ifndef _YM_BEAM_PARAM_INDEX_H_
#define _YM_BEAM_PARAM_INDEX_H_

typedef enum
{
    beam_mode_index,
    beam_width_level_index,

    beam_track_mode_index,
    beam_track_x_axis_index,
    beam_track_z_axis_index,

    beam_attack_time_index,
    beam_release_time_index,

    beam_area1_x1_axis_index,
    beam_area2_x1_axis_index,
    beam_area3_x1_axis_index,
    beam_area4_x1_axis_index,
    beam_area5_x1_axis_index,
    beam_area6_x1_axis_index,
    beam_area7_x1_axis_index,
    beam_area8_x1_axis_index,
    beam_area1_y1_axis_index,
    beam_area2_y1_axis_index,
    beam_area3_y1_axis_index,
    beam_area4_y1_axis_index,
    beam_area5_y1_axis_index,
    beam_area6_y1_axis_index,
    beam_area7_y1_axis_index,
    beam_area8_y1_axis_index,
    beam_area1_x2_axis_index,
    beam_area2_x2_axis_index,
    beam_area3_x2_axis_index,
    beam_area4_x2_axis_index,
    beam_area5_x2_axis_index,
    beam_area6_x2_axis_index,
    beam_area7_x2_axis_index,
    beam_area8_x2_axis_index,
    beam_area1_y2_axis_index,
    beam_area2_y2_axis_index,
    beam_area3_y2_axis_index,
    beam_area4_y2_axis_index,
    beam_area5_y2_axis_index,
    beam_area6_y2_axis_index,
    beam_area7_y2_axis_index,
    beam_area8_y2_axis_index,
    beam_area1_enabled_index,
    beam_area2_enabled_index,
    beam_area3_enabled_index,
    beam_area4_enabled_index,
    beam_area5_enabled_index,
    beam_area6_enabled_index,
    beam_area7_enabled_index,
    beam_area8_enabled_index,
    beam_area1_width_index,
    beam_area2_width_index,
    beam_area3_width_index,
    beam_area4_width_index,
    beam_area5_width_index,
    beam_area6_width_index,
    beam_area7_width_index,
    beam_area8_width_index,
    beam_area1_gain_index,
    beam_area2_gain_index,
    beam_area3_gain_index,
    beam_area4_gain_index,
    beam_area5_gain_index,
    beam_area6_gain_index,
    beam_area7_gain_index,
    beam_area8_gain_index,

    beam_process_tick_index,
    beam_process_count_index,

    beam_channel0_out_id_index,
    beam_channel1_out_id_index,

    peq_beamf_onoff_index,
    peq_beamf_cnt_index,
    peq_beamf_freq1_index,
    peq_beamf_freq2_index,
    peq_beamf_freq3_index,
    peq_beamf_freq4_index,
    peq_beamf_freq5_index,
    peq_beamf_freq6_index,
    peq_beamf_freq7_index,
    peq_beamf_freq8_index,
    peq_beamf_gain1_index,
    peq_beamf_gain2_index,
    peq_beamf_gain3_index,
    peq_beamf_gain4_index,
    peq_beamf_gain5_index,
    peq_beamf_gain6_index,
    peq_beamf_gain7_index,
    peq_beamf_gain8_index,
    peq_beamf_filter1_index,
    peq_beamf_filter2_index,
    peq_beamf_filter3_index,
    peq_beamf_filter4_index,
    peq_beamf_filter5_index,
    peq_beamf_filter6_index,
    peq_beamf_filter7_index,
    peq_beamf_filter8_index,
    peq_beamf_qval1_index,
    peq_beamf_qval2_index,
    peq_beamf_qval3_index,
    peq_beamf_qval4_index,
    peq_beamf_qval5_index,
    peq_beamf_qval6_index,
    peq_beamf_qval7_index,
    peq_beamf_qval8_index,

    beam_input1_gain_index,
    beam_input2_gain_index,
    beam_input3_gain_index,
    beam_input4_gain_index,
    beam_input5_gain_index,
    beam_input6_gain_index,
    beam_input7_gain_index,
    beam_input8_gain_index,

    beam_output_gain_index,

    BEAM_PARAM_MAX,
} ym_beam_param_def;

#endif /* _YM_BEAM_PARAM_INDEX_H_ */
