#ifndef _YM_AUDIO_TOOLS_H_
#define _YM_AUDIO_TOOLS_H_

#include <stdint.h>

void audio32_interleaved_to_noninterleaved(const int32_t *input, int32_t *output, int32_t channels, int32_t frames);
void audio32_noninterleaved_to_interleaved(const int32_t *input, int32_t *output, int32_t channels, int32_t frames);
void audio16_interleaved_to_noninterleaved(const int16_t *input, int16_t *output, int32_t channels, int32_t frames);
void audio16_noninterleaved_to_interleaved(const int16_t *input, int16_t *output, int32_t channels, int32_t frames);

void audio_set_chn_map(int32_t *poff[], int32_t *pbuf, int32_t chns, int32_t word);
void audio_set_chn_map16(int16_t *poff[], int16_t *pbuf, int32_t chns, int32_t word);

void generate_sine(void *data, int32_t frames, int32_t is_float, int32_t is_noninterleaved,
                   int32_t rate, uint32_t bytes_in_sample, uint32_t channels,
                   float freq, float amp_of_percentage, double *_phase);

int32_t int2dB(int32_t val);

float mixer_percent_to_db(int32_t vol);
int32_t mixer_db_to_percent(float dB);

void swap16Bits(uint32_t *arr, uint32_t size);

void bit32_to_16(uint8_t *src, uint8_t *dest, uint32_t channels, uint32_t frames);
void bit16_to_32(uint8_t *src, uint8_t *dest, uint32_t channels, uint32_t frames);

void int16_to_float(const int16_t *in, float *out, int32_t len);
void float_to_int16(const float *in, int16_t *out, int32_t len);
void int32_to_float(const int32_t *in, float *out, int32_t len);
void float_to_int32(const float *in, int32_t *out, int32_t len);

#endif /* _YM_AUDIO_TOOLS_H_ */
