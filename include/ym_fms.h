/*
 * @Author: <PERSON><PERSON><PERSON>.An
 * @Date: 2022-05-12 20:37:45
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-10-12 10:43:24
 * @Description: 有限状态机框架
 */
#ifndef __MODULES_FMS_H__
#define __MODULES_FMS_H__

#include <stdio.h>
#include <stdint.h>
#include <semaphore.h>
#include "ym_timer.h"
#include "utlist.h"
#include "ym_utilities.h"

struct _state_node;

#define STATE_TIMEOUT_EVENT 0xffffffff
#define YM_FMS_NAME_MAX (64)

typedef void (*Action)(struct _state_node *node);
typedef void *(*state_task)(void *param);

typedef struct _state_event
{
  struct _state_event *next, *prev;
  uint32_t event;
} state_event, *state_event_t;

// 状态跳转接口，如果当事件匹配时，跳转到所指定状态
typedef struct _state_api
{
  uint32_t event;
  Action event_action; // 特殊事件，跳转到下一状态前先执行，参数为这一节点的指针
  struct _state_node *next_node;
  struct _state_api *next; // 链表，用于节点遍历事件
} state_api, *state_api_t;

// 当状态机当前状态匹配节点状态，并且时间id满足时，跳转下个状态
typedef struct _state_node
{
  Action enter;            // 进入状态首先执行的动作
  Action leave;            // 离开状态时要执行的动作
  Action timeout_callback; // 状态超时动作
  const char *name;        // 节点名称
  state_api_t api;         // 当前状态可以匹配的事件链表
  uint32_t state_timeout;  // 状态计时器，当计时器超时后，进异常状态，0xffffffff，状态不计时
  uint32_t id;
  void *user_data;
  state_task _state_task; // 处于当前状态时被创建的线程函数
} state_node, *state_node_t;

typedef struct _state_machine
{
  ym_timer_p state_timer;
  state_event_t event_queue; // 事件队列
  pthread_mutex_t event_queue_mutex;
  sem_t event_queue_sem;
  void *event_pool;
  state_node_t cur_state;
  void *state_thread_handle; // 状态线程句柄，当前状态的线程句柄
  char name[YM_FMS_NAME_MAX];
} state_machine, *state_machine_t;

/**
 * @description: 创建状态机
 * @param {state_node_t} init_state,状态机初始节点
 * @param {char} *name,状态机名称
 * @return {state_machine_t} fms,NULL失败，其他成功
 * @author: Zhichao.An
 */
state_machine_t fms_create(state_node_t init_state, const char *name);

/**
 * @description: 给状态添加跳转接口
 * @param {state_node_t} node,状态节点
 * @param {state_api_t} api,跳转接口
 * @return {int32_t} 0,添加成功 其他失败
 * @author: Zhichao.An
 */
int32_t state_add_event(state_node_t node, state_api_t api);

/**
 * @description: 向状态机发送事件
 * @param {state_machine_t} fms,状态机指针
 * @param {uint32_t} event,具体事件
 * @return {*}
 * @author: Zhichao.An
 */
void fms_post_event(state_machine_t fms, uint32_t event);

/**
 * @description: 状态机轮训
 * @param {state_machine_t} fms,状态机指针
 * @return {*}
 * @author: Zhichao.An
 */
void fms_poll(state_machine_t fms);

uint32_t get_fms_state_id(state_machine_t fms);

#endif
