/*
 * Copyright (c) 2006-2021, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2011-06-02     <PERSON>      Add finsh_get_prompt function declaration
 */

#ifndef _YM_SHELL_H_
#define _YM_SHELL_H_

#include <stdint.h>

typedef int32_t (*syscall_func)(int32_t argc, char **argv);

/* system call table */
typedef struct finsh_syscall
{
    const char *name;  /* the name of system call */
    const char *desc;  /* description of system call */
    syscall_func func; /* the function address of system call */
} finsh_syscall_t, *finsh_syscall_p;

#define CMD_SECTION __attribute__((unused, section("FSymTab"), aligned(4)))

#define MSH_FUNCTION_EXPORT_CMD(name, cmd, desc)       \
    const char __fsym_##cmd##_name[] = #cmd;           \
    const char __fsym_##cmd##_desc[] = #desc;          \
    const finsh_syscall_t __fsym_##cmd CMD_SECTION = { \
        __fsym_##cmd##_name,                           \
        __fsym_##cmd##_desc,                           \
        (syscall_func)&name };

/**
 * @brief This macro exports a command to module shell.
 *
 * @param command is the name of the command.
 * @param desc is the description of the command, which will show in help list.
 */
#define MSH_CMD_EXPORT(command, desc) \
    MSH_FUNCTION_EXPORT_CMD(command, command, desc)

/**
 * @brief This macro exports a command with alias to module shell.
 *
 * @param command is the name of the command.
 * @param alias is the alias of the command.
 * @param desc is the description of the command, which will show in help list.
 */
#define MSH_CMD_EXPORT_ALIAS(command, alias, desc) \
    MSH_FUNCTION_EXPORT_CMD(command, alias, desc)

int32_t finsh_system_init(void);
void *finsh_thread_entry(void *param);

#endif /* _YM_SHELL_H_ */
