/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-06-20 09:50:27
 * @LastEditTime: 2024-10-12 10:43:35
 * @LastEditors: An<PERSON><PERSON><PERSON>
 * @Description:
 * @FilePath: /audio_mixer_rk3308/include/ym_protocol.h
 * Copyright (c) 2006-2024, Yinkman Development Team
 */
#ifndef __YINKMAN_PROTOCOL_H_
#define __YINKMAN_PROTOCOL_H_

#include <stdint.h>
#include "ym_utilities.h"
#include "uthash.h"

enum
{
    IN_CHN_MIN = (0),
    IN_CHN_MAX = (15),
    OUT_CHN_MIN = (16),
    OUT_CHN_MAX = (31),
    MIC_CHN_MIN = (32),
    MIC_CHN_MAX = (39),
    AUX_CHN_MIN = (40),
    AUX_CHN_MAX = (47),
    DAC_CHN_MIN = (48),
    DAC_CHN_MAX = (63),
    MIC_GLOBAL_VOL = (64),
    OUT_GLOBAL_VOL = (65),
    IN_CHN17 = (66),
    IN_CHN24 = (73),
    ALL_SPK_VOL = (74),
    TUNE_MODE_VOL = 75,
    AEC_OUT_VOL = 76,
    AFC_OUT_VOL = 77,
    ANDROID_OUT_VOL = 78,
    UAC_CAPTURE_VOL = 79,
    IN_CHN_STEP_MIN = (128),
    IN_CHN_STEP_MAX = (143),
    OUT_CHN_STEP_MIN = (144),
    OUT_CHN_STEP_MAX = (159),
    MIC_CHN_STEP_MIN = (160),
    MIC_CHN_STEP_MAX = (167),
    AUX_CHN_STEP_MIN = (168),
    AUX_CHN_STEP_MAX = (175),
    DAC_CHN_STEP_MIN = (176),
    DAC_CHN_STEP_MAX = (191),
    MIC_GLOBAL_MUTE = (192),
    OUT_GLOBAL_MUTE = (193),
    IN_MUTE_CHN17 = (194),
    IN_MUTE_CHN24 = (201),
    ALL_SPEAKER_MUTE = (202),
    TRAILER_VOL_STEP = 203,
    AEC_OUT_VOL_STEP = 204,
    AFC_OUT_VOL_STEP = 205,
    AEC_OUT_MUTE = 204,
    AFC_OUT_MUTE = 205,
    HW_PA_MUTE = 208,
};

enum
{
    AEC_CHN_MIN = (0),
    AEC_CHN_MAX = (15),
    ATMP_CHN_MIN = (16),
    ATMP_CHN_MAX = (31),
    ATMF_CHN_MIN = (32),
    ATMF_CHN_MAX = (47),
    INPUT_CHN17 = (64),
    INPUT_CHN24 = (71),
    ATMP_CHN17_MIN = (80),
    ATMP_CHN24_MAX = (87),
    ATMF_CHN17_MIN = (96),
    ATMF_CHN24_MAX = (103),
};

enum
{
    TEST_SIG_TYPE = (0x00), /* signal type */
    TEST_SIG_FREQ = (0x01), /* signal frequency */
    TEST_SIG_AMP = (0x02),  /* signal amplitude */
};

struct ymlink_stack;

void *eth_protocol_thread_handle(void *arg);
void *uart_protocol_thread_entry(void *arg);

#define YM_PROTO_PAYLOAD_MAX (1024)

/* 0: master(local) node, [1 15]: slave node */
#define YM_PROTO_SLAVE_ID(devid) (((devid) >> 4U) & 0x0FU)
/* [0 15]: the number of core */
#define YM_PROTO_CORE_ID(devid)  ((devid) & 0x0FU)
#define YM_PROTO_DEV_ID(s,c)     ((((s) & 0x0FU) << 4U) + ((c) & 0x0FU))

struct ymlink_desc;

extern struct ymlink_desc ym;

typedef void *(*handle_callback_def)(uint8_t device, uint8_t *data);

typedef struct ymlink_message
{
    uint8_t header1; // Header of packet
    uint8_t header2; // Header of packet
    uint8_t dev_id;  // device ID
    uint8_t command; // Command
    uint8_t payload[YM_PROTO_PAYLOAD_MAX];
    uint8_t tail1; // Tail of packet
    uint8_t tail2; // Tail of packet
    uint16_t len;
    struct ymlink_message *next, *prev;
} __attribute__((packed)) ymlink_message_t, *ymlink_message_p;

typedef enum
{
    ym_status_wait_head1,
    ym_status_wait_head2,
    ym_status_wait_devid,
    ym_status_wait_cmd,
    ym_status_wait_tail1,
    ym_status_wait_tail2
} ymlink_parse_status;

typedef struct handle_callback_list
{
    handle_callback_def callback;
    struct handle_callback_list *next, *prev;
} handle_callback_list_t, *handle_callback_list_p;

typedef struct ymlink_callback_def
{
    int command;
    handle_callback_list_p callback_list;
    UT_hash_handle hh; /* makes this structure hashable */
} ymlink_callback_def_t, *ymlink_callback_def_p;

typedef int (*special_status_change_handle)(ymlink_message_p msg, struct ymlink_stack *ym_stack);

typedef struct ymlink_status_change_def
{
    int command;
    special_status_change_handle callback;
    UT_hash_handle hh; /* makes this structure hashable */
} ymlink_status_change_def_t, *ymlink_status_change_def_p;

/**
 * @description: 用于描述当前实现的具体协议
 * @handle_head: 实现具体命令回调的hash list头指针
 * @special_status_change: 用于检测特殊协议是否结束
 * @message_pool: 用于回复协议的内存池，注意释放(尤其是网络通信)
 */
struct ymlink_desc
{
    ymlink_callback_def_p handle_head;
    ymlink_status_change_def_p special_status_change;
    void *message_pool;
};

/**
 * @description: 协议栈的描述信息
 * @parse_status: 当前协议栈状态
 * @rx_pos: 当前接收位置
 * @cur_check: 当前特殊检测函数
 * @msg: 当前接收的msg
 * @c: 当前接收的字符
 */
struct ymlink_stack
{
    ymlink_parse_status parse_status;
    uint32_t rx_pos;
    ymlink_status_change_def_p cur_check;
    ymlink_message_t msg;
    uint8_t c;
};

#define YMLINK_MESSAGE_HEAD(msg)  \
    do                            \
    {                             \
        (msg)->payload[0] = 0x7B; \
        (msg)->payload[1] = 0x7D; \
        (msg)->len = 2;           \
    } while (0)

#define MALLOC_YMLINK_MESSAGE()             \
    ymlink_message_p msg;                   \
    do                                      \
    {                                       \
        msg = ym_mem_pool_alloc(ym.message_pool); \
        if (!msg)                           \
        {                                   \
            log_e("malloc err.");           \
            return NULL;                    \
        }                                   \
        memset(msg->payload, 0, 128);       \
    } while (0)

#define FREE_YMLINK_MESSAGE()             \
    do                                    \
    {                                     \
        ym_mem_pool_free(ym.message_pool, msg); \
    } while (0)

#define YMLINK_MESSAGE_TAIL(msg)             \
    do                                       \
    {                                        \
        (msg)->payload[(msg)->len++] = 0x7D; \
        (msg)->payload[(msg)->len++] = 0x7B; \
    } while (0)

#define YMLINK_MESSAGE_APPEND(msg) \
    return (msg);

void ymlink_register(int command, handle_callback_def callback);

#endif
