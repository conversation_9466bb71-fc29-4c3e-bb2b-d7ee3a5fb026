/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-06-16 09:19:04
 * @LastEditTime: 2024-10-14 16:36:10
 * @LastEditors: Anzhichao
 * @Description:
 * @FilePath: /audio_mixer_rk3308/include/ym_stream.h
 * Copyright (c) 2006-2024, Yinkman Development Team
 */
#ifndef __YINKMAN_STREAM__
#define __YINKMAN_STREAM__

#include <stdio.h>
#include <stdint.h>
#include <linux/types.h>
#include <pthread.h>

#include "ym_utilities.h"
#include "utlist.h"

struct yinkman_stream;

typedef struct yinkman_stream yinkman_stream_t;
typedef struct yinkman_stream *yinkman_stream_p;

typedef void (*yinkman_stream_notify_func)(void *args);

/**
 * @description:
 * @param {yinkman_stream} *stream
 * @param {char} *name
 * @param {size_t} framems
 * @param {size_t} frame_size
 * @return {*}
 * @use:
 */
struct yinkman_stream *yinkman_stream_create(
    const char *name,
    size_t framems,
    size_t frame_size,
    size_t block_count);

/**
 * @description: 一个消费者一个生产者
 * @return {*}
 * @use:
 */
struct yinkman_stream *yinkman_stream_with_sem_create(
    const char *name,
    size_t framems,
    size_t frame_size,
    size_t block_count);

void yinkman_stream_wait(struct yinkman_stream *stream);

void yinkman_stream_trywait(struct yinkman_stream *stream);

uint32_t yinkman_stream_get_package_size(struct yinkman_stream *stream);

uint32_t yinkman_stream_register(struct yinkman_stream *stream,
                                 yinkman_stream_notify_func notify_callback,
                                 void *args);

uint32_t yinkman_stream_register_no_data(struct yinkman_stream *stream,
                                         yinkman_stream_notify_func notify_callback,
                                         void *args);

void yinkman_stream_register_free_callback(struct yinkman_stream *stream,
                                           yinkman_stream_notify_func pull_callback,
                                           void *args);

/**
 * @description: push a package data to stream
 * @param {yinkman_stream} *stream
 * @param {unsigned char} *data,data len must equal framems * frame_size
 * @return {int} -1,push error please free data,0 success don't free data
 * @use:
 */
int yinkman_stream_push(struct yinkman_stream *stream, const void *data);

/**
 * @description: pull a package data from stream
 * @param {yinkman_stream} *stream
 * @param {unsigned char} *
 * @param {uint32_t} mask,register return
 * @param {uint32_t} package_index
 * @return {int} 0 success,1 pull err,
 * @use:
 */
int yinkman_stream_pull(struct yinkman_stream *stream, void **pdata, uint32_t mask);

/**
 * @description: 销毁音频流有巨大的风险，除非明确风险不可能发生，否则不应该调用该API
 * @param {yinkman_stream} *stream
 * @return {none}
 * @use: 只有明确的知道消费者和生产者的关系，才能调用该API。
 */
void yinkman_stream_destroy(struct yinkman_stream *stream);

/**
 * @description: list all stream
 * @return {none}
 * @use:
 */
void yinkman_stream_list(void);

/**
 * @description: yinkman_stream_get_by_name
 * @param {char *} stream name
 * @return {struct yinkman_stream *} NULL or point to stream
 * @use:
 */
struct yinkman_stream *yinkman_stream_get_by_name(const char *name);

/**
 * @description: malloc stream data block
 * @param {yinkman_stream} *stream
 * @return {*}
 * @use:
 */
void *yinkman_stream_malloc(struct yinkman_stream *stream);

void *yinkman_stream_calloc(struct yinkman_stream *stream);

void yinkman_stream_free(struct yinkman_stream *stream, void *data, uint32_t mask);

void yinkman_stream_release_all(struct yinkman_stream *stream);

const char *yinkman_stream_name(struct yinkman_stream *stream);

uint32_t yinkman_stream_get_register_count(struct yinkman_stream *stream);

#endif
