#ifndef _YM_STARTUP_H_
#define _YM_STARTUP_H_

#include <stdint.h>
#include <stddef.h>
#include <stdarg.h>

typedef int32_t (*init_fn_t)(void);

/* system call table */
typedef struct ym_init_syscall
{
    const char *name; /* the name of system call */
    init_fn_t func;   /* the function address of system call */
} ym_init_syscall_t;

#define INIT_SECTION(x) __attribute__((unused, section("ykm_fn" x), aligned(4)))

#define INIT_EXPORT(name, SEC)                                  \
    const char _init_fun_##name[] = #name;                      \
    const ym_init_syscall_t __init_##name INIT_SECTION(SEC) =   \
    {                                                           \
        _init_fun_##name,                                       \
        (init_fn_t) & name                                      \
    };

#define INIT_LOW_LEVEL_EXPORT(fn) INIT_EXPORT(fn, "0")
#define INIT_HW_EXPORT(fn) INIT_EXPORT(fn, "1")
#define INIT_SEM_EXPORT(fn) INIT_EXPORT(fn, "2")
#define INIT_MEM_EXPORT(fn) INIT_EXPORT(fn, "3")
#define INIT_STREAM_EXPORT(fn) INIT_EXPORT(fn, "4")
#define INIT_COMPONENT_EXPORT(fn) INIT_EXPORT(fn, "5")
#define INIT_APP_BEFORE_EXPORT(fn) INIT_EXPORT(fn, "6")
#define INIT_APP_EXPORT(fn) INIT_EXPORT(fn, "7")

#define EXIT_EXPORT(fn) INIT_EXPORT(fn, "8")

/**
 * @brief: 初始化函数
 */
void ym_init(void);

/**
 * @brief: 退出函数
 */
void ym_exit(void);

#endif /* _YM_STARTUP_H_ */
