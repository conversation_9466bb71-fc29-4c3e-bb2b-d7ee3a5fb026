/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-04-18 11:45:55
 * @LastEditTime: 2024-05-11 10:52:07
 * @LastEditors: Anzhicha<PERSON>
 * @Description:
 * @FilePath: /audio_mixer_rk3308/include/ym_thread.h
 * Copyright (c) 2006-2024, Yinkman Development Team
 */

#ifndef _YM_THREAD_H_
#define _YM_THREAD_H_

enum
{
    default_thread_prio = 0,
    elog_thread_prio,
    stdout_thread_prio,
    uart_protocol_thread_prio,
    eth_protocol_thread_prio,
    tuming_fms_thread_prio,
    ym_timer_thread_prio,

    usb_event_thread_prio,
    usb_tx_thread_prio,
    usb_rx_thread_prio,
    usb_host_event_thread_prio,
    usb_host_tx_thread_prio,
    usb_host_rx_thread_prio,

    acodec_in_thread_prio,
    es7210_in_thread_prio,
    beam_in_thread_prio,
    rx4_in_thread_prio,
    rx2_in_thread_prio,
    dummy1_in_thread_prio,
    a2b_in_thread_prio,
    
    acodec_out_thread_prio,
    dummy1_out_thread_prio,
    dummy2_out_thread_prio,
    dummy3_out_thread_prio,
    a2b_out_thread_prio,
    rx4_out_thread_prio,
    rx2_out_thread_prio,
    msusb_out_thread_prio,

    mmixer_thread_prio,
    smixer_thread_prio,

    audio_in_thread_prio,
    audio_process_thread_prio,
    audio_out_thread_prio
};

enum
{
    default_thread_cpuid = 4,
    audio_in_thread_cpuid = 5,
    mmixer_thread_cpuid = 6,
    smixer_thread_cpuid = 7,
    audio_out_thread_cpuid = 5,
};

void *ym_thread_create(const char *name, int core_id, int priority, void *(*__start_routine)(void *), void *__arg);
void ym_thread_run(void *handle);
void ym_thread_kill(void *handle, void **thread_ret);
void ym_thread_destroy(void *handle);
void ym_thread_all_run(void);
void ym_thread_list(void);
int ym_thread_is_running(void *handle);

#endif
