/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-06-12 09:30:14
 * @LastEditTime: 2024-06-12 10:25:20
 * @LastEditors: Anzhi<PERSON><PERSON>
 * @Description: 采样率转换
 * @FilePath: /audio_mixer_rk3308/include/ym_resamplerate.h
 * Copyright (c) 2006-2024, Yinkman Development Team
 */
#ifndef _YM_RESAMPLERATE_H_
#define _YM_RESAMPLERATE_H_

#include <stdint.h>
#include <samplerate.h>
#include <pthread.h>

typedef struct
{
    pthread_mutex_t *mutex;
    SRC_STATE *state;
    SRC_DATA data;

    uint8_t *raw_data;
    float *pf_raw_data;
    int output_buffer_size;
    uint8_t *out_data;
    float *pf_out_data;

    int channels;
    int in_frames;
    int sample_bits;

    int out_frames;
    int in_rate;
    int out_rate;
} samplerate_t, *samplerate_p;

samplerate_p samplerate_new(int channels, int frames, int sample_bits, int in_rate, int out_rate, int quality);

int samplerate_process(samplerate_p p_samplerate);

void samplerate_free(samplerate_p p_samplerate);

#endif // !_YM_RESAMPLERATE_H_
