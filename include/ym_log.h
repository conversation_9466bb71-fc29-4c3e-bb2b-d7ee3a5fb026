#ifndef _YM_LOG_H_
#define _YM_LOG_H_

#include <zlog.h>
#include <stdint.h>
#include <stdbool.h>

extern bool zlog_file_ext2_err;

#define log_i(...)                             \
    do                                         \
    {                                          \
        if(!zlog_file_ext2_err){               \
            dzlog_info(__VA_ARGS__);           \
            printf(__VA_ARGS__);               \
            printf("\n");                      \
        }                                      \
    } while (0)

#define log_d(...)                             \
    do                                         \
    {                                          \
        if(!zlog_file_ext2_err){               \
            dzlog_debug(__VA_ARGS__);          \
            printf(__VA_ARGS__);               \
            printf("\n");                      \
        }                                      \
    } while (0)

#define log_e(...)                             \
    do                                         \
    {                                          \
        if(!zlog_file_ext2_err){               \
            dzlog_error(__VA_ARGS__);          \
            printf(__VA_ARGS__);               \
            printf("\n");                      \
        }                                      \
    } while (0)

#define log_a(...)                             \
    do                                         \
    {                                          \
        if(!zlog_file_ext2_err){               \
            dzlog_alert(__VA_ARGS__);          \
            printf(__VA_ARGS__);               \
            printf("\n");                      \
        }                                      \
    } while (0)

#define log_w(...)                             \
    do                                         \
    {                                          \
        if(!zlog_file_ext2_err){               \
            dzlog_warn(__VA_ARGS__);           \
            printf(__VA_ARGS__);               \
            printf("\n");                      \
        }                                      \
    } while (0)

#endif /* _YM_LOG_H_ */
