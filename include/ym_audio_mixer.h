#ifndef _YM_AUDIO_MIXER_H_
#define _YM_AUDIO_MIXER_H_

#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <gpiod.h>
// #include <tinyalsa/asoundlib.h>
#include <alsa/asoundlib.h>
#include <pthread.h>
#include <time.h>
#include <unistd.h>
#include <signal.h>
#include <semaphore.h>
#include <fcntl.h>
#include <alloca.h>
#include <errno.h>
#include <math.h>

#include "config.h"
#include "ym_shell.h"
#include "ym_startup.h"
#include "ym_database.h"
#include "ym_thread.h"
#include "ym_fms.h"
#include "ym_protocol.h"
#include "ym_mixer_platform.h"
#include "ym_audio_tools.h"
#include "ym_timer.h"
#include "ym_mem_pool.h"
#include "ym_utilities.h"
#include "ym_log.h"
#include "usb_audio.h"
// #include "audio_io.h"

#define GLOBAL_AUDIO_CLOCK_NAME "es7210_src0"

#define INIT_MIXER_STREAM(_name, chn, mixer)            \
    static mixer_stream_in_t _name##_stream_in = {      \
        .name = #_name,                                 \
        .channels = chn,                                \
    };                                                  \
    mixer_stream_in_init(&_name##_stream_in, &(mixer));

#define INIT_MASTER_MIXER_STREAM_IN(_name, chn) \
    INIT_MIXER_STREAM(_name, chn, master_mixer)

#define INIT_SLAVE_MIXER_STREAM_IN(_name, chn) \
    INIT_MIXER_STREAM(_name, chn, slave_mixer)

#if BEAM_INSTANCE_NUMBER > 0
#define INIT_BEAM_MIXER_STREAM_IN(_name, chn) \
    INIT_MIXER_STREAM(_name, chn, beam)
#endif
#if BEAM_INSTANCE_NUMBER > 1
#define INIT_BEAM2_MIXER_STREAM_IN(_name, chn) \
    INIT_MIXER_STREAM(_name, chn, beam2)
#endif
#if BEAM_INSTANCE_NUMBER > 2
#define INIT_BEAM3_MIXER_STREAM_IN(_name, chn) \
    INIT_MIXER_STREAM(_name, chn, beam3)
#endif
#if BEAM_INSTANCE_NUMBER > 3
#define INIT_BEAM4_MIXER_STREAM_IN(_name, chn) \
    INIT_MIXER_STREAM(_name, chn, beam4)
#endif
#if BEAM_INSTANCE_NUMBER > 4
#define INIT_BEAM5_MIXER_STREAM_IN(_name, chn) \
    INIT_MIXER_STREAM(_name, chn, beam5)
#endif
#if BEAM_INSTANCE_NUMBER > 5
#define INIT_BEAM6_MIXER_STREAM_IN(_name, chn) \
    INIT_MIXER_STREAM(_name, chn, beam6)
#endif
#if BEAM_INSTANCE_NUMBER > 6
#define INIT_BEAM7_MIXER_STREAM_IN(_name, chn) \
    INIT_MIXER_STREAM(_name, chn, beam7)
#endif
#if BEAM_INSTANCE_NUMBER > 7
#define INIT_BEAM8_MIXER_STREAM_IN(_name, chn) \
    INIT_MIXER_STREAM(_name, chn, beam8)
#endif

#define INIT_MIXER_STREAM_OUT(_name, _channels, _interleaved, _stream_name) \
    static mixer_stream_out_t _name##_stream_out = {                        \
        .channels = _channels,                                              \
        .interleaved = _interleaved,                                        \
        .name = #_stream_name,                                              \
    };                                                                      \
    _name##_stream_out.stream = yinkman_stream_get_by_name(#_stream_name);

#define INIT_MASTER_MIXER_STREAM_OUT(_name, _channels, _interleaved, _stream_name)      \
    INIT_MIXER_STREAM_OUT(_name, _channels, _interleaved, _stream_name)                 \
    {                                                                                   \
        uint32_t count = 0;                                                             \
        mixer_stream_out_p pos;                                                         \
        DL_FOREACH(master_mixer.stream_out, pos)                                        \
        {                                                                               \
            count += pos->channels;                                                     \
        }                                                                               \
        _name##_stream_out.offset = count;                                              \
    }                                                                                   \
    DL_APPEND(master_mixer.stream_out, &_name##_stream_out);                            \
    {                                                                                   \
        used_output_stream_p used_output_stream = malloc(sizeof(used_output_stream_t)); \
        used_output_stream->name = #_stream_name;                                       \
        DL_APPEND(used_output_stream_list, used_output_stream);                         \
    }

typedef struct used_output_stream
{
    struct used_output_stream *next, *prev;
    char *name;
} used_output_stream_t, *used_output_stream_p;

extern used_output_stream_p used_output_stream_list;

bool is_used_output_stream(const char *name);

#endif // !_YM_AUDIO_MIXER_H_
