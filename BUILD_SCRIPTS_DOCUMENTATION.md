# RK3576音频混音器构建脚本说明文档

本文档详细说明了RK3576音频混音器项目中四个主要构建脚本的功能、用法和工作流程。

## 目录
- [prevbuild.sh - 预构建脚本](#prevbuildsh---预构建脚本)
- [build.sh - 主构建脚本](#buildsh---主构建脚本)
- [buildall.sh - 完整构建脚本](#buildallsh---完整构建脚本)
- [ym_release.sh - 发布脚本](#ym_releasesh---发布脚本)

---

## prevbuild.sh - 预构建脚本

### 功能概述
`prevbuild.sh`是一个产品选择和环境配置脚本，用于在正式构建前选择目标产品并配置构建环境。该脚本支持被其他脚本调用，并能返回选择的产品信息。

### 主要功能
1. **产品发现**: 自动扫描`platform/`目录下的所有可用产品
2. **交互式选择**: 提供用户友好的产品选择界面，支持10秒超时
3. **环境配置**: 将选择的产品信息保存到用户环境变量中
4. **产品信息输出**: 向调用脚本返回选择的产品名称

### 使用方法
```bash
# 交互式选择产品（10秒超时）
./prevbuild.sh

# 直接指定产品编号
./prevbuild.sh [产品编号]

# 被其他脚本调用（如ym_release.sh）
(cd audio_mixer_rk3576 && ./prevbuild.sh $1) > stdout 2> stderr
```

### 工作流程
1. **扫描产品目录**
   - 遍历`platform/*/`目录
   - 排除以`_`开头的目录
   - 生成可用产品列表

2. **显示产品选项**（输出到stderr，避免被调用脚本捕获）
   ```
   Available Products:
   1: audio_mixer_matrix_mic
   2: pcba_test
   3: wrong_board_id
   4: build all boards program
   ```

3. **处理用户输入**
   - 无输入或超时：编译所有产品，`selected_product="all"`
   - 选择具体产品：仅编译该产品，`selected_product="产品名"`
   - 选择"build all"：编译所有产品，`selected_product="all"`
   - 错误输入：退出脚本

4. **环境变量配置**
   - 检查`~/.bashrc`中的`YM_AUDIO_MIXER_COMPILE_PLATFORM`变量
   - 如不存在则添加，存在则更新
   - 导出环境变量供后续脚本使用

5. **产品信息输出**（输出到stdout，供调用脚本捕获）
   ```
   SELECTED_PRODUCT:产品名
   ```

### 输出示例
**交互式使用：**
```
Available Products:
1: audio_mixer_matrix_mic
2: pcba_test
3: wrong_board_id
4: build all boards program
Select platform(timeout 10s): 1
the audio_mixer_matrix_mic will be compiled
SELECTED_PRODUCT:audio_mixer_matrix_mic
```

**被脚本调用时：**
- stderr输出：用户交互信息（显示给用户）
- stdout输出：`SELECTED_PRODUCT:产品名`（供调用脚本解析）

---

## build.sh - 主构建脚本

### 功能概述
`build.sh`是核心构建脚本，负责版本管理、代码编译、文件部署和打包等完整的构建流程。

### 主要功能
1. **版本管理**: Git分支版本解析和验证
2. **代码编译**: 多平台应用程序编译
3. **文件部署**: 将编译结果部署到buildroot目录
4. **打包生成**: 创建OTA升级包

### 使用方法
```bash
# 编译所有平台（默认）
./build.sh
./build.sh all

# 仅安装文件到buildroot
./build.sh install

# 清理构建结果
./build.sh clean

# 指定版本和平台编译
./build.sh all [平台名] [版本号]
```

### 工作流程

#### 1. 版本解析阶段
- 调用`git_info_extract.sh`提取Git仓库信息
- 解析当前分支名，验证是否符合`release_Vx.x.x`格式
- 如不符合，向上搜索父分支寻找符合格式的版本分支
- 生成`scripts/firmware_version`文件

#### 2. 平台配置阶段
- 读取环境变量`YM_AUDIO_MIXER_COMPILE_PLATFORM`
- 根据配置决定编译单个平台还是所有平台
- 设置编译目标列表

#### 3. 编译阶段
- 清理之前的构建结果
- 创建必要的目录结构
- 复制DSP库文件到release目录
- 调用`lib_file_generator.py`生成库文件
- 根据平台配置执行Make编译

#### 4. 部署阶段
- 将编译结果复制到buildroot的app和appbak分区
- 创建符号链接`audio_mixer_app`指向具体平台程序
- 设置可执行权限

#### 5. 打包阶段
- 生成OTA升级包的JSON配置文件
- 计算文件MD5校验和
- 创建`ymupdate.tar.gz`升级包

### 关键函数

#### `app_backup_set()`
```bash
# 功能：备份应用程序到指定目录
# 参数：备份路径、源程序路径
app_backup_set() {
    local bak_path=$1
    local app_src=$2
    # 创建备份目录并复制文件
    # 设置只读权限
}
```

#### `platform_public_install()`
- 安装公共脚本和配置文件到buildroot
- 配置USB自动复制规则
- 设置SSH密钥和日志配置

#### `gen_app_tar()`
- 生成OTA升级包
- 创建包含MD5校验的JSON配置
- 打包为tar.gz格式

### 版本格式要求
- 分支名必须符合：`release_Vx.x.x`格式
- 例如：`release_V1.2.3`
- 版本号将被解析为：主版本.子版本.修订版本

---

## buildall.sh - 完整构建脚本

### 功能概述
`buildall.sh`是最高级别的构建脚本，整合了平台选择、应用编译和固件生成的完整流程。

### 主要功能
1. **完整构建流程**: 从平台选择到固件生成的一站式构建
2. **固件管理**: 自动生成带时间戳的固件版本
3. **文件归档**: 整理和保存构建产物

### 使用方法
```bash
# 完整构建流程
./buildall.sh

# 指定平台构建
./buildall.sh [平台编号]
```

### 工作流程

#### 1. 预构建阶段
```bash
./prevbuild.sh $1
```
- 调用预构建脚本选择平台
- 配置构建环境变量

#### 2. 清理阶段
```bash
cd ..
rm -rf buildroot/output/rockchip_rk3308_b_release/build/ym_audio_mixer-1.0.0
```
- 切换到上级目录
- 清理之前的buildroot构建缓存

#### 3. 主构建阶段
```bash
./build.sh
```
- 调用主构建脚本
- 执行完整的编译和部署流程

#### 4. 固件归档阶段
- 生成时间戳格式：`YYYYMMDDHH`（年月日时）
- 创建输出目录：`./output/firmware/[时间戳]/`
- 复制构建产物：
  - Git信息文件：`git_info_summary.txt`
  - 固件镜像：`audio*.img`
  - 打包文件：`*.tar`
  - 二进制文件：`audio*.bin`

### 输出目录结构
```
./output/firmware/2024102315/
├── git_info_summary.txt      # Git仓库信息
├── audio_mixer_xxx.img       # 固件镜像文件
├── update.tar               # 应用更新包
└── audio_mixer_xxx.bin      # 二进制可执行文件
```

### 时间戳说明
- 格式：`YYYYMMDDHH`
- 示例：`2024102315`表示2024年10月23日15时
- 用于版本管理和固件追踪

---

## ym_release.sh - 发布脚本

### 功能概述
`ym_release.sh`是最高级别的发布脚本，提供完整的SDK根目录检测、产品选择、版本解析、构建执行和发布包生成的一站式解决方案。该脚本具有完善的错误处理机制和彩色输出功能。

### 主要功能
1. **SDK根目录检测**: 确保脚本在正确的SDK根目录下执行
2. **产品选择集成**: 调用prevbuild.sh进行产品选择
3. **版本信息解析**: 从version.h文件解析固件版本和硬件版本
4. **构建执行**: 调用build.sh执行完整构建流程
5. **发布包生成**: 创建包含版本信息的发布包
6. **错误处理**: 完善的错误检测和退出机制
7. **彩色输出**: 错误、警告、成功和信息的彩色显示

### 使用方法
```bash
# 在SDK根目录下执行完整发布流程
./audio_mixer_rk3576/scripts/ym_release.sh

# 指定产品编号
./audio_mixer_rk3576/scripts/ym_release.sh [产品编号]
```

### 工作流程

#### 1. SDK根目录检测
- 检查当前目录是否包含必需的文件夹：
  - `audio_mixer_rk3576`
  - `buildroot`
  - `kernel`
  - `device`
  - `u-boot`
- 如有缺失，显示错误信息并退出

#### 2. 产品选择阶段
- 调用`prevbuild.sh`进行产品选择
- 使用临时文件分离stdout和stderr
- 从stdout提取产品名称
- 如无法获取产品名称，退出脚本

#### 3. 构建清理阶段
- 删除之前的编译结果：
  - `buildroot/output/rockchip_rk3576/build/ym-appd-1.0`
  - `buildroot/output/rockchip_rk3576/build/ym-audio-mixer-1.0`

#### 4. 构建执行阶段
- 调用`build.sh`执行完整构建
- 检查构建结果，失败则退出

#### 5. 版本信息解析
- 解析`audio_mixer_rk3576/include/version.h`文件
- 提取版本信息：
  - 固件版本：`V$(YM_VERSION)$(YM_SUBVERSION)$(YM_REVISION)`
  - 硬件版本：`$(YM_RK3576_HARDWARE_VERSION)`
  - 产品型号：`$(YM_PRODUCT_MODEL)`
- 验证版本信息完整性

#### 6. 发布包生成
- 生成时间戳：`YYYYMMDDHH`格式
- 创建目标目录：`./output/ym_release/rel_${固件版本}_${时间戳}_${硬件版本}_${产品型号}`
- 复制发布文件：
  - Git信息文件
  - ymupdate升级包（重命名包含版本信息）
  - update.img镜像文件（重命名包含版本信息）

### 彩色输出功能
- **红色错误信息**: 所有错误信息以红色显示，格式为"错误：[消息]"
- **绿色成功信息**: 成功完成的操作以绿色显示
- **青色信息**: 一般状态信息以青色显示
- **黄色警告**: 警告信息以黄色显示（预留功能）

### 错误处理机制
1. **SDK根目录检测失败**: 立即退出
2. **产品名称获取失败**: 立即退出
3. **版本文件不存在**: 立即退出
4. **版本信息解析失败**: 立即退出
5. **构建执行失败**: 立即退出
6. **目录创建失败**: 立即退出
7. **文件复制失败**: 立即退出
8. **关键文件缺失**: 立即退出

### 输出目录结构
```
./output/ym_release/rel_V010_2024102315_0x10_audio_mixer_matrix_mic/
├── git_info_summary.txt                                    # Git仓库信息
├── ymupdate_audio_mixer_matrix_mic_V010_2024102315.tar.gz  # 升级包
└── update_audio_mixer_matrix_mic_V010_2024102315.img       # 镜像文件
```

### 版本信息格式
- **固件版本**: V + 主版本 + 子版本 + 修订版本（如：V010）
- **硬件版本**: 十六进制格式（如：0x10）
- **时间戳**: 年月日时格式（如：2024102315）
- **产品型号**: 从version.h解析或选择的产品名称

---

## 脚本依赖关系

```mermaid
graph TD
    A[ym_release.sh] --> B[prevbuild.sh]
    A --> C[build.sh]
    D[buildall.sh] --> B
    D --> C
    C --> E[git_info_extract.sh]
    C --> F[generate_version.sh]
    C --> G[post_build.sh]
    C --> H[lib_file_generator.py]
    C --> I[Makefile]
    A --> J[version.h解析]
    A --> K[SDK根目录检测]
```

## 环境要求

### 系统依赖
- Linux环境（推荐Ubuntu 18.04+）
- Bash shell
- Git版本控制系统
- Python3（用于库文件生成）

### 工具链依赖
- 交叉编译工具链：`aarch64-linux-gcc`
- 构建工具：make, tar, md5sum
- 音频库：ALSA, libsamplerate等

### 目录结构要求
```
sdk_root/                   # SDK根目录（ym_release.sh执行位置）
├── audio_mixer_rk3576/     # 当前项目目录
│   ├── include/version.h   # 版本信息文件
│   ├── scripts/            # 脚本目录
│   │   └── ym_release.sh   # 发布脚本
│   └── prevbuild.sh        # 预构建脚本
├── buildroot/              # Buildroot构建系统
├── kernel/                 # Linux内核源码
├── device/                 # 设备配置
├── u-boot/                 # U-Boot引导程序
└── build.sh                # 主构建脚本
```

## 常见问题

### 1. 版本分支格式错误
**问题**: 当前分支不符合`release_Vx.x.x`格式
**解决**: 确保在正确的发布分支上，或手动指定版本号

### 2. 交叉编译工具链缺失
**问题**: 找不到`aarch64-linux-gcc`
**解决**: 确保buildroot已正确构建并生成工具链

### 3. 产品目录不存在
**问题**: 指定的产品在`platform/`目录中不存在
**解决**: 检查产品名称拼写，确保目录存在且不以`_`开头

### 4. SDK根目录检测失败
**问题**: ym_release.sh提示"当前目录不是SDK根目录"
**解决**: 确保在包含audio_mixer_rk3576、buildroot、kernel、device、u-boot的SDK根目录下执行

### 5. 版本文件解析失败
**问题**: 无法从version.h文件中解析版本信息
**解决**: 检查audio_mixer_rk3576/include/version.h文件格式，确保包含正确的版本定义

### 6. 产品名称获取失败
**问题**: ym_release.sh无法获取选择的产品名称
**解决**: 检查prevbuild.sh是否正常执行，确保输出包含"SELECTED_PRODUCT:"

### 7. 构建文件缺失
**问题**: 找不到update.img、ymupdate.tar.gz等构建产物
**解决**: 确保build.sh成功执行，检查构建日志排查编译问题

## 最佳实践

1. **构建前检查**: 确保所有依赖工具和库已安装
2. **版本管理**: 在正确的发布分支上进行构建
3. **目录检查**: 使用ym_release.sh确保在正确的SDK根目录下执行
4. **增量构建**: 使用`build.sh clean`清理后再构建
5. **日志保存**: 重定向构建输出到日志文件便于调试
6. **权限检查**: 确保对所有相关目录有读写权限
7. **版本信息**: 确保version.h文件包含正确的版本定义
8. **错误处理**: 注意观察彩色错误信息，及时处理构建问题

---

*文档版本: 1.0*  
*最后更新: 2024年10月*
