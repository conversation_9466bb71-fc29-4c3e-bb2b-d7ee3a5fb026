# RK3576音频混音器构建脚本说明文档

本文档详细说明了RK3576音频混音器项目中三个主要构建脚本的功能、用法和工作流程。

## 目录
- [prevbuild.sh - 预构建脚本](#prevbuildsh---预构建脚本)
- [build.sh - 主构建脚本](#buildsh---主构建脚本)
- [buildall.sh - 完整构建脚本](#buildallsh---完整构建脚本)

---

## prevbuild.sh - 预构建脚本

### 功能概述
`prevbuild.sh`是一个平台选择和环境配置脚本，用于在正式构建前选择目标平台并配置构建环境。

### 主要功能
1. **平台发现**: 自动扫描`platform/`目录下的所有可用平台
2. **交互式选择**: 提供用户友好的平台选择界面
3. **环境配置**: 将选择的平台信息保存到用户环境变量中

### 使用方法
```bash
# 交互式选择平台（10秒超时）
./prevbuild.sh

# 直接指定平台编号
./prevbuild.sh [平台编号]
```

### 工作流程
1. **扫描平台目录**
   - 遍历`platform/*/`目录
   - 排除以`_`开头的目录
   - 生成可用平台列表

2. **显示平台选项**
   ```
   1: audio_mixer_matrix_mic
   2: pcba_test
   3: wrong_board_id
   4: build all boards program
   ```

3. **处理用户输入**
   - 无输入或超时：编译所有平台
   - 选择具体平台：仅编译该平台
   - 选择"build all"：编译所有平台

4. **环境变量配置**
   - 检查`~/.bashrc`中的`YM_AUDIO_MIXER_COMPILE_PLATFORM`变量
   - 如不存在则添加，存在则更新
   - 导出环境变量供后续脚本使用

### 输出示例
```
1: audio_mixer_matrix_mic
2: pcba_test  
3: wrong_board_id
4: build all boards program
Select platform(timeout 10s): 1
the audio_mixer_matrix_mic will be compiled
```

---

## build.sh - 主构建脚本

### 功能概述
`build.sh`是核心构建脚本，负责版本管理、代码编译、文件部署和打包等完整的构建流程。

### 主要功能
1. **版本管理**: Git分支版本解析和验证
2. **代码编译**: 多平台应用程序编译
3. **文件部署**: 将编译结果部署到buildroot目录
4. **打包生成**: 创建OTA升级包

### 使用方法
```bash
# 编译所有平台（默认）
./build.sh
./build.sh all

# 仅安装文件到buildroot
./build.sh install

# 清理构建结果
./build.sh clean

# 指定版本和平台编译
./build.sh all [平台名] [版本号]
```

### 工作流程

#### 1. 版本解析阶段
- 调用`git_info_extract.sh`提取Git仓库信息
- 解析当前分支名，验证是否符合`release_Vx.x.x`格式
- 如不符合，向上搜索父分支寻找符合格式的版本分支
- 生成`scripts/firmware_version`文件

#### 2. 平台配置阶段
- 读取环境变量`YM_AUDIO_MIXER_COMPILE_PLATFORM`
- 根据配置决定编译单个平台还是所有平台
- 设置编译目标列表

#### 3. 编译阶段
- 清理之前的构建结果
- 创建必要的目录结构
- 复制DSP库文件到release目录
- 调用`lib_file_generator.py`生成库文件
- 根据平台配置执行Make编译

#### 4. 部署阶段
- 将编译结果复制到buildroot的app和appbak分区
- 创建符号链接`audio_mixer_app`指向具体平台程序
- 设置可执行权限

#### 5. 打包阶段
- 生成OTA升级包的JSON配置文件
- 计算文件MD5校验和
- 创建`ymupdate.tar.gz`升级包

### 关键函数

#### `app_backup_set()`
```bash
# 功能：备份应用程序到指定目录
# 参数：备份路径、源程序路径
app_backup_set() {
    local bak_path=$1
    local app_src=$2
    # 创建备份目录并复制文件
    # 设置只读权限
}
```

#### `platform_public_install()`
- 安装公共脚本和配置文件到buildroot
- 配置USB自动复制规则
- 设置SSH密钥和日志配置

#### `gen_app_tar()`
- 生成OTA升级包
- 创建包含MD5校验的JSON配置
- 打包为tar.gz格式

### 版本格式要求
- 分支名必须符合：`release_Vx.x.x`格式
- 例如：`release_V1.2.3`
- 版本号将被解析为：主版本.子版本.修订版本

---

## buildall.sh - 完整构建脚本

### 功能概述
`buildall.sh`是最高级别的构建脚本，整合了平台选择、应用编译和固件生成的完整流程。

### 主要功能
1. **完整构建流程**: 从平台选择到固件生成的一站式构建
2. **固件管理**: 自动生成带时间戳的固件版本
3. **文件归档**: 整理和保存构建产物

### 使用方法
```bash
# 完整构建流程
./buildall.sh

# 指定平台构建
./buildall.sh [平台编号]
```

### 工作流程

#### 1. 预构建阶段
```bash
./prevbuild.sh $1
```
- 调用预构建脚本选择平台
- 配置构建环境变量

#### 2. 清理阶段
```bash
cd ..
rm -rf buildroot/output/rockchip_rk3308_b_release/build/ym_audio_mixer-1.0.0
```
- 切换到上级目录
- 清理之前的buildroot构建缓存

#### 3. 主构建阶段
```bash
./build.sh
```
- 调用主构建脚本
- 执行完整的编译和部署流程

#### 4. 固件归档阶段
- 生成时间戳格式：`YYYYMMDDHH`（年月日时）
- 创建输出目录：`./output/firmware/[时间戳]/`
- 复制构建产物：
  - Git信息文件：`git_info_summary.txt`
  - 固件镜像：`audio*.img`
  - 打包文件：`*.tar`
  - 二进制文件：`audio*.bin`

### 输出目录结构
```
./output/firmware/2024102315/
├── git_info_summary.txt      # Git仓库信息
├── audio_mixer_xxx.img       # 固件镜像文件
├── update.tar               # 应用更新包
└── audio_mixer_xxx.bin      # 二进制可执行文件
```

### 时间戳说明
- 格式：`YYYYMMDDHH`
- 示例：`2024102315`表示2024年10月23日15时
- 用于版本管理和固件追踪

---

## 脚本依赖关系

```mermaid
graph TD
    A[buildall.sh] --> B[prevbuild.sh]
    A --> C[build.sh]
    C --> D[git_info_extract.sh]
    C --> E[generate_version.sh]
    C --> F[post_build.sh]
    C --> G[lib_file_generator.py]
    C --> H[Makefile]
```

## 环境要求

### 系统依赖
- Linux环境（推荐Ubuntu 18.04+）
- Bash shell
- Git版本控制系统
- Python3（用于库文件生成）

### 工具链依赖
- 交叉编译工具链：`aarch64-linux-gcc`
- 构建工具：make, tar, md5sum
- 音频库：ALSA, libsamplerate等

### 目录结构要求
```
project_root/
├── audio_mixer_rk3576/     # 当前项目目录
├── buildroot/              # Buildroot构建系统
├── kernel/                 # Linux内核源码
└── u-boot/                 # U-Boot引导程序
```

## 常见问题

### 1. 版本分支格式错误
**问题**: 当前分支不符合`release_Vx.x.x`格式
**解决**: 确保在正确的发布分支上，或手动指定版本号

### 2. 交叉编译工具链缺失
**问题**: 找不到`aarch64-linux-gcc`
**解决**: 确保buildroot已正确构建并生成工具链

### 3. 平台目录不存在
**问题**: 指定的平台在`platform/`目录中不存在
**解决**: 检查平台名称拼写，确保目录存在且不以`_`开头

## 最佳实践

1. **构建前检查**: 确保所有依赖工具和库已安装
2. **版本管理**: 在正确的发布分支上进行构建
3. **增量构建**: 使用`build.sh clean`清理后再构建
4. **日志保存**: 重定向构建输出到日志文件便于调试
5. **权限检查**: 确保对所有相关目录有读写权限

---

*文档版本: 1.0*  
*最后更新: 2024年10月*
