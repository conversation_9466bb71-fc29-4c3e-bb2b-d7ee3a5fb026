<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-11-09 16:59:28
 * @LastEditTime: 2024-03-22 09:12:52
 * @LastEditors: An<PERSON><PERSON><PERSON>
 * @Description: 
 * @FilePath: /audio_mixer_rk3308/README.md
 * Copyright (c) 2006-2024, Yinkman Development Team
-->
# 音曼基于RK3308项目 #
* 教学系统4mic，platform-->audio_mixer_4mic
* 6+1圆盘阵列麦，platform-->audio_mixer_6_1mic_array

## 添加新底板
* 修改rk3308_defconfig，CONFIG_BOOTDELAY配置。将延迟启动的uboot下载到核心板。uboot启动后执行rktest adc 2，查看hwid_adc值，并记录。恢复CONFIG_BOOTDELAY=0。
* 创建对应底板的设备树文件，参考rk3308b-evb-amic-v10.dts，修改kernel/arch/arm64/boot/dts/rockchip/Makefile，将创建的设备树文件加入dtb-y。
* 在kernel/scripts/mkmultidtb.py-->RK3308-YM中添加设备树文件和adc值，**注意:adc的值不能和原有底板有冲突，uboot识别adc的值范围+-30。**
* 在platform目录下创建板级目录，**注意板级目录名称要与设备树文件app属性保持一致**。根据项目要求编写音频io、mixer输入输出等。
* 修改scripts/generate_version.sh文件，根据PC工具要求添加对应的硬件ID。
* 修改scripts/audio-mixer-start.sh文件，根据不同项目选择启动不同的应用程序。
* 重新编译固件，删除buildroot/output/rockchip_rk3308_b_release/build/ym_audio_mixer-1.0.0下的.stamp*文件，buildroot重新编译audio_mixer app。

## GPIO
计算公式：
* GPIO1_B1 = 1 * 32 + (B-A)*8 + 1 = 32 + 8 + 1 = 41;
* GPIO1_B2 = 1 * 32 + (B-A)*8 + 2 = 32 + 8 + 1 = 42;

| name     | 引脚号                   | 功能    | 有效值 |
| -------- | ------------------------ | ------- | ------ |
| GPIO4_B3 | 4 x 32 + 1 x 8 + 3 = 139 | HP_MUTE | 高     |
| GPIO4_A7 | 4 x 32 + 0 x 8 + 7 = 135 | SP_MUTE | 高     |
| GPIO0_A2 | 0 x 32 + 0 x 8 + 2 = 2   | MIC_SW1 | 高     |
| GPIO0_A1 | 0 x 32 + 0 x 8 + 1 = 1   | MIC_SW2 | 高     |
| GPIO0_A6 | 0 x 32 + 0 x 8 + 6 = 6   | MIC_SW3 | 高     |
| GPIO0_C1 | 0 x 32 + 2 x 8 + 1 = 17  | MIC_SW4 | 高     |
| GPIO0_C4 | 0 x 32 + 2 x 8 + 4 = 20  | LED     |        |

### GOIO测试
```bash
gpios=(139 135 2 1 6 17 20)
for i in "${gpios[@]}"; do
    echo ${i} > /sys/class/gpio/export
    echo out > /sys/class/gpio/gpio${i}/direction
    echo 0 > /sys/class/gpio/gpio${i}/value
done
```

### tune key
调音按键具有两个功能，调音和整机升级。长按8秒重启进入loader模式。

## 升级
项目发布两个升级包：
* 整机升级包，audio_mixer_rk3308_bsp_Vx.x.x.img用于整机升级，整机升级会擦除emmc所有信息，无需再次升级audio mixer。
* audio mixer升级包，audio_mixer_rk3308_app_Vx.x.x.bin用于只升级应用程序。

#### 版本自动生成
audio_mixer_rk3308_bsp_**Vx.x.x**.img，audio_mixer_rk3308_app_**Vx.x.x**.bin 版本由脚本根据audio mixer 的git tag自动生成。**发布版本前请创建最新的git tag**
使用build.sh构建升级包时，请务必删除output/rockchip_rk3308_b_release/build/ym_audio_mixer-1.0.0/.stamp_* ，使得buildroot打包最新的audio mixer。

### 整机升级
整机升级需要进入loder模式，进入方式有：
* audio mixer app正常执行，**usb audio插入pc打开瑞芯微升级工具**，长按tune key 10秒，系统自动重启进入loader模式。
* 设备断电，**usb audio插入pc打开瑞芯微升级工具**，上电启动同时长按tune key 5秒，系统进入loader模式。
* 设备断电，**usb audio插入pc打开瑞芯微升级工具**，按下板载recove key，上电启动，系统进入loader模式。

## 项目架构
### 组件
#### ym_database
用于整机参数，包括实时运行参数和配置参数。是软件用于完成分层设计的重要一环。不能在回调函数中实现阻塞函数，不能形成回调环路。
#### ym_fms
有限状态机，目前用于调音逻辑。
#### ym_startup
分层启动架构，具体分层详见ym_startup.h，数字越小启动优先级越高。
#### ym_stream
音频流中间件，实时传输音频流。
#### ym_protocol
音曼协议实现，有一些协议与底板相关，无法共通实现的在platform目录下实现。
#### ym_timer
定时器的软实现，分辨率为1ms，不能在timeout回调函数中使用阻塞函数。
#### ym_mixer_param
结合ym_database，用于mixer库的参数初始化和运行中配置mixer参数。
### msh
实现命令行。
### elog
实现日志。
### platform
不同底板的音频接口、特殊协议实现。

### 线程
使用ps命令可以查看当前所有由ym_thread_create维护的线程和线程运行在哪个core上。

| 线程          | CPU | 描述                             |
| ------------- | --- | -------------------------------- |
| elog_async    | 0   | 异步日志                         |
| uart_protocol | 0   | 音曼协议，串口                   |
| audio_in      | 1   | 每个codec输入为一个线程          |
| audio_out     | 1   | 每个codec输入为一个线程          |
| eth_protocol  | 0   | 音曼协议，网口                   |
| beam          | 1   | beam process                     |
| ym_timer      | 0   | 软件定时器                       |
| mmixer        | 2   | mixer 主核处理线程               |
| smixer        | 3   | mixer 从核处理线程               |
| tuning_fms    | 0   | 调音线程                         |
| uac_tx        | 1   | UAC 输出                         |
| uac_rx        | 1   | UAC 输入                         |
| uac_event     | 0   | UAC 事件捕获，用于实现pc音量调节 |
| usb_event     | 0   | 检测USB gadget是否插入           |
| ttygs0_out    | 0   | 标准输出到虚拟串口1              |
| finsh         | 0   | 虚拟串口1命令行                  |
| usb_protocol  | 0   | 虚拟串口2，实现音曼协议          |

## TODO
- 看门狗策略。
- 如果app跑不起来，系统自动进入loader模式。
- 生成定制化的描述信息添加到可执行文件的末尾，描述信息包括：
  + 适配硬件
  + 编译日期
  + git版本，sha256
  + 文件原始大小
  + 默认参数偏移地址
- 剥离所有的默认参数值，根据可执行文件的默认参数偏址读取默认参数。
- 嵌入lua脚本。
