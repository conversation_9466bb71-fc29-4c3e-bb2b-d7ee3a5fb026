#!/bin/bash

./scripts/git_info_extract.sh

rk3576_release_version=$(git rev-parse --abbrev-ref HEAD)
if [ $# -eq 3 ]; then
    rk3576_release_version=$(echo "release_V${3}")
fi
if [[ ! $rk3576_release_version =~ ^release_V[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    echo "当前分支 ($rk3576_release_version) 不符合 Vx.x.x 格式，开始检索父分支..."

    rk3576_release_version=""

    for commit in $(git rev-list --first-parent HEAD); do
        branches=$(git branch --contains $commit | sed 's/^[ *]*//')
        for branch in $branches; do
            if [[ $branch =~ ^release_V[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                rk3576_release_version=$branch
                break 2
            fi
        done
    done

    if [ -z "$rk3576_release_version" ]; then
        echo "未找到符合条件的父分支。"
        exit 1
    else
        echo "父分支为: $rk3576_release_version"
    fi
else
    echo "当前分支 ($rk3576_release_version) 符合 Vx.x.x 格式。"
fi

rk3576_release_version=${rk3576_release_version#release_}

export rk3576_release_version

# 去掉版本号中的 "V"
version=${rk3576_release_version#V}

# 解析版本号
IFS='-' read -ra version_parts <<<"$version"
IFS='.' read -ra version_numbers <<<"${version_parts[0]}"

# 分离主版本、子版本和修订版本
major_version=${version_numbers[0]}
minor_version=${version_numbers[1]}
revision_version=${version_numbers[2]}

cat <<EOL >scripts/firmware_version
$major_version-$minor_version-$revision_version
EOL

# 遍历目录
if [ $# -eq 2 ]; then
    target=("$2")
else
    target=()
    for dir in "platform"/*/; do
        if [ -d "$dir" ] && [[ $(basename "$dir") != _* ]]; then
            target+=("$(basename "$dir")")
        fi
    done
fi

app_backup_set()
{
    local bak_path=$1
    local app_src=$2
    local bak_folder=$bak_path/audio-mixer-bak
    if [ -d $bak_folder ]; then
        rm -rf $bak_folder
    fi
    mkdir $bak_folder
    cp -r $app_src/* $bak_folder
    chmod 555 $bak_folder/audio_mixer_*
}

tar_index=${#target[@]}
((tar_index++))

#bash prevbuild.sh

export `grep 'YM_AUDIO_MIXER_COMPILE_PLATFORM=' ~/.bashrc`
sel_platform=$YM_AUDIO_MIXER_COMPILE_PLATFORM
#echo $tar_index
#echo $sel_platform

single_platform=("pcba_test" "wrong_board_id")

platform_public_install()
{
#    cp scripts/S99audio_mixer.sh ../buildroot/output/rockchip_rk3576/target/etc/init.d/
#    cp scripts/audio-mixer-start.sh ../buildroot/output/rockchip_rk3576/target/usr/bin
#    cp scripts/audio-mixer-upgrade.sh ../buildroot/output/rockchip_rk3576/target/usr/bin
#    rsync -av --progress scripts/usbdevice.sh ../buildroot/output/rockchip_rk3576/target/usr/bin
#    rsync -av --progress scripts/virtual_udisk.sh ../buildroot/output/rockchip_rk3576/target/usr/bin
#    cp scripts/usb_adb.sh ../buildroot/output/rockchip_rk3576/target/usr/bin
#    app_backup_set ../buildroot/output/rockchip_rk3576/target/usr/bin ../device/rockchip/common/extra-parts/app/normal

    if [ ! -e ../buildroot/output/rockchip_rk3576/target/etc/udev/rules.d ]; then
        mkdir -p ../buildroot/output/rockchip_rk3576/target/etc/udev/rules.d
    fi
    cp scripts/90-usb-autocopy.rules ../buildroot/output/rockchip_rk3576/target/etc/udev/rules.d/
    cp scripts/usb-autocopy.sh ../buildroot/output/rockchip_rk3576/target/usr/bin

    cd ../buildroot/output/rockchip_rk3576/target/etc/
    rm -rf dropbear
    mkdir -p dropbear
    mkdir -p ../userdata/ym_log
    cd -
    cp scripts/dropbear_ed25519_host_key ../buildroot/output/rockchip_rk3576/target/etc/dropbear/

    cp etc/zlog.conf ../buildroot/output/rockchip_rk3576/target/etc/

#    rsync -av --progress scripts/Wx_3308_MicPair ../buildroot/output/rockchip_rk3576/target/usr/bin
    # cp scripts/neofetch ../buildroot/output/rockchip_rk3576/target/usr/bin
#    cp scripts/check_update.sh ../buildroot/output/rockchip_rk3576/target/usr/bin

    # if [ ! -e ../buildroot/output/rockchip_rk3576/target/etc/mosquitto ]; then
    #     mkdir -p ../buildroot/output/rockchip_rk3576/target/etc/mosquitto
    # fi
    # cp scripts/mosquitto.conf ../buildroot/output/rockchip_rk3576/target/etc/mosquitto/

    # cp scripts/firmware_version ../device/rockchip/common/extra-parts/app/normal/
    # cp scripts/firmware_version ../device/rockchip/common/extra-parts/appbak/normal/
    # cp scripts/firmware_version release/
#    cp platform/audio_mixer_yao_long_8mic/yaolong-voltage.sh ../buildroot/output/rockchip_rk3576/target/usr/bin
    cp scripts/ym_log_package.sh ../buildroot/output/rockchip_rk3576/target/usr/bin

#    chmod 555 scripts/imageota
#    rm -f ../buildroot/output/rockchip_rk3576/target/usr/bin/imageota
#    rsync -av --progress scripts/imageota ../buildroot/output/rockchip_rk3576/target/usr/bin
}

gen_app_tar()
{
    tarf='update.tar'
    cd release
    tar -cvf $tarf ./*

    json='ota.json'
    pack='ymupdate.tar.gz'
    md5=$(md5sum ${tarf} | awk '{print $1}')

    cat << EOF > ${json}
{
    "platform": "rk3576",
    "board": "MicMatrix",
    "type": "app",
    "name": "app",
    "srcdir": "/ota",
    "dstdir": "/app",
    "md5": "${md5}"
}
EOF
    if [ -e ${pack} ]
    then
        rm ${pack}
    fi

    tar -cvzf ${pack} $tarf ${json}
    mv ${pack} ../
    rm -rf ${tarf} ${json}
}

case "$1" in
all | "")

    # 重新编译app，并将所有app程序拷贝到buildroot app分区的打包目录
    rm -rf release/

    rm -rf ../device/rockchip/common/extra-parts/app/normal
    rm -rf ../device/rockchip/common/extra-parts/appbak/normal
    rm -rf ../device/rockchip/common/extra-parts/appcfg/normal

    mkdir -p ../device/rockchip/common/extra-parts/app/normal
    mkdir -p ../device/rockchip/common/extra-parts/appbak/normal
    mkdir -p ../device/rockchip/common/extra-parts/appcfg/normal

    make clean
    # make tools
    mkdir -p release/
    cp lib_dsp/*.so release/
    # cp release/* ../device/rockchip/common/extra-parts/app/normal/
    # cp release/* ../device/rockchip/common/extra-parts/appbak/normal/

    cp scripts/git_info_summary.txt ../device/rockchip/common/extra-parts/app/normal/

    # 获取时间戳
    timestamp=$(date +%Y%m%d%H%M%S)
    python3 ./scripts/lib_file_generator.py

    if [ $sel_platform -eq $tar_index ]; then
        for i in "${target[@]}"; do
            make clean
            make all TARGET=${i} -j8
        done
    else
        ((sel_platform--))

        single_platform+=("${target[$sel_platform]}")

        for i in "${single_platform[@]}"; do
            make clean
            make all TARGET=${i} -j8
        done
        cd release
        ln -s ${target[$sel_platform]} audio_mixer_app
        cd -
    fi

    # cp scripts/firmware_version ../device/rockchip/common/extra-parts/app/normal/
    # cp scripts/firmware_version ../device/rockchip/common/extra-parts/appbak/normal/
    cp scripts/firmware_version release/

    cp release/* ../device/rockchip/common/extra-parts/app/normal/
    cd ../device/rockchip/common/extra-parts/app/normal/
    if [ -e audio_mixer_app ]; then
        rm -f audio_mixer_app
    fi
    ln -s ${target[$sel_platform]} audio_mixer_app
    chmod +x ${target[$sel_platform]}
    cd -

    cp release/* ../device/rockchip/common/extra-parts/appbak/normal/
    cd ../device/rockchip/common/extra-parts/appbak/normal/
    if [ -e audio_mixer_app ]; then
        rm -f audio_mixer_app
    fi
    ln -s ${target[$sel_platform]} audio_mixer_app
    chmod +x ${target[$sel_platform]}
    cd -

    gen_app_tar
    ;;
install)
    platform_public_install
    ((sel_platform--))
    ;;
clean)
    rm -rf release/
    make clean
    ;;
*)
    echo "Usage: [all|install|clean]" >&2
    exit 3
    ;;
esac
