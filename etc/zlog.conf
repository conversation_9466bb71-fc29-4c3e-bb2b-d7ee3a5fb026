# comments
[global]
strict init = true
buffer min = 1024
buffer max = 2MB
file perms = 666
default format = "[%-8V %d(%m-%d %T):%us] %m%n"
rotate lock file = /tmp/zlog.lock

[formats]
eformat = "[%-8V %us %F:%L] %m%n"

[rules]
audio_mixer.info            "/userdata/ym_log/audio_mixer.log",200KB*5~"/userdata/ym_log/audio_mixer.log.#r"
audio_mixer.warn            "/userdata/ym_log/audio_mixer.log",200KB*5~"/userdata/ym_log/audio_mixer.log.#r"
audio_mixer.error           "/userdata/ym_log/audio_mixer.log",200KB*5~"/userdata/ym_log/audio_mixer.log.#r";eformat

