/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-10-11 16:39:55
 * @LastEditTime: 2024-10-12 09:49:55
 * @LastEditors: Anzhicha<PERSON>
 * @Description: 
 * @FilePath: /audio_mixer_rk3308/src/usb_utilites.c
 * Copyright (c) 2006-2024, Yinkman Development Team
 */

#include "ym_audio_mixer.h"

uint32_t usb_out_xrun_count;
uint32_t usb_in_xrun_count;

int set_hwparams(snd_pcm_t *handle,
                 snd_pcm_hw_params_t *params,
                 snd_pcm_access_t __access,
                 snd_pcm_uframes_t buffer_size,
                 snd_pcm_uframes_t period_size)
{
    unsigned int rrate;
    int err;

    /* choose all parameters */
    err = snd_pcm_hw_params_any(handle, params);
    if (err < 0)
    {
        log_e("Broken configuration: no configurations available: %s", snd_strerror(err));
        return err;
    }

    /* add by lian<PERSON><PERSON>, 2025/1/6 */
    /* set hardware resampling, disable alsa-lib resampling */
    err = snd_pcm_hw_params_set_rate_resample(handle, params, 0);
    if (err < 0) {
        log_e("Resampling setup failed for playback: %s\n", snd_strerror(err));
        return err;
    }

    /* set the interleaved read/write format */
    // snd_pcm_access_mask_t *mask = alloca(snd_pcm_access_mask_sizeof());
    // snd_pcm_access_mask_none(mask);
    // snd_pcm_access_mask_set(mask, SND_PCM_ACCESS_MMAP_INTERLEAVED);
    // err = snd_pcm_hw_params_set_access_mask(handle, params, mask);
    err = snd_pcm_hw_params_set_access(handle, params, __access);
    if (err < 0)
    {
        log_e("Access type not available for playback: %s", snd_strerror(err));
        return err;
    }

    /* set the sample format */
    err = snd_pcm_hw_params_set_format(handle, params, SND_PCM_FORMAT_S16);
    if (err < 0)
    {
        log_e("Sample format not available: %s", snd_strerror(err));
        return err;
    }

    /* set the count of channels = 2 */
    err = snd_pcm_hw_params_set_channels(handle, params, 2);
    if (err < 0)
    {
        log_e("Channels count (2) not available: %s", snd_strerror(err));
        return err;
    }

    /* set the stream rate */
    rrate = RATE;
    err = snd_pcm_hw_params_set_rate_near(handle, params, &rrate, 0);
    if (err < 0)
    {
        log_e("Rate %dHz not available: %s", rrate, snd_strerror(err));
        return err;
    }
    if (rrate != RATE)
    {
        log_e("Rate doesn't match (requested %dHz, get %iHz)", rrate, err);
        return -EINVAL;
    }

    /* set the buffer size */
    err = snd_pcm_hw_params_set_buffer_size(handle, params, buffer_size);
    if (err < 0)
    {
        log_e("Unable to set buffer: %s", snd_strerror(err));
        return err;
    }

    /* set the period size */
    err = snd_pcm_hw_params_set_period_size(handle, params, period_size, 0);
    if (err < 0)
    {
        log_e("Unable to set period size: %s", snd_strerror(err));
        return err;
    }

    /* write the parameters to device */
    err = snd_pcm_hw_params(handle, params);
    if (err < 0)
    {
        log_e("Unable to set hw params for playback: %s", snd_strerror(err));
        return err;
    }
    return 0;
}

int set_swparams(snd_pcm_t *handle,
                 snd_pcm_sw_params_t *swparams,
                 int start,
                 int stop,
                 int min)
{
    int err;

    /* get the current swparams */
    err = snd_pcm_sw_params_current(handle, swparams);
    if (err < 0)
    {
        log_e("Unable to determine current swparams: %s", snd_strerror(err));
        return err;
    }

    /* start the transfer when the buffer is almost full: */
    err = snd_pcm_sw_params_set_start_threshold(handle, swparams, start);
    if (err < 0)
    {
        log_e("Unable to set start threshold mode: %s", snd_strerror(err));
        return err;
    }

    err = snd_pcm_sw_params_set_stop_threshold(handle, swparams, stop);
    if (err < 0)
    {
        log_e("Unable to set stop threshold mode: %s", snd_strerror(err));
        return err;
    }

    /* allow the transfer when at least period_size samples can be processed */
    /* or disable this mechanism when period event is enabled (aka interrupt like style processing) */
    err = snd_pcm_sw_params_set_avail_min(handle, swparams, min);
    if (err < 0)
    {
        log_e("Unable to set avail min: %s", snd_strerror(err));
        return err;
    }

    /* write the parameters to the playback device */
    err = snd_pcm_sw_params(handle, swparams);
    if (err < 0)
    {
        log_e("Unable to set sw params: %s", snd_strerror(err));
        return err;
    }
    return 0;
}

void get_pcm_type(const char *s, int *card, int *device, char *type)
{
    *card = *device = 0;
    // Parse the card and device numbers from the input string
    if (sscanf(s, "/dev/snd/pcmC%dD%d%c", card, device, type) < 0)
    {
        *card = *device = 0;
    }
}

int is_control(const char *s, int *card)
{
    *card = 0;
    // Parse the card and device numbers from the input string
    if (sscanf(s, "/dev/snd/controlC%d", card) < 0)
        return 0;
    return !(*card == 0);
}

int capture_xrun_recovery(snd_pcm_t *handle)
{
    usb_in_xrun_count++;
    int err = snd_pcm_prepare(handle);
    if (err < 0)
    {
    }
    else
    {
        err = snd_pcm_start(handle);
        // if (err == 0)
        // {
        //     err = snd_pcm_wait(handle, -1);
        // }
    }

    return err;
}

ssize_t usb_pcm_read(snd_pcm_t *handle, uint8_t *data, size_t rcount)
{
    ssize_t r;
    size_t result = 0;
    size_t count = rcount;
    const uint32_t bits_per_frame = snd_pcm_format_physical_width(SND_PCM_FORMAT_S16_LE) * 2;

    while (count > 0)
    {
        pthread_testcancel();
        r = snd_pcm_readi(handle, data, count);
        if (r == -EAGAIN || (r >= 0 && (size_t)r < count))
        {
        }
        else if (r < 0)
        {
            if (capture_xrun_recovery(handle) == 0)
            {
                // usleep(10000);
            }
            break;
        }

        if (r > 0)
        {
            result += r;
            count -= r;
            data += r * bits_per_frame / 8;
        }
    }
    return result;
}

int play_xrun_recovery(snd_pcm_t *handle)
{
    usb_out_xrun_count++;
    int err = snd_pcm_prepare(handle);
    return err;
}

ssize_t usb_pcm_write(snd_pcm_t *handle, uint8_t *data, size_t count)
{
    ssize_t r;
    ssize_t result = 0;
    const uint32_t bits_per_frame = snd_pcm_format_physical_width(SND_PCM_FORMAT_S16_LE) * 2;

    while (count > 0)
    {
        pthread_testcancel();
        r = snd_pcm_writei(handle, data, count);
#if 1
        if (r < 0)
        {
            if (play_xrun_recovery(handle) == 0)
            {
                // usleep(10000);
            }
            break;
        }
#else
        if (r == -EAGAIN)
        {
            continue;
        }
        else if (r == -EPIPE) /* under-run */
        {
            r = snd_pcm_prepare(handle);
            if (r < 0)
                log_e("Can't recovery from underrun, prepare failed: %s\n", snd_strerror(r));
            return 0;
        }
        else if (r == -ESTRPIPE)
        {
            while ((r = snd_pcm_resume(handle)) == -EAGAIN)
                sleep(1);   /* wait until the suspend flag is released */
            if (r < 0)
            {
                r = snd_pcm_prepare(handle);
                if (r < 0)
                    log_e("Can't recovery from suspend, prepare failed: %s\n", snd_strerror(r));
            }
            return 0;
        }
#endif
        if (r > 0)
        {
            result += r;
            count -= r;
            data += r * bits_per_frame / 8;
        }
    }
    return result;
}
