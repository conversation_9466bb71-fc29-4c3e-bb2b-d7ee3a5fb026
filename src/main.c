#include <stdio.h>
#include <time.h>
#include <unistd.h>
#include <stdlib.h>
#include <signal.h>
#include <semaphore.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <sys/types.h>
#include <sys/file.h>
#include <fcntl.h>
#include <zlog.h>
#include <stdint.h>
#include <stdbool.h>

#include "ym_shell.h"
#include "ym_stream.h"
#include "ym_startup.h"
#include "ym_protocol.h"
#include "ym_database.h"
#include "ym_thread.h"
#include "ym_log.h"
#include "ym_timer.h"

uint32_t start_time = 0;
bool zlog_file_ext2_err = false;

static int32_t lockfile(int32_t fd)
{
    struct flock fl;

    fl.l_type = F_WRLCK;
    fl.l_start = 0;
    fl.l_whence = SEEK_SET;
    fl.l_len = 0;

    return (fcntl(fd, F_SETLK, &fl));
}

static int32_t proc_is_exist(const char *procname)
{
    int32_t fd;
    char buf[16];
    char filename[100];

    sprintf(filename, "/tmp/%s.pid", procname);

    fd = open(filename, O_RDWR | O_CREAT, (S_IRUSR | S_IWUSR | S_IRGRP | S_IROTH));
    if (fd < 0)
    {
        printf("open file \"%s\" failed!!!\n", filename);
        return 1;
    }

    if (lockfile(fd) == -1)
    { /* 尝试对文件进行加锁 */
        // printf("file \"%s\" locked. proc already exit!!!\n", filename);
        close(fd);
        return 1;
    }
    else
    {
        /* 写入运行实例的pid */
        if (ftruncate(fd, 0) < 0)
        {
            printf("ftruncate file \"%s\" failed!!!\n", filename);
            close(fd);
            return 1;
        }
        sprintf(buf, "%ld\n", (long)getpid());
        ssize_t bytes_written = write(fd, buf, strlen(buf) + 1);
        if (bytes_written < 0)
        {
            printf("write pid to file \"%s\" failed!!!\n", filename);
            close(fd);
            return 1;
        }
        else
        {
            // printf("write pid to file \"%s\" success!!!\n", filename);
            return 0;
        }
    }
}

static void stream_close(int32_t sig)
{
    printf("\nCtrl+c\n");
    ym_exit();
    zlog_fini();
    exit(0);
}

int32_t main(int32_t argc, char *const *argv)
{
    (void)argc;
    (void)argv;

    signal(SIGINT, stream_close);
    signal(SIGTERM, stream_close);
    signal(SIGQUIT, stream_close);

    /* 单实例运行 */
    if (proc_is_exist("audio_mixer") == 1)
    {
        printf("an audio_mixer already running in system. exit now...\n");
        return 0;
    }
    else
    {
        printf("audio_mixer starting...\n");
    }

    if (dzlog_init("/etc/zlog.conf", "audio_mixer") != 0)
    {
        printf("zlog init err.\n");
        zlog_file_ext2_err = true;
    }
    else
    {
        zlog_file_ext2_err = false;
    }

    // 屏蔽socket断联信号
    struct sigaction sa;
    sa.sa_handler = SIG_IGN;
    sigaction(SIGPIPE, &sa, 0);

    start_time = time(NULL);

    recored_start_count();
    database_lowlevel_init();
    // finsh_system_init();

    ym_init();

    ym_thread_create("ym_timer",
                     default_thread_cpuid,
                     default_thread_prio,
                     ym_timer_thread_handle,
                     NULL);

    ym_thread_all_run();

#ifndef SYSTEM_NO_AUDIO_STREAM
    int32_t audio_io_app_init(void);
    audio_io_app_init();
#endif /* end SYSTEM_NO_AUDIO_STREAM */
    sleep(5);
    log_i("ym thread all run");

    for (;;)
    {
        sleep(60);
        if (system("date +%s > /userdata/timestamp"))
        {
        }
        if (system("sync;sync") == -1)
        {
            log_e("sync error");
        }
    }

    return 0;
}
