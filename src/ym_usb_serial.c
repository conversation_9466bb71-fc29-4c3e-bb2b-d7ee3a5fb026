#include <fcntl.h>
#include <unistd.h>
#include <termios.h>
#include <stdint.h>

#include "ym_log.h"
#include "ym_thread.h"
#include "ym_protocol.h"

static void *usb_protocol_thread_handle;
static int32_t usb_serial_fd;

void ym_protocol_serial_set_param(int32_t fd, int baudrate)
{
    struct termios param;
    tcgetattr(fd, &param); // Get terminal properties

    cfsetospeed(&param, baudrate);
    cfsetispeed(&param, baudrate);

    param.c_iflag &= ~(BRKINT | ICRNL | INPCK | ISTRIP | IXON); // 屏蔽各种控制字符
    // OPOST: 使用原始输出，就是禁用输出处理，使数据能不经过处理、过滤地完整地输出到串口接口。
    param.c_oflag &= ~OPOST;
    // CLOCAL: 忽略调制调解器状态行
    // CREAD: 启用接收
    param.c_cflag |= (CLOCAL | CREAD);
    // ~(ICANON | ECHO | ECHOE | ISIG): 原始数据模式，并关闭信号
    param.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);

    // 设置读取超时
    param.c_cc[VMIN] = 1;   // 协议最少4字节
    param.c_cc[VTIME] = 10; // 1s,单位百毫秒

    tcsetattr(fd, TCSANOW, &param);
}

void usb_serial_configured(void)
{
    usb_serial_fd = open("/dev/ttyGS0", O_RDWR | O_NOCTTY);
    if (usb_serial_fd < 0)
    {
        log_e("ttyGS0 serial open err.");
    }
    else
    {
        ym_protocol_serial_set_param(usb_serial_fd, B115200);

        usb_protocol_thread_handle = ym_thread_create("usb_protocol",
                                                      default_thread_cpuid,
                                                      default_thread_prio,
                                                      uart_protocol_thread_entry,
                                                      (void *)&usb_serial_fd);


        ym_thread_run(usb_protocol_thread_handle);
    }
}

void usb_serial_disconnected(void)
{
    if (usb_protocol_thread_handle)
    {
        ym_thread_destroy(usb_protocol_thread_handle);
        usb_protocol_thread_handle = NULL;
    }

    close(usb_serial_fd);
}
