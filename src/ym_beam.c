#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include <semaphore.h>
#include <pthread.h>

#include "ym_log.h"
#include "config.h"
#include "ym_database.h"
#include "ym_mixer_platform.h"
#include "ym_startup.h"
#include "ym_utilities.h"
#include "ym_audio_mixer.h"
#include "ym_shell.h"
#include "ym_audio_tools.h"
#include "ym_mixer_beam.h"
#include "ym_database_index.h"
#include "ym_stream.h"
#include "ym_beam.h"
#include "ym_slide_filter.h"

#ifdef BEAM_ENABLE

#define MSH_RESULT_PRINT(result)         \
    do                                   \
    {                                    \
        if (result == 0)                 \
            printf("set successful\n");  \
        else                             \
            printf("set failure\n");     \
    } while (0)

#if BEAM_INSTANCE_NUMBER > 0
MIXER_DEF_INIT(beam, BEAM_IN_CHANNELS, BEAM_OUT_CHANNELS);
#endif
#if BEAM_INSTANCE_NUMBER > 1
MIXER_DEF_INIT(beam2, BEAM_IN_CHANNELS, BEAM_OUT_CHANNELS);
#endif
#if BEAM_INSTANCE_NUMBER > 2
MIXER_DEF_INIT(beam3, BEAM_IN_CHANNELS, BEAM_OUT_CHANNELS);
#endif
#if BEAM_INSTANCE_NUMBER > 3
MIXER_DEF_INIT(beam4, BEAM_IN_CHANNELS, BEAM_OUT_CHANNELS);
#endif
#if BEAM_INSTANCE_NUMBER > 4
MIXER_DEF_INIT(beam5, BEAM_IN_CHANNELS, BEAM_OUT_CHANNELS);
#endif
#if BEAM_INSTANCE_NUMBER > 5
MIXER_DEF_INIT(beam6, BEAM_IN_CHANNELS, BEAM_OUT_CHANNELS);
#endif
#if BEAM_INSTANCE_NUMBER > 6
MIXER_DEF_INIT(beam7, BEAM_IN_CHANNELS, BEAM_OUT_CHANNELS);
#endif
#if BEAM_INSTANCE_NUMBER > 7
MIXER_DEF_INIT(beam8, BEAM_IN_CHANNELS, BEAM_OUT_CHANNELS);
#endif

#if defined(BD_MIXER_MATRIX_MIC)
mixer_def_p pbeam_handle[BEAM_INSTANCE_NUMBER] = {&beam, &beam2};
#elif defined(BD_MIXER_LINE_MIC)
mixer_def_p pbeam_handle[BEAM_INSTANCE_NUMBER] = {&beam, &beam2, &beam3, &beam4, &beam5, &beam6, &beam7, &beam8};
#endif

static int32_t beam_query_open(mixer_def_p pbeam, uint32_t index)
{
    uint32_t p_sta_size;
    uint32_t p_sta_ddr_size;
    uint32_t p_dyn_size;
    void *beam_s_buf = NULL;
    void *beam_r_buf = NULL;
    void *beam_d_buf = NULL;
    void *beam_handle = NULL;
    int32_t database = pbeam->database_base_index;

#if defined(BD_MIXER_MATRIX_MIC)
    const int beam_band_freq[MIXER_PARAM_PEQ_MAX] = {2200, 800, 1300, 100, 3300, 6300, 10800, 800};
    const int beam_band_gain[MIXER_PARAM_PEQ_MAX] = {30, -50, 50, -60, 20, -60, -120, 0};
    const int beam_band_qval[MIXER_PARAM_PEQ_MAX] = {YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE, 2600, 5000,
                                                     3300, 9000, 4000, YM_PEQ_DEF_Q_VALUE};
    const int beam_filter_type[MIXER_PARAM_PEQ_MAX] = {YM_PEQ_HIGH_SHELF_FILTER, YM_PEQ_LOW_SHELF_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER,
                                                       YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_LOW_SHELF_FILTER};
#elif defined(BD_MIXER_LINE_MICS)
    const int beam_band_freq[MIXER_PARAM_PEQ_MAX] = {100, 260, 1300, 5000, 6300, 4500, 6000, 800};
    const int beam_band_gain[MIXER_PARAM_PEQ_MAX] = {-120, -30, -60, 30, -60, 0, 0, 0};
    const int beam_band_qval[MIXER_PARAM_PEQ_MAX] = {5000, YM_PEQ_DEF_Q_VALUE, 1000, 2500,
                                                     9000, YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE};
    const int beam_filter_type[MIXER_PARAM_PEQ_MAX] = {YM_PEQ_PEAKEQ_FILTER, YM_PEQ_LOW_SHELF_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER,
                                                       YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_LOW_SHELF_FILTER};
    const int beam_band_freq_2[MIXER_PARAM_PEQ_MAX] = {100, 260, 1300, 5000, 6300, 4500, 6000, 800};
    const int beam_band_gain_2[MIXER_PARAM_PEQ_MAX] = {-120, -30, -60, 30, -60, 0, 0, 0};
    const int beam_band_qval_2[MIXER_PARAM_PEQ_MAX] = {5000, YM_PEQ_DEF_Q_VALUE, 1000, 2500,
                                                       9000, YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE};
    const int beam_filter_type_2[MIXER_PARAM_PEQ_MAX] = {YM_PEQ_PEAKEQ_FILTER, YM_PEQ_LOW_SHELF_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER,
                                                         YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_LOW_SHELF_FILTER};
    const int beam_band_freq_3[MIXER_PARAM_PEQ_MAX] = {100, 260, 1300, 5000, 6300, 4500, 6000, 800};
    const int beam_band_gain_3[MIXER_PARAM_PEQ_MAX] = {-120, -30, -60, 30, -60, 0, 0, 0};
    const int beam_band_qval_3[MIXER_PARAM_PEQ_MAX] = {5000, YM_PEQ_DEF_Q_VALUE, 1000, 2500,
                                                       9000, YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE};
    const int beam_filter_type_3[MIXER_PARAM_PEQ_MAX] = {YM_PEQ_PEAKEQ_FILTER, YM_PEQ_LOW_SHELF_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER,
                                                         YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_LOW_SHELF_FILTER};
    const int beam_band_freq_4[MIXER_PARAM_PEQ_MAX] = {100, 260, 1300, 5000, 6300, 4500, 6000, 800};
    const int beam_band_gain_4[MIXER_PARAM_PEQ_MAX] = {-120, -30, -60, 30, -60, 0, 0, 0};
    const int beam_band_qval_4[MIXER_PARAM_PEQ_MAX] = {5000, YM_PEQ_DEF_Q_VALUE, 1000, 2500,
                                                       9000, YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE};
    const int beam_filter_type_4[MIXER_PARAM_PEQ_MAX] = {YM_PEQ_PEAKEQ_FILTER, YM_PEQ_LOW_SHELF_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER,
                                                       YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_LOW_SHELF_FILTER};
    const int beam_band_freq_5[MIXER_PARAM_PEQ_MAX] = {100, 260, 1300, 5000, 6300, 1000, 1000, 1000};
    const int beam_band_gain_5[MIXER_PARAM_PEQ_MAX] = {-120, -10, -30, 10, -30, 0, 0, 0};
    const int beam_band_qval_5[MIXER_PARAM_PEQ_MAX] = {5000, YM_PEQ_DEF_Q_VALUE, 1000, 2500,
                                                       9000, YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE};
    const int beam_filter_type_5[MIXER_PARAM_PEQ_MAX] = {YM_PEQ_PEAKEQ_FILTER, YM_PEQ_LOW_SHELF_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER,
                                                         YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER};
    const int beam_band_freq_6[MIXER_PARAM_PEQ_MAX] = {100, 260, 1300, 5000, 6300, 1000, 1000, 1000};
    const int beam_band_gain_6[MIXER_PARAM_PEQ_MAX] = {-120, -10, -30, 10, -30, 0, 0, 0};
    const int beam_band_qval_6[MIXER_PARAM_PEQ_MAX] = {5000, YM_PEQ_DEF_Q_VALUE, 1000, 2500,
                                                       9000, YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE};
    const int beam_filter_type_6[MIXER_PARAM_PEQ_MAX] = {YM_PEQ_PEAKEQ_FILTER, YM_PEQ_LOW_SHELF_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER,
                                                         YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER};
    const int beam_band_freq_7[MIXER_PARAM_PEQ_MAX] = {100, 260, 1300, 5000, 6300, 1000, 1000, 1000};
    const int beam_band_gain_7[MIXER_PARAM_PEQ_MAX] = {-120, -10, -30, 10, -30, 0, 0, 0};
    const int beam_band_qval_7[MIXER_PARAM_PEQ_MAX] = {5000, YM_PEQ_DEF_Q_VALUE, 1000, 2500,
                                                       9000, YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE};
    const int beam_filter_type_7[MIXER_PARAM_PEQ_MAX] = {YM_PEQ_PEAKEQ_FILTER, YM_PEQ_LOW_SHELF_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER,
                                                         YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER};
    const int beam_band_freq_8[MIXER_PARAM_PEQ_MAX] = {100, 260, 1300, 5000, 6300, 1000, 1000, 1000};
    const int beam_band_gain_8[MIXER_PARAM_PEQ_MAX] = {-120, -10, -30, 10, -30, 0, 0, 0};
    const int beam_band_qval_8[MIXER_PARAM_PEQ_MAX] = {5000, YM_PEQ_DEF_Q_VALUE, 1000, 2500,
                                                       9000, YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE};
    const int beam_filter_type_8[MIXER_PARAM_PEQ_MAX] = {YM_PEQ_PEAKEQ_FILTER, YM_PEQ_LOW_SHELF_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER,
                                                         YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER};
#endif

    int32_t ret = beam_query_mem(&p_sta_size, &p_sta_ddr_size, &p_dyn_size);
    if (ret == 0)
    {
        log_i("beam query mem successful.\t%u\t%u\t%u", p_sta_size, p_sta_ddr_size, p_dyn_size);
        beam_s_buf = aligned_malloc(p_sta_size, 1024);
        beam_r_buf = aligned_malloc(p_sta_ddr_size, 1024);
        beam_d_buf = aligned_malloc(p_dyn_size, 1024);
        if (beam_s_buf == NULL || beam_r_buf == NULL || beam_d_buf == NULL)
        {
            log_e("beam aligned malloc error.");
            ret = -1;
        }
        else
        {
            memset(beam_s_buf, 0, p_sta_size);
            memset(beam_r_buf, 0, p_sta_ddr_size);
            memset(beam_d_buf, 0, p_dyn_size);
            beam_handle = beam_open(FRAMES, beam_s_buf, beam_r_buf, beam_d_buf);
            if (beam_handle != NULL)
            {
                beam_set_lowDelayDsGain_on_off(beam_handle, 1);

                database_write_no_callback(beam_mode_index + database, (database_value)BEAM_NORMAL);
                database_write_no_callback(beam_width_level_index + database, (database_value)2);
                database_write_no_callback((beam_channel0_out_id_index + database), (database_value)BEAM_IN_CHANNELS);
                database_write_no_callback((beam_channel1_out_id_index + database), (database_value)BEAM_IN_CHANNELS);

#if defined(BD_MIXER_LINE_MIC)
                database_write_no_callback((beam_channel1_out_id_index + database), (database_value)2);

                database_write_no_callback(beam_track_x_axis_index + database, (database_value)90);
                database_write_no_callback(beam_track_z_axis_index + database, (database_value)90);
                database_write_no_callback(beam_track_mode_index + database, (database_value)BEAM_ADAPTIVE_MODE);

                database_write_no_callback(beam_area1_x1_axis_index + database, (database_value)(-320));
                database_write_no_callback(beam_area2_x1_axis_index + database, (database_value)(-150));
                database_write_no_callback(beam_area3_x1_axis_index + database, (database_value)(-50));
                database_write_no_callback(beam_area4_x1_axis_index + database, (database_value)(-200));
                database_write_no_callback(beam_area1_y1_axis_index + database, (database_value)(100));
                database_write_no_callback(beam_area2_y1_axis_index + database, (database_value)(100));
                database_write_no_callback(beam_area3_y1_axis_index + database, (database_value)(-100));
                database_write_no_callback(beam_area4_y1_axis_index + database, (database_value)(-100));
                database_write_no_callback(beam_area1_x2_axis_index + database, (database_value)(-200));
                database_write_no_callback(beam_area2_x2_axis_index + database, (database_value)(-50));
                database_write_no_callback(beam_area3_x2_axis_index + database, (database_value)(-150));
                database_write_no_callback(beam_area4_x2_axis_index + database, (database_value)(-320));
                database_write_no_callback(beam_area1_y2_axis_index + database, (database_value)(100));
                database_write_no_callback(beam_area2_y2_axis_index + database, (database_value)(100));
                database_write_no_callback(beam_area3_y2_axis_index + database, (database_value)(-100));
                database_write_no_callback(beam_area4_y2_axis_index + database, (database_value)(-100));
                database_write_no_callback(beam_area1_width_index + database, (database_value)4);
                database_write_no_callback(beam_area2_width_index + database, (database_value)4);
                database_write_no_callback(beam_area3_width_index + database, (database_value)4);
                database_write_no_callback(beam_area4_width_index + database, (database_value)4);
                database_write_no_callback(beam_area1_gain_index + database, (database_value)0);
                database_write_no_callback(beam_area2_gain_index + database, (database_value)3);
                database_write_no_callback(beam_area3_gain_index + database, (database_value)3);
                database_write_no_callback(beam_area4_gain_index + database, (database_value)0);
                database_write_no_callback(beam_area1_enabled_index + database, (database_value)1);
                database_write_no_callback(beam_area2_enabled_index + database, (database_value)1);
                database_write_no_callback(beam_area3_enabled_index + database, (database_value)1);
                database_write_no_callback(beam_area4_enabled_index + database, (database_value)1);
                database_write_no_callback(beam_area5_enabled_index + database, (database_value)0);
                database_write_no_callback(beam_area6_enabled_index + database, (database_value)0);
                database_write_no_callback(beam_area7_enabled_index + database, (database_value)0);
                database_write_no_callback(beam_area8_enabled_index + database, (database_value)0);
#elif defined(BD_MIXER_MATRIX_MIC)
                database_write_no_callback(beam_area1_x1_axis_index + database, (database_value)(-320));
                database_write_no_callback(beam_area1_y1_axis_index + database, (database_value)(100));
                database_write_no_callback(beam_area1_x2_axis_index + database, (database_value)(-200));
                database_write_no_callback(beam_area1_y2_axis_index + database, (database_value)(100));
                database_write_no_callback(beam_area1_enabled_index + database, (database_value)1);

                database_write_no_callback(beam_area2_x1_axis_index + database, (database_value)(-150));
                database_write_no_callback(beam_area2_y1_axis_index + database, (database_value)(100));
                database_write_no_callback(beam_area2_x2_axis_index + database, (database_value)(-50));
                database_write_no_callback(beam_area2_y2_axis_index + database, (database_value)(100));
                database_write_no_callback(beam_area2_enabled_index + database, (database_value)1);

                database_write_no_callback(beam_area3_x1_axis_index + database, (database_value)(-50));
                database_write_no_callback(beam_area3_y1_axis_index + database, (database_value)(-100));
                database_write_no_callback(beam_area3_x2_axis_index + database, (database_value)(-150));
                database_write_no_callback(beam_area3_y2_axis_index + database, (database_value)(-100));
                database_write_no_callback(beam_area3_enabled_index + database, (database_value)1);

                database_write_no_callback(beam_area4_x1_axis_index + database, (database_value)(-200));
                database_write_no_callback(beam_area4_y1_axis_index + database, (database_value)(-100));
                database_write_no_callback(beam_area4_x2_axis_index + database, (database_value)(-320));
                database_write_no_callback(beam_area4_y2_axis_index + database, (database_value)(-100));
                database_write_no_callback(beam_area4_enabled_index + database, (database_value)1);

                for (int k = 0; 4 > k; k++)
                {
                    database_write_no_callback(beam_area5_x1_axis_index + k + database, (database_value)0);
                    database_write_no_callback(beam_area5_y1_axis_index + k + database, (database_value)0);
                    database_write_no_callback(beam_area5_x2_axis_index + k + database, (database_value)0);
                    database_write_no_callback(beam_area5_y2_axis_index + k + database, (database_value)0);
                    database_write_no_callback(beam_area5_enabled_index + k + database, (database_value)0);

                    database_write_no_callback(beam_area1_width_index + k + database, (database_value)4);
                }
                database_write_no_callback(beam_area1_gain_index + database, (database_value)0);
                database_write_no_callback(beam_area2_gain_index + database, (database_value)3);
                database_write_no_callback(beam_area3_gain_index + database, (database_value)3);
                database_write_no_callback(beam_area4_gain_index + database, (database_value)0);

                database_write_no_callback(beam_width_level_index + database, (database_value)2);
                database_write_no_callback(beam_track_mode_index + database, (database_value)BEAM_MULTI_FIXED_AUTO_MIXER_MODE);
                database_write_no_callback(beam_track_x_axis_index + database, (database_value)0);
                database_write_no_callback(beam_track_z_axis_index + database, (database_value)0);
#endif

                database_write_no_callback(beam_attack_time_index + database, (database_value)120);
                database_write_no_callback(beam_release_time_index + database, (database_value)160);

                database_write_no_callback(database + peq_beamf_onoff_index, (database_value)1);
                database_write_no_callback(database + peq_beamf_cnt_index, (database_value)YM_PEQ_MAX_BANDS_NUM);

#if defined(BD_MIXER_LINE_MICS)
                if (index == 0)
                {
                    database_write2(database + peq_beamf_freq1_index, (database_value *)beam_band_freq, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_gain1_index, (database_value *)beam_band_gain, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_filter1_index, (database_value *)beam_filter_type, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_qval1_index, (database_value *)beam_band_qval, MIXER_PARAM_PEQ_MAX);
                }
                else if (index == 1)
                {
                    database_write2(database + peq_beamf_freq1_index, (database_value *)beam_band_freq_2, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_gain1_index, (database_value *)beam_band_gain_2, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_filter1_index, (database_value *)beam_filter_type_2, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_qval1_index, (database_value *)beam_band_qval_2, MIXER_PARAM_PEQ_MAX);
                }
                else if (index == 2)
                {
                    database_write2(database + peq_beamf_freq1_index, (database_value *)beam_band_freq_3, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_gain1_index, (database_value *)beam_band_gain_3, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_filter1_index, (database_value *)beam_filter_type_3, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_qval1_index, (database_value *)beam_band_qval_3, MIXER_PARAM_PEQ_MAX);
                }
                else if (index == 3)
                {
                    database_write2(database + peq_beamf_freq1_index, (database_value *)beam_band_freq_4, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_gain1_index, (database_value *)beam_band_gain_4, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_filter1_index, (database_value *)beam_filter_type_4, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_qval1_index, (database_value *)beam_band_qval_4, MIXER_PARAM_PEQ_MAX);
                }
                else if (index == 4)
                {
                    database_write2(database + peq_beamf_freq1_index, (database_value *)beam_band_freq_5, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_gain1_index, (database_value *)beam_band_gain_5, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_filter1_index, (database_value *)beam_filter_type_5, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_qval1_index, (database_value *)beam_band_qval_5, MIXER_PARAM_PEQ_MAX);
                }
                else if (index == 5)
                {
                    database_write2(database + peq_beamf_freq1_index, (database_value *)beam_band_freq_6, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_gain1_index, (database_value *)beam_band_gain_6, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_filter1_index, (database_value *)beam_filter_type_6, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_qval1_index, (database_value *)beam_band_qval_6, MIXER_PARAM_PEQ_MAX);
                }
                else if (index == 6)
                {
                    database_write2(database + peq_beamf_freq1_index, (database_value *)beam_band_freq_7, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_gain1_index, (database_value *)beam_band_gain_7, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_filter1_index, (database_value *)beam_filter_type_7, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_qval1_index, (database_value *)beam_band_qval_7, MIXER_PARAM_PEQ_MAX);
                }
                else
                {
                    database_write2(database + peq_beamf_freq1_index, (database_value *)beam_band_freq_8, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_gain1_index, (database_value *)beam_band_gain_8, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_filter1_index, (database_value *)beam_filter_type_8, MIXER_PARAM_PEQ_MAX);
                    database_write2(database + peq_beamf_qval1_index, (database_value *)beam_band_qval_8, MIXER_PARAM_PEQ_MAX);
                }
#elif defined(BD_MIXER_MATRIX_MIC)
                database_write2(database + peq_beamf_freq1_index, (database_value *)beam_band_freq, MIXER_PARAM_PEQ_MAX);
                database_write2(database + peq_beamf_gain1_index, (database_value *)beam_band_gain, MIXER_PARAM_PEQ_MAX);
                database_write2(database + peq_beamf_filter1_index, (database_value *)beam_filter_type, MIXER_PARAM_PEQ_MAX);
                database_write2(database + peq_beamf_qval1_index, (database_value *)beam_band_qval, MIXER_PARAM_PEQ_MAX);
#endif

                beam_area_set_auto_mixer_decay(beam_handle, 0, 0u);
                for (uint32_t i = 1; i < 8; i++)
                {
                    beam_area_set_auto_mixer_decay(beam_handle, i, 40u);
                }
                /* update beam library handle to beam mixer data structure */
                pbeam->handle = beam_handle;
                log_i("beam open successful. handle %p", beam_handle);
            }
            else
            {
                log_e("beam open error.");
                ret = -2;
            }
        }
    }
    else
        log_e("beam query mem failed. ret:%d", ret);

    return ret;
}

static int32_t beam_hw_init(void)
{
    if (ym_beam_latency_5ms_init() != 0)
    {
        log_e("ym_beam_latency_5ms_init fail.");
        return -1;
    }

    for (uint32_t i = 0u; i < BEAM_INSTANCE_NUMBER; i++)
    {
        sem_init(&(pbeam_handle[i]->input_notify_sem), 0, 0);
        pbeam_handle[i]->p_mixer_in = malloc(sizeof(int32_t *) * pbeam_handle[i]->ichns);
        pbeam_handle[i]->p_mixer_out = malloc(sizeof(int32_t *) * pbeam_handle[i]->ochns);
        pbeam_handle[i]->latency_id = LATENCY_5MS;
        pbeam_handle[i]->database_base_index = BEAM_BASE_INDEX + i * BEAM_PARAM_MAX;
        if (beam_query_open(pbeam_handle[i], i))
        {
            log_e("beam_query_open fail for instance %u, base %u", i, pbeam_handle[i]->database_base_index);
            return -1;
        }
    }

    return 0;
}
INIT_MEM_EXPORT(beam_hw_init);

static int32_t beam_stream_init(void)
{
#if BEAM_INSTANCE_NUMBER > 0
    beam.audio_out_stream = yinkman_stream_create(STREAM_OUT_BEAM_NAME, FRAMES, BEAM_OUT_CHANNELS * 4, 2);
#endif
#if BEAM_INSTANCE_NUMBER > 1
    beam2.audio_out_stream = yinkman_stream_create(STREAM_OUT_BEAM2_NAME, FRAMES, BEAM_OUT_CHANNELS * 4, 2);
#endif
#if BEAM_INSTANCE_NUMBER > 2
    beam3.audio_out_stream = yinkman_stream_create(STREAM_OUT_BEAM3_NAME, FRAMES, BEAM_OUT_CHANNELS * 4, 2);
#endif
#if BEAM_INSTANCE_NUMBER > 3
    beam4.audio_out_stream = yinkman_stream_create(STREAM_OUT_BEAM4_NAME, FRAMES, BEAM_OUT_CHANNELS * 4, 2);
#endif
#if BEAM_INSTANCE_NUMBER > 4
    beam5.audio_out_stream = yinkman_stream_create(STREAM_OUT_BEAM5_NAME, FRAMES, BEAM_OUT_CHANNELS * 4, 2);
#endif
#if BEAM_INSTANCE_NUMBER > 5
    beam6.audio_out_stream = yinkman_stream_create(STREAM_OUT_BEAM6_NAME, FRAMES, BEAM_OUT_CHANNELS * 4, 2);
#endif
#if BEAM_INSTANCE_NUMBER > 6
    beam7.audio_out_stream = yinkman_stream_create(STREAM_OUT_BEAM7_NAME, FRAMES, BEAM_OUT_CHANNELS * 4, 2);
#endif
#if BEAM_INSTANCE_NUMBER > 7
    beam8.audio_out_stream = yinkman_stream_create(STREAM_OUT_BEAM8_NAME, FRAMES, BEAM_OUT_CHANNELS * 4, 2);
#endif

    return 0;
}
INIT_STREAM_EXPORT(beam_stream_init);

static int32_t beam_comp_init(void)
{
#if defined(BD_MIXER_LINE_MIC)
    {
        INIT_BEAM_MIXER_STREAM_IN(a2b_1_src4, 1);
        INIT_BEAM_MIXER_STREAM_IN(a2b_1_src0, 1);
        INIT_BEAM_MIXER_STREAM_IN(a2b_1_src5, 1);
        INIT_BEAM_MIXER_STREAM_IN(a2b_1_src2, 1);
    }
    {
        INIT_BEAM2_MIXER_STREAM_IN(a2b_1_src6, 1);
        INIT_BEAM2_MIXER_STREAM_IN(a2b_1_src1, 1);
        INIT_BEAM2_MIXER_STREAM_IN(a2b_1_src3, 1);
        INIT_BEAM2_MIXER_STREAM_IN(a2b_1_src7, 1);
    }
    {
        INIT_BEAM3_MIXER_STREAM_IN(a2b_2_src4, 1);
        INIT_BEAM3_MIXER_STREAM_IN(a2b_2_src0, 1);
        INIT_BEAM3_MIXER_STREAM_IN(a2b_2_src5, 1);
        INIT_BEAM3_MIXER_STREAM_IN(a2b_2_src2, 1);
    }
    {
        INIT_BEAM4_MIXER_STREAM_IN(a2b_2_src6, 1);
        INIT_BEAM4_MIXER_STREAM_IN(a2b_2_src1, 1);
        INIT_BEAM4_MIXER_STREAM_IN(a2b_2_src3, 1);
        INIT_BEAM4_MIXER_STREAM_IN(a2b_2_src7, 1);
    }
    {
        INIT_BEAM5_MIXER_STREAM_IN(a2b_1_src4, 1);
        INIT_BEAM5_MIXER_STREAM_IN(a2b_1_src0, 1);
        INIT_BEAM5_MIXER_STREAM_IN(a2b_1_src5, 1);
        INIT_BEAM5_MIXER_STREAM_IN(a2b_1_src2, 1);
    }
    {
        INIT_BEAM5_MIXER_STREAM_IN(a2b_1_src6, 1);
        INIT_BEAM5_MIXER_STREAM_IN(a2b_1_src1, 1);
        INIT_BEAM5_MIXER_STREAM_IN(a2b_1_src3, 1);
        INIT_BEAM5_MIXER_STREAM_IN(a2b_1_src7, 1);
    }
    {
        INIT_BEAM7_MIXER_STREAM_IN(a2b_2_src4, 1);
        INIT_BEAM7_MIXER_STREAM_IN(a2b_2_src0, 1);
        INIT_BEAM7_MIXER_STREAM_IN(a2b_2_src5, 1);
        INIT_BEAM7_MIXER_STREAM_IN(a2b_2_src2, 1);
    }
    {
        INIT_BEAM8_MIXER_STREAM_IN(a2b_2_src6, 1);
        INIT_BEAM8_MIXER_STREAM_IN(a2b_2_src1, 1);
        INIT_BEAM8_MIXER_STREAM_IN(a2b_2_src3, 1);
        INIT_BEAM8_MIXER_STREAM_IN(a2b_2_src7, 1);
    }
#elif defined(BD_MIXER_MATRIX_MIC)
    {
        INIT_BEAM_MIXER_STREAM_IN(es7210_src3, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src19, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src18, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src2, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src11, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src27, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src26, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src10, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src1, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src17, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src16, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src0, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src9, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src25, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src24, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src8, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src5, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src21, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src20, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src4, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src13, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src29, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src28, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src12, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src7, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src23, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src22, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src6, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src15, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src31, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src30, 1)
        INIT_BEAM_MIXER_STREAM_IN(es7210_src14, 1)
    }
    {
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src3, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src19, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src18, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src2, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src11, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src27, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src26, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src10, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src1, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src17, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src16, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src0, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src9, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src25, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src24, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src8, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src5, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src21, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src20, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src4, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src13, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src29, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src28, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src12, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src7, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src23, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src22, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src6, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src15, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src31, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src30, 1)
        INIT_BEAM2_MIXER_STREAM_IN(es7210_src14, 1)
    }
#endif
    return 0;
}
INIT_COMPONENT_EXPORT(beam_comp_init);

static void beam_first_frame_handle(mixer_def_p pbeam)
{
    int32_t *in_buffer = malloc(sizeof(int32_t) * FRAMES * pbeam->ichns);
    int32_t *out_buffer = malloc(sizeof(int32_t) * FRAMES * pbeam->ochns);
    audio_set_chn_map(pbeam->p_mixer_in, in_buffer, pbeam->ichns, FRAMES);
    audio_set_chn_map(pbeam->p_mixer_out, out_buffer, pbeam->ochns, FRAMES);
    pthread_mutex_lock(&pbeam->pmutex);
    beam_process(pbeam->handle,
                 (const int32_t **)pbeam->p_mixer_in, 1,
                 pbeam->p_mixer_out, 1,
                 NULL);
    pthread_mutex_unlock(&pbeam->pmutex);
    free(in_buffer);
    free(out_buffer);

    /* workaround for beam params not invalue after beam_open. set beam database again */
    beam_lib_param_default();
}

/**
 * AEC REF通道电平值、AFC 通道电平值、AEC 远端信号电平值。取最大值。
 */
static uint8_t beam_far_end_sig_leveldB(mixer_def_p mixer)
{
    int32_t sig_level[MIXER_INPUT_CHN_MAX + 1];
    int32_t ichn_mode;
    uint32_t i = 0u;
    int32_t max_level = -96 * 2;

    /* 找到sig_level的最大值并返回 */
    for (i = 0u; i < MIXER_INPUT_CHN_MAX; i++)
    {
        ichn_mode = database_get(mixer->database_base_index + input_channel1_aec_mode_ms_index + i)->i32;
        if ((ichn_mode == YM_MATRIX_INPUT_AEC_REF) ||
            (ichn_mode == YM_MATRIX_INPUT_AEC_FAREND_REF))
        {
            sig_level[i] = database_get(mixer_input_channel1_sig_db_ms_index + mixer->database_base_index + i)->i32;
        }
        else
            sig_level[i] = -96 * 2;

        max_level = (sig_level[i] > max_level) ? sig_level[i] : max_level;
    }
    sig_level[MIXER_INPUT_CHN_MAX] = int2dB((int32_t)(2147483647.0f * mixerGetAfcRefMSE[mixer->latency_id](mixer->handle)));
    max_level = (sig_level[MIXER_INPUT_CHN_MAX] > max_level) ? sig_level[MIXER_INPUT_CHN_MAX] : max_level;

    return (uint8_t)max_level;
}

int32_t beam_semphore_handle(int32_t argc, void *argv)
{
    mixer_def_p pbeam = (mixer_def_p)argv;
    if (argc == 1)
        beam_first_frame_handle(pbeam);
    else
    {
        mixer_wait_audio_in(pbeam);
        if (mixer_out_stream_alloc(pbeam))
        {
            mixer_audio_in_free(pbeam);
            return -1;
        }
    }
    return 0;
}

int32_t beam_thread_handle(void *arg, void *argv)
{
    database_value tmp;
    uint64_t start_tick;
    uint64_t end_tick;
    mixer_def_p pbeam = (mixer_def_p)arg;
    int32_t database = pbeam->database_base_index;

    mixer_def_p mixer =(mixer_def_p)argv;

    start_tick = ym_tick_get_us();

    tmp.u32 = database_get(beam_process_count_index + database)->u32 + 1;
    database_write_no_callback(beam_process_count_index + database, tmp);
    beam_set_far_end_flag(pbeam->handle, beam_far_end_sig_leveldB(mixer) / 2);
    beam_set_vad_prob(pbeam->handle, mixer_get_aec_vad_prob[mixer->latency_id](mixer->handle));
    pthread_mutex_lock(&pbeam->pmutex);
    beam_process(pbeam->handle, (const int32_t **)pbeam->p_mixer_in, 1, pbeam->p_mixer_out, 1, NULL);
    pthread_mutex_unlock(&pbeam->pmutex);
    ym_mixer_setLowDelayDsGain[mixer->latency_id](mixer->handle, beam_get_lowDelayDsGain(pbeam->handle));

    if (database_get(beam_channel0_out_id_index + database)->u32 < BEAM_IN_CHANNELS)
    {
        memcpy(pbeam->p_mixer_out[0], pbeam->p_mixer_in[database_get(beam_channel0_out_id_index + database)->u32], FRAMES * 4);
    }
    if (database_get(beam_channel1_out_id_index + database)->u32 < BEAM_IN_CHANNELS)
    {
        memcpy(pbeam->p_mixer_out[1], pbeam->p_mixer_in[database_get(beam_channel1_out_id_index + database)->u32], FRAMES * 4);
    }

    mixer_audio_in_free(pbeam);
    yinkman_stream_push(pbeam->audio_out_stream, pbeam->p_mixer_out[0]);

    end_tick = ym_tick_get_us();
    tmp.f32 = (float)(end_tick - start_tick);
    if (tmp.f32 > FRAMES_UNIT_US)
    {
        log_e("%s over load:%fus", __FUNCTION__, tmp.f32);
        // TODO:清空一次输入
    }
    tmp.f32 = sliding_average_filter(pbeam->load_filter, tmp.f32);
    database_write_no_callback(beam_process_tick_index + database, tmp);

    return 0;
}

static int32_t beam_thread_handle_init(void)
{
    for (uint32_t i = 0u; i < BEAM_INSTANCE_NUMBER; i++)
    {
        pbeam_handle[i]->load_filter = sliding_average_filter_create(100);
    }

    return 0;
}
INIT_APP_EXPORT(beam_thread_handle_init);

static void beam_peq_callback(int32_t index, database_value old_value, database_value new_value)
{
    int32_t ret = 0;
    int32_t inst = (index - BEAM_BASE_INDEX) / BEAM_PARAM_MAX;
    int32_t id = (index - BEAM_BASE_INDEX) % BEAM_PARAM_MAX;

    switch (id)
    {
    case peq_beamf_onoff_index:
        pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
        ret = beam_set_peq_onOff(pbeam_handle[inst]->handle, new_value.u32);
        pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
        if (ret != 0)
        {
            log_e("beam_set_peq_onOff failed.");
            database_write_no_callback(index, old_value);
        }
        break;
    case peq_beamf_cnt_index:
        pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
        ret = beam_set_peq_bandNum(pbeam_handle[inst]->handle, (new_value.u32 > BEAM_PEQ_MAX_BANDS_NUM) ? BEAM_PEQ_MAX_BANDS_NUM : new_value.u32);
        pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
        if (ret != 0)
        {
            log_e("beam_set_peq_bandNum failed.");
            database_write_no_callback(index, old_value);
        }
        break;
    case peq_beamf_freq1_index:
    case peq_beamf_freq2_index:
    case peq_beamf_freq3_index:
    case peq_beamf_freq4_index:
    case peq_beamf_freq5_index:
    case peq_beamf_freq6_index:
    case peq_beamf_freq7_index:
    case peq_beamf_freq8_index:
        pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
        ret = beam_set_peq_bandFreqs(pbeam_handle[inst]->handle, (int16_t)new_value.u32, (uint32_t)(id - peq_beamf_freq1_index));
        pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
        if (ret != 0)
        {
            log_e("beam_set_peq_bandFreqs failed.");
            database_write_no_callback(index, old_value);
        }
        break;
    case peq_beamf_gain1_index:
    case peq_beamf_gain2_index:
    case peq_beamf_gain3_index:
    case peq_beamf_gain4_index:
    case peq_beamf_gain5_index:
    case peq_beamf_gain6_index:
    case peq_beamf_gain7_index:
    case peq_beamf_gain8_index:
        pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
        ret = beam_set_peq_bandGains(pbeam_handle[inst]->handle, (int16_t)new_value.u32, (uint32_t)(id - peq_beamf_gain1_index));
        pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
        if (ret != 0)
        {
            log_e("beam_set_peq_bandGains failed.");
            database_write_no_callback(index, old_value);
        }
        break;
    case peq_beamf_filter1_index:
    case peq_beamf_filter2_index:
    case peq_beamf_filter3_index:
    case peq_beamf_filter4_index:
    case peq_beamf_filter5_index:
    case peq_beamf_filter6_index:
    case peq_beamf_filter7_index:
    case peq_beamf_filter8_index:
        pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
        ret = beam_set_peq_filterType(pbeam_handle[inst]->handle, (int16_t)new_value.u32, (uint32_t)(id - peq_beamf_filter1_index));
        pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
        if (ret != 0)
        {
            log_e("beam_set_peq_filterType failed.");
            database_write_no_callback(index, old_value);
        }
        break;
    case peq_beamf_qval1_index:
    case peq_beamf_qval2_index:
    case peq_beamf_qval3_index:
    case peq_beamf_qval4_index:
    case peq_beamf_qval5_index:
    case peq_beamf_qval6_index:
    case peq_beamf_qval7_index:
    case peq_beamf_qval8_index:
        pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
        ret = beam_set_peq_Qvalue(pbeam_handle[inst]->handle, (int16_t)new_value.u32, (uint32_t)(id - peq_beamf_qval1_index));
        pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
        if (ret != 0)
        {
            log_e("beam_set_peq_Qvalue failed.");
            database_write_no_callback(index, old_value);
        }
        break;
    default:
        break;
    }
}

static void beam_width_level_callback(int index, database_value old_value, database_value new_value)
{
    int32_t ret = 0;
    int32_t inst = (index - BEAM_BASE_INDEX) / BEAM_PARAM_MAX;

    pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
    ret = beam_set_width(pbeam_handle[inst]->handle, new_value.i32);
    pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
    if (ret != 0)
    {
        log_e("beam_set_width failed.");
        database_write_no_callback(index, old_value);
    }
}

static void beam_mode_callback(int index, database_value old_value, database_value new_value)
{
    int32_t ret = 0;
    int32_t inst = (index - BEAM_BASE_INDEX) / BEAM_PARAM_MAX;

    pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
    ret = beam_set_mode(pbeam_handle[inst]->handle, new_value.i32);
    pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
    if (ret != 0)
    {
        log_e("beam_mode_callback failed.");
        database_write_no_callback(index, old_value);
    }
}

static void beam_track_mode_callback(int index, database_value old_value, database_value new_value)
{
    int32_t ret = 0;
    int32_t inst = (index - BEAM_BASE_INDEX) / BEAM_PARAM_MAX;
    int32_t id = (index - BEAM_BASE_INDEX) % BEAM_PARAM_MAX;
    int32_t database = pbeam_handle[inst]->database_base_index;

    switch (id)
    {
    case beam_track_mode_index:
        switch (new_value.i32)
        {
        case 0: /* direct to specified angle */
            pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
            ret = beam_set_fixed_beamformer_polar(pbeam_handle[inst]->handle,
                                                  database_get(database + beam_track_z_axis_index)->i32,
                                                  database_get(database + beam_track_x_axis_index)->i32);
            pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
            if (ret != 0)
            {
                log_e("beam_set_fixed_beamformer_polar failed.");
                database_write_no_callback(index, old_value);
            }
            break;
        case 1: /* auto-trace mode */
            pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
            ret = beam_set_adaptive_beamformer(pbeam_handle[inst]->handle);
            pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
            if (ret != 0)
            {
                log_e("beam_set_adaptive_beamformer failed.");
                database_write_no_callback(index, old_value);
            }
            break;
        case 2: /* outside aec mode */
            pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
            ret = beam_set_outside_aec_beamformer_polar(pbeam_handle[inst]->handle,
                                                        database_get(database + beam_track_z_axis_index)->i32,
                                                        database_get(database + beam_track_x_axis_index)->i32);
            pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
            if (ret != 0)
            {
                log_e("beam_set_outside_aec_beamformer_polar failed.");
                database_write_no_callback(index, old_value);
            }
            break;
        case BEAM_MULTI_FIXED_AUTO_MIXER_MODE:
        case BEAM_MULTI_FIXED_DOWN_MIX_MODE:
        case BEAM_MULTI_OBJECTIVE_MODE:
        case BEAM_MULTI_OBJECTIVE_ZERO_MODE:
        case BEAM_MULTI_FIXED_SELECT_MODE:
            pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
            ret = beam_set_track_mode(pbeam_handle[inst]->handle, (BEAM_TRACK_TYPE)(new_value.i32));
            pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
            if (ret != 0)
            {
                log_e("beam_set_track_mode failed.");
                database_write_no_callback(index, old_value);
            }
            break;
        default:
            break;
        }
        break;
    case beam_track_x_axis_index:
    case beam_track_z_axis_index:
        if (database_get(beam_track_mode_index)->i32 == 0)
        {
            pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
            ret = beam_set_fixed_beamformer_polar(pbeam_handle[inst]->handle,
                                                  database_get(database + beam_track_z_axis_index)->i32,
                                                  database_get(database + beam_track_x_axis_index)->i32);
            pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
            if (ret != 0)
            {
                log_e("beam_set_fixed_beamformer_polar failed.");
                database_write_no_callback(index, old_value);
            }
        }
        else if (database_get(beam_track_mode_index)->i32 == 2)
        {
            pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
            ret = beam_set_outside_aec_beamformer_polar(pbeam_handle[inst]->handle,
                                                        database_get(database + beam_track_z_axis_index)->i32,
                                                        database_get(database + beam_track_x_axis_index)->i32);
            pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
            if (ret != 0)
            {
                log_e("beam_set_outside_aec_beamformer_polar failed.");
                database_write_no_callback(index, old_value);
            }
        }
        break;
    default:
        break;
    }
}

static void beam_attach_time_callback(int index, database_value old_value, database_value new_value)
{
    int32_t ret = 0;
    int32_t inst = (index - BEAM_BASE_INDEX) / BEAM_PARAM_MAX;

    pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
    ret = beam_set_auto_mixer_attack_gain(pbeam_handle[inst]->handle, new_value.i32);
    pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
    if (ret != 0)
    {
        log_e("beam_set_auto_mixer_attack_gain failed.");
        database_write_no_callback(index, old_value);
    }
}

static void beam_release_time_callback(int index, database_value old_value, database_value new_value)
{
    int32_t ret = 0;
    int32_t inst = (index - BEAM_BASE_INDEX) / BEAM_PARAM_MAX;

    pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
    ret = beam_set_auto_mixer_release_gain(pbeam_handle[inst]->handle, new_value.i32);
    pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
    if (ret != 0)
    {
        log_e("beam_set_auto_mixer_release_gain failed.");
        database_write_no_callback(index, old_value);
    }
}

static void set_fixed_beam_area_callback(int index, database_value old_value, database_value new_value)
{
    int ret = 0;
    int32_t inst = (index - BEAM_BASE_INDEX) / BEAM_PARAM_MAX;
    int32_t id = (index - BEAM_BASE_INDEX) % BEAM_PARAM_MAX;
    int32_t database = pbeam_handle[inst]->database_base_index;

    switch (id)
    {
    case beam_area1_enabled_index:
    case beam_area2_enabled_index:
    case beam_area3_enabled_index:
    case beam_area4_enabled_index:
    case beam_area5_enabled_index:
    case beam_area6_enabled_index:
    case beam_area7_enabled_index:
    case beam_area8_enabled_index:
        pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
        ret = beam_set_fixed_beam_area(pbeam_handle[inst]->handle,
                                       (id - beam_area1_enabled_index),
                                       database_get(database + id)->i32,
                                       database_get(database + beam_area1_x1_axis_index + (id - beam_area1_enabled_index))->i32,
                                       database_get(database + beam_area1_y1_axis_index + (id - beam_area1_enabled_index))->i32,
                                       database_get(database + beam_area1_x2_axis_index + (id - beam_area1_enabled_index))->i32,
                                       database_get(database + beam_area1_y2_axis_index + (id - beam_area1_enabled_index))->i32);
        pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
        if (ret != 0)
        {
            log_e("beam_set_fixed_beam_area failed.");
            database_write_no_callback(index, old_value);
        }
        break;
    case beam_area1_width_index:
    case beam_area2_width_index:
    case beam_area3_width_index:
    case beam_area4_width_index:
    case beam_area5_width_index:
    case beam_area6_width_index:
    case beam_area7_width_index:
    case beam_area8_width_index:
        pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
        ret = beam_set_area_beam_width(pbeam_handle[inst]->handle, (id - beam_area1_width_index), new_value.i32);
        pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
        if (ret != 0)
        {
            log_e("beam_set_area_beam_width failed.");
            database_write_no_callback(index, old_value);
        }
        break;
    case beam_area1_gain_index:
    case beam_area2_gain_index:
    case beam_area3_gain_index:
    case beam_area4_gain_index:
    case beam_area5_gain_index:
    case beam_area6_gain_index:
    case beam_area7_gain_index:
    case beam_area8_gain_index:
        pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
        ret = beam_area_set_auto_mixer_gain(pbeam_handle[inst]->handle, (id - beam_area1_gain_index), new_value.i32);
        pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
        if (ret != 0)
        {
            log_e("beam_area_set_auto_mixer_gain failed.");
            database_write_no_callback(index, old_value);
        }
        break;
    default:
        break;
    }
}

static void beam_channel_out_callback(int index, database_value old_value, database_value new_value)
{
    int ret = 0;
    int32_t inst = (index - BEAM_BASE_INDEX) / BEAM_PARAM_MAX;
    int32_t id = (index - BEAM_BASE_INDEX) % BEAM_PARAM_MAX;
    int32_t database = pbeam_handle[inst]->database_base_index;

    switch (id)
    {
    case beam_channel0_out_id_index:
        pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
        ret = beam_set_out_id(pbeam_handle[inst]->handle, new_value.i32, database_get(beam_channel1_out_id_index + database)->i32);
        pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
        if (ret != 0)
        {
            log_e("beam_set_out_id failed.");
            database_write_no_callback(index, old_value);
        }
        break;
    case beam_channel1_out_id_index:
        pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
        ret = beam_set_out_id(pbeam_handle[inst]->handle, database_get(beam_channel0_out_id_index + database)->i32, new_value.i32);
        pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
        if (ret != 0)
        {
            log_e("beam_set_out_id failed.");
            database_write_no_callback(index, old_value);
        }
        break;
    default:
        break;
    }
}

static void beam_gain_callback(int index, database_value old_value, database_value new_value)
{
    int ret = 0;
    int32_t inst = (index - BEAM_BASE_INDEX) / BEAM_PARAM_MAX;
    int32_t id = (index - BEAM_BASE_INDEX) % BEAM_PARAM_MAX;

    switch (id)
    {
    case beam_input1_gain_index:
    case beam_input2_gain_index:
    case beam_input3_gain_index:
    case beam_input4_gain_index:
    case beam_input5_gain_index:
    case beam_input6_gain_index:
    case beam_input7_gain_index:
    case beam_input8_gain_index:
        /* just handle the input channel that valid in the platform */
        if ((id - beam_input1_gain_index) >= BEAM_IN_CHANNELS)
            break;

        pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
        ret = beam_set_in_gain(pbeam_handle[inst]->handle, (id - beam_input1_gain_index), new_value.i32);
        pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
        if (ret != 0)
        {
            log_e("beam_set_in_gain failed.");
            database_write_no_callback(index, old_value);
        }
        break;
    case beam_output_gain_index:
        pthread_mutex_lock(&(pbeam_handle[inst]->pmutex));
        ret = beam_set_out_gain(pbeam_handle[inst]->handle, new_value.i32);
        pthread_mutex_unlock(&(pbeam_handle[inst]->pmutex));
        if (ret != 0)
        {
            log_e("beam_set_out_gain failed.");
            database_write_no_callback(index, old_value);
        }
        break;
    default:
        break;
    }
}

static int32_t beam_semphore_callback(int32_t argc, void *argv)
{
    (void)argv;
    int32_t error = 0;

    for (uint32_t i = 0u; i < (BEAM_INSTANCE_NUMBER >> 1); i++)
    {
        error += beam_semphore_handle(argc, pbeam_handle[i]);
    }

    return error;
}

static int32_t beam_process_callback(int32_t argc, void *argv)
{
    (void)argc;
    int error = 0;

    for (uint32_t i = 0u; i < (BEAM_INSTANCE_NUMBER >> 1); i++)
    {
        error += beam_thread_handle(pbeam_handle[i], argv);
    }

    return error;
}

static int32_t sbeam_semphore_callback(int32_t argc, void *argv)
{
    (void)argv;
    int error = 0;

    for (uint32_t i = (BEAM_INSTANCE_NUMBER >> 1); i < BEAM_INSTANCE_NUMBER; i++)
    {
        error += beam_semphore_handle(argc, pbeam_handle[i]);
    }

    return error;
}

static int32_t sbeam_process_callback(int32_t argc, void *argv)
{
    (void)argc;
    int32_t error = 0;

    for (uint32_t i = (BEAM_INSTANCE_NUMBER >> 1); i < BEAM_INSTANCE_NUMBER; i++)
    {
        error += beam_thread_handle(pbeam_handle[i], argv);
    }

    return error;
}

void beam_param_register(void)
{
    log_i("register beam parameters callback");
    int32_t database = 0;

    beam_semphore_callback_register(beam_semphore_callback, 1);
    beam_process_callback_register(beam_process_callback, 1);
    beam_semphore_callback_register(sbeam_semphore_callback, 0);
    beam_process_callback_register(sbeam_process_callback, 0);

    for (uint32_t i = 0; i < BEAM_INSTANCE_NUMBER; i++)
    {
        database = pbeam_handle[i]->database_base_index;
        database_register(database + beam_mode_index, beam_mode_callback);
        database_register(database + beam_width_level_index, beam_width_level_callback);
        database_register(database + beam_track_mode_index, beam_track_mode_callback);
        database_register(database + beam_track_x_axis_index, beam_track_mode_callback);
        database_register(database + beam_track_z_axis_index, beam_track_mode_callback);
        database_register(database + beam_attack_time_index, beam_attach_time_callback);
        database_register(database + beam_release_time_index, beam_release_time_callback);
        database_register(database + beam_area1_enabled_index, set_fixed_beam_area_callback);
        database_register(database + beam_area2_enabled_index, set_fixed_beam_area_callback);
        database_register(database + beam_area3_enabled_index, set_fixed_beam_area_callback);
        database_register(database + beam_area4_enabled_index, set_fixed_beam_area_callback);
        database_register(database + beam_area5_enabled_index, set_fixed_beam_area_callback);
        database_register(database + beam_area6_enabled_index, set_fixed_beam_area_callback);
        database_register(database + beam_area7_enabled_index, set_fixed_beam_area_callback);
        database_register(database + beam_area8_enabled_index, set_fixed_beam_area_callback);
        database_register(database + beam_area1_width_index, set_fixed_beam_area_callback);
        database_register(database + beam_area2_width_index, set_fixed_beam_area_callback);
        database_register(database + beam_area3_width_index, set_fixed_beam_area_callback);
        database_register(database + beam_area4_width_index, set_fixed_beam_area_callback);
        database_register(database + beam_area5_width_index, set_fixed_beam_area_callback);
        database_register(database + beam_area6_width_index, set_fixed_beam_area_callback);
        database_register(database + beam_area7_width_index, set_fixed_beam_area_callback);
        database_register(database + beam_area8_width_index, set_fixed_beam_area_callback);
        database_register(database + beam_area1_gain_index, set_fixed_beam_area_callback);
        database_register(database + beam_area2_gain_index, set_fixed_beam_area_callback);
        database_register(database + beam_area3_gain_index, set_fixed_beam_area_callback);
        database_register(database + beam_area4_gain_index, set_fixed_beam_area_callback);
        database_register(database + beam_area5_gain_index, set_fixed_beam_area_callback);
        database_register(database + beam_area6_gain_index, set_fixed_beam_area_callback);
        database_register(database + beam_area7_gain_index, set_fixed_beam_area_callback);
        database_register(database + beam_area8_gain_index, set_fixed_beam_area_callback);
        database_register(database + beam_channel0_out_id_index, beam_channel_out_callback);
        database_register(database + beam_channel1_out_id_index, beam_channel_out_callback);
        database_register(database + peq_beamf_onoff_index, beam_peq_callback);
        database_register(database + peq_beamf_cnt_index,   beam_peq_callback);
        database_register(database + peq_beamf_freq1_index, beam_peq_callback);
        database_register(database + peq_beamf_freq2_index, beam_peq_callback);
        database_register(database + peq_beamf_freq3_index, beam_peq_callback);
        database_register(database + peq_beamf_freq4_index, beam_peq_callback);
        database_register(database + peq_beamf_freq5_index, beam_peq_callback);
        database_register(database + peq_beamf_freq6_index, beam_peq_callback);
        database_register(database + peq_beamf_freq7_index, beam_peq_callback);
        database_register(database + peq_beamf_freq8_index, beam_peq_callback);
        database_register(database + peq_beamf_gain1_index, beam_peq_callback);
        database_register(database + peq_beamf_gain2_index, beam_peq_callback);
        database_register(database + peq_beamf_gain3_index, beam_peq_callback);
        database_register(database + peq_beamf_gain4_index, beam_peq_callback);
        database_register(database + peq_beamf_gain5_index, beam_peq_callback);
        database_register(database + peq_beamf_gain6_index, beam_peq_callback);
        database_register(database + peq_beamf_gain7_index, beam_peq_callback);
        database_register(database + peq_beamf_gain8_index, beam_peq_callback);
        database_register(database + peq_beamf_filter1_index, beam_peq_callback);
        database_register(database + peq_beamf_filter2_index, beam_peq_callback);
        database_register(database + peq_beamf_filter3_index, beam_peq_callback);
        database_register(database + peq_beamf_filter4_index, beam_peq_callback);
        database_register(database + peq_beamf_filter5_index, beam_peq_callback);
        database_register(database + peq_beamf_filter6_index, beam_peq_callback);
        database_register(database + peq_beamf_filter7_index, beam_peq_callback);
        database_register(database + peq_beamf_filter8_index, beam_peq_callback);
        database_register(database + peq_beamf_qval1_index, beam_peq_callback);
        database_register(database + peq_beamf_qval2_index, beam_peq_callback);
        database_register(database + peq_beamf_qval3_index, beam_peq_callback);
        database_register(database + peq_beamf_qval4_index, beam_peq_callback);
        database_register(database + peq_beamf_qval5_index, beam_peq_callback);
        database_register(database + peq_beamf_qval6_index, beam_peq_callback);
        database_register(database + peq_beamf_qval7_index, beam_peq_callback);
        database_register(database + peq_beamf_qval8_index, beam_peq_callback);
        database_register(database + beam_input1_gain_index, beam_gain_callback);
        database_register(database + beam_input2_gain_index, beam_gain_callback);
        database_register(database + beam_input3_gain_index, beam_gain_callback);
        database_register(database + beam_input4_gain_index, beam_gain_callback);
        database_register(database + beam_input5_gain_index, beam_gain_callback);
        database_register(database + beam_input6_gain_index, beam_gain_callback);
        database_register(database + beam_input7_gain_index, beam_gain_callback);
        database_register(database + beam_input8_gain_index, beam_gain_callback);
        database_register(database + beam_output_gain_index, beam_gain_callback);
    }
}

void beam_lib_param_default(void)
{
    int32_t database = 0;

    for (uint32_t i = 0; i < BEAM_INSTANCE_NUMBER; i++)
    {
        database = pbeam_handle[i]->database_base_index;

        database_write(beam_mode_index + database, database_read(beam_mode_index + database));
        database_write(beam_width_level_index + database, database_read(beam_width_level_index + database));
        database_write(beam_track_mode_index + database, database_read(beam_track_mode_index + database));
        database_write(beam_attack_time_index + database, database_read(beam_attack_time_index + database));
        database_write(beam_release_time_index + database, database_read(beam_release_time_index + database));
        database_write(beam_channel1_out_id_index + database, database_read(beam_channel1_out_id_index + database));

        database_write2(beam_area1_enabled_index + database, database_get(beam_area1_enabled_index + database), 24);
        database_write2(peq_beamf_onoff_index + database, database_get(peq_beamf_onoff_index + database), 34);
    }

    log_i("beam_lib_param_default done");
}

int beam_database_start_get(int instance)
{
    return (instance < BEAM_INSTANCE_NUMBER) ? (pbeam_handle[instance]->database_base_index) : (-1);
}

static int32_t beam_polar(int32_t argc, char **argv)
{
    (void)argc;
    (void)argv;

    int32_t x_z[2] = {0, 0};
    for (uint32_t i = 0u; i < BEAM_INSTANCE_NUMBER; i++)
    {
        pthread_mutex_lock(&(pbeam_handle[i]->pmutex));
        beam_get_sound_source_location_polar(pbeam_handle[i]->handle, &x_z[0], &x_z[1]);
        pthread_mutex_unlock(&(pbeam_handle[i]->pmutex));
        printf("beam instance %u, ssl: %d, %d\n", i, x_z[0], x_z[1]);
    }

    return 0;
}
MSH_CMD_EXPORT_ALIAS(beam_polar, bmpolar, beam sound source polarity get);

static int32_t beam_track_mode(int32_t argc, char **argv)
{
    if (argc < 5)
    {
        printf("Usage: beam_track_mode [id] [mode] [z] [x]\n");
        return -1;
    }

    int32_t result = 0;
    int32_t id = atoi(argv[1]);
    if (id >= BEAM_INSTANCE_NUMBER)
    {
        printf("beam id is out of range. should less than %u\n", BEAM_INSTANCE_NUMBER);
        return -1;
    }

    int32_t mode = atoi(argv[2]);
    int32_t x_z[2] = {atoi(argv[3]), atoi(argv[4])};
    int32_t database = pbeam_handle[id]->database_base_index;
    result = database_write_no_callback(beam_track_z_axis_index + database, (database_value)x_z[0]);
    result += database_write_no_callback(beam_track_x_axis_index + database, (database_value)x_z[1]);
    result += database_write(beam_track_mode_index + database, (database_value)mode);

    MSH_RESULT_PRINT(result);

    return 0;
}
MSH_CMD_EXPORT_ALIAS(beam_track_mode, bmtrack, beam track mode set);

static int32_t beam_info(int32_t argc, char **argv)
{
    if (argc < 8)
    {
        printf("Usage: beam_info [id] [iBeam] [enabled] [theta] [phi] [r] [width]\n");
        return -1;
    }

    int32_t id = atoi(argv[1]);
    if (id >= BEAM_INSTANCE_NUMBER)
    {
        printf("beam id is out of range. should less than %u\n", BEAM_INSTANCE_NUMBER);
        return -1;
    }

    int32_t result = 0;
    int32_t iBeam = atoi(argv[2]);
    int32_t enabled = atoi(argv[3]);
    int32_t theta = atoi(argv[4]);
    int32_t phi = atoi(argv[5]);
    int32_t r = atoi(argv[6]);
    int32_t width = atoi(argv[7]);

    pthread_mutex_lock(&(pbeam_handle[id]->pmutex));
    result = beam_set_fixed_beam_info(pbeam_handle[id]->handle, iBeam, enabled, theta, phi, r, width);
    pthread_mutex_unlock(&(pbeam_handle[id]->pmutex));

    MSH_RESULT_PRINT(result);

    return 0;
}
MSH_CMD_EXPORT_ALIAS(beam_info, bminfo, beam info set);

int32_t beam_info_print(int32_t argc, char **argv)
{
    if (argc < 2)
    {
        printf("Usage: beam_info_print [id]\n");
        return -1;
    }

    int32_t id = atoi(argv[1]);
    if (id > BEAM_INSTANCE_NUMBER)
    {
        printf("beam id is out of range. should less than %u\n", BEAM_INSTANCE_NUMBER);
        return -1;
    }

    int32_t vad_flag = 0;

    if (id < BEAM_INSTANCE_NUMBER)
    {
        pthread_mutex_lock(&(pbeam_handle[id]->pmutex));
        beam_print_fixed_beam_info(pbeam_handle[id]->handle);
        beam_get_vad_flag(pbeam_handle[id]->handle, &vad_flag);
        pthread_mutex_unlock(&(pbeam_handle[id]->pmutex));
        printf("beam instance %u vad_flag: %d\n", id, vad_flag);
    }
    else
    {
        for (uint32_t i = 0u; i < BEAM_INSTANCE_NUMBER; i++)
        {
            pthread_mutex_lock(&(pbeam_handle[i]->pmutex));
            beam_print_fixed_beam_info(pbeam_handle[i]->handle);
            beam_get_vad_flag(pbeam_handle[i]->handle, &vad_flag);
            pthread_mutex_unlock(&(pbeam_handle[i]->pmutex));
            printf("beam instance %u vad_flag: %d\n", i, vad_flag);
        }
    }

    return 0;
}
MSH_CMD_EXPORT_ALIAS(beam_info_print, bminfoprint, beam info print);

static int32_t beam_att_rel_time(int32_t argc, char **argv)
{
    if (argc < 4)
    {
        printf("Usage: beam_att_rel_time [id] [attach] [release]\n");
        return -1;
    }

    int32_t id = atoi(argv[1]);
    if (id >= BEAM_INSTANCE_NUMBER)
    {
        printf("beam id is out of range. should less than %u\n", BEAM_INSTANCE_NUMBER);
        return -1;
    }

    int32_t result = 0;
    int32_t attack_time = atoi(argv[2]);
    int32_t release_time = atoi(argv[3]);

    result = database_write(beam_attack_time_index + pbeam_handle[id]->database_base_index, (database_value)attack_time);
    result += database_write(beam_release_time_index + pbeam_handle[id]->database_base_index, (database_value)release_time);

    MSH_RESULT_PRINT(result);

    return 0;
}
MSH_CMD_EXPORT_ALIAS(beam_att_rel_time, bmattrel, beam multi obj attach release time set);

static int32_t beam_width(int32_t argc, char *argv[])
{
    if (argc < 3)
    {
        printf("Usage: beam_width [id] [width]\n");
        return -1;
    }

    int32_t id = atoi(argv[1]);
    if (id >= BEAM_INSTANCE_NUMBER)
    {
        printf("beam id is out of range. should less than %u\n", BEAM_INSTANCE_NUMBER);
        return -1;
    }

    int32_t result = 0;
    int32_t width = atoi(argv[2]);

    result = database_write(beam_width_level_index + pbeam_handle[id]->database_base_index, (database_value)width);

    MSH_RESULT_PRINT(result);

    return 0;
}
MSH_CMD_EXPORT_ALIAS(beam_width, bmwidth, beam width set);

static int32_t beam_angle(int32_t argc, char *argv[])
{
    if (argc < 4)
    {
        printf("Usage: beam_angle [id] [theta] [phi]\n");
        return -1;
    }

    int32_t id = atoi(argv[1]);
    if (id >= BEAM_INSTANCE_NUMBER)
    {
        printf("beam id is out of range. should less than %u\n", BEAM_INSTANCE_NUMBER);
        return -1;
    }

    int32_t result = 0;
    int32_t theta = atoi(argv[2]);
    int32_t phi = atoi(argv[3]);

    result = database_write_no_callback(beam_track_z_axis_index + pbeam_handle[id]->database_base_index, (database_value)theta);
    result += database_write_no_callback(beam_track_x_axis_index + pbeam_handle[id]->database_base_index, (database_value)phi);
    result += database_write(beam_track_mode_index + pbeam_handle[id]->database_base_index, database_read(beam_track_mode_index + pbeam_handle[id]->database_base_index));

    MSH_RESULT_PRINT(result);

    return 0;
}
MSH_CMD_EXPORT_ALIAS(beam_angle, bmangle, beam angle set);

static int32_t beam_area(int32_t argc, char *argv[])
{
    if (argc < 8)
    {
        printf("Usage: beam_area [id] [areaId] [enable] [theta_min] [theta_max] [phi_min] [phi_max]\n");
        return -1;
    }

    int32_t id = atoi(argv[1]);
    if (id >= BEAM_INSTANCE_NUMBER)
    {
        printf("beam id is out of range. should less than %u\n", BEAM_INSTANCE_NUMBER);
        return -1;
    }

    int32_t result;
    int32_t areaId = atoi(argv[2]);
    int32_t enable = atoi(argv[3]);
    int32_t theta_min = atoi(argv[4]);
    int32_t theta_max = atoi(argv[5]);
    int32_t phi_min = atoi(argv[6]);
    int32_t phi_max = atoi(argv[7]);

    if (phi_min < BEAM_HDEGREE_MIN)
        phi_min = BEAM_HDEGREE_MIN;
    else if (phi_min > BEAM_HDEGREE_MAX)
        phi_min = BEAM_HDEGREE_MAX;

    if (phi_max < BEAM_HDEGREE_MIN)
        phi_max = BEAM_HDEGREE_MIN;
    else if (phi_max > BEAM_HDEGREE_MAX)
        phi_max = BEAM_HDEGREE_MAX;

    if (theta_min < BEAM_VDEGREE_MIN)
        theta_min = BEAM_VDEGREE_MIN;
    else if (theta_min > BEAM_VDEGREE_MAX)
        theta_min = BEAM_VDEGREE_MAX;

    if (theta_max < BEAM_VDEGREE_MIN)
        theta_max = BEAM_VDEGREE_MIN;
    else if (theta_max > BEAM_VDEGREE_MAX)
        theta_max = BEAM_VDEGREE_MAX;

    if (BEAM_PARAM_SHADOW_MAX <= areaId)
    {
        printf("areaId err\n");
        return -1;
    }

    if (!((phi_max >= phi_min) && (theta_max >= theta_min)))
    {
        printf("phi or theta err\n");
        return -1;
    }

    /* clear last shadow */
    do
    {
        pthread_mutex_lock(&(pbeam_handle[id]->pmutex));
        result = beam_set_direction_area(pbeam_handle[id]->handle,
                                         areaId + 1, /* must not set area 0 */
                                         0,
                                         SSL_DIR_PRIORITY_0,
                                         10, 20, 10, 20);
        pthread_mutex_unlock(&(pbeam_handle[id]->pmutex));
        usleep(10 * 1000);
    } while (result == RETURN_PROC_BUSY_ERROR);

    int32_t min = (theta_min > phi_min) ? phi_min : theta_min;
    if ((phi_min + phi_max + theta_min + theta_max) == (min * 4)) /* point */
        enable = 0;

    SSL_DIR_PRIORITY_TYPE priority = (areaId >= BEAM_SPK_SHADOW_START) ? SSL_DIR_FAR_END_SHADOW : SSL_DIR_SHADOW;
    do
    {
        pthread_mutex_lock(&(pbeam_handle[id]->pmutex));
        result = beam_set_direction_area(pbeam_handle[id]->handle,
                                         areaId + 1,
                                         enable,
                                         priority,
                                         theta_min,
                                         theta_max,
                                         phi_min,
                                         phi_max);
        pthread_mutex_unlock(&(pbeam_handle[id]->pmutex));
        usleep(10 * 1000);
    } while (result == RETURN_PROC_BUSY_ERROR);

    MSH_RESULT_PRINT(result);

    return 0;
}
MSH_CMD_EXPORT_ALIAS(beam_area, bmarea, beam area set);

int32_t beam_out_channel_set(uint32_t id, uint32_t lchn, uint32_t rchn)
{
    if (id >= BEAM_INSTANCE_NUMBER)
    {
        log_w("beam id is out of range. should less than %u\n", BEAM_INSTANCE_NUMBER);
        return -1;
    }

    database_write_no_callback(beam_channel0_out_id_index + pbeam_handle[id]->database_base_index, (database_value)lchn);
    database_write(beam_channel1_out_id_index + pbeam_handle[id]->database_base_index, (database_value)rchn);

    return 0;
}

int32_t beam_out_channel_get(uint32_t id, uint32_t *lchn, uint32_t *rchn)
{
    if (id >= BEAM_INSTANCE_NUMBER)
    {
        log_w("beam id is out of range. should less than %u\n", BEAM_INSTANCE_NUMBER);
        return -1;
    }

    *lchn = database_get(beam_channel0_out_id_index + pbeam_handle[id]->database_base_index)->u32;
    *rchn = database_get(beam_channel1_out_id_index + pbeam_handle[id]->database_base_index)->u32;

    return 0;
}

static int32_t beam_out_chs(int32_t argc, char **argv)
{
    if (argc < 4)
    {
        printf("Usage: beam_out_chs [id] [lchn] [rchn]\n");
        printf("\t[lchn]: 0~12\n");
        printf("\t[rchn]: 0~12\n");
        return -1;
    }

    int32_t id = atoi(argv[1]);
    if (id >= BEAM_INSTANCE_NUMBER)
    {
        printf("beam id is out of range. should less than %u\n", BEAM_INSTANCE_NUMBER);
        return -1;
    }

    int32_t result = 0;
    int32_t lchn = atoi(argv[2]);
    int32_t rchn = atoi(argv[3]);

    result = database_write_no_callback(beam_channel0_out_id_index + pbeam_handle[id]->database_base_index, (database_value)lchn);
    result += database_write(beam_channel1_out_id_index + pbeam_handle[id]->database_base_index, (database_value)rchn);

    MSH_RESULT_PRINT(result);

    return 0;
}
MSH_CMD_EXPORT_ALIAS(beam_out_chs, bmoutchs, beam out channels set);

static int32_t shadow(int32_t argc, char **argv)
{
    if (argc < 2)
    {
        printf("Usage: shadow [-c/-s/-g] [area] [x-min] [x-max] [z-min] [z-max]\n");
        printf("\t[-c/-s/-g]: '-c'(clear all priority), '-s'(set one priority), '-g'(get all priority)\n");
        printf("\t[area]: prior area, [0 7]\n");
        printf("\t[x-axis]: x-axis angle, [-90 450]\n");
        printf("\t[z-axis]: z-axis angle, [0 90]\n");
        return -1;
    }

    int32_t enable;
    SSL_DIR_PRIORITY_TYPE priority_type;
    int32_t theta_min;
    int32_t theta_max;
    int32_t phi_min;
    int32_t phi_max;
    if (memcmp("-g", argv[1], 2) == 0)
    {
        printf("Mcore\tx-axis\t\tz-axis\n");
        for (uint32_t j = 0; j < BEAM_INSTANCE_NUMBER; j++)
        {
            printf("beam instance %u\n", j);
            for (uint32_t i = 0u; i < BEAM_PARAM_PRIOR_MAX; i++)
            {
                pthread_mutex_lock(&(pbeam_handle[j]->pmutex));
                beam_get_direction_area(pbeam_handle[j]->handle, i, &enable, &priority_type, &theta_min, &theta_max, &phi_min, &phi_max);
                pthread_mutex_unlock(&(pbeam_handle[j]->pmutex));
                printf("%u\t %d [%d %d]\t[%d %d]\n",
                       i,
                       priority_type,
                       theta_min,
                       theta_max,
                       phi_min,
                       phi_max);
                i++;
            }
        }
    }

    return 0;
}
MSH_CMD_EXPORT(shadow, shadow info get);

static int32_t beam_sig(int32_t argc, char **argv)
{
    (void)argc;
    (void)argv;

    int32_t beam_in_signal[BEAM_IN_CHANNELS];

    for (uint32_t i = 0u; i < BEAM_INSTANCE_NUMBER; i++)
    {
        pthread_mutex_lock(&(pbeam_handle[i]->pmutex));
        beam_get_ch_vol(pbeam_handle[i]->handle, &beam_in_signal[0]);
        pthread_mutex_unlock(&(pbeam_handle[i]->pmutex));
        printf("beam instance %u: input channel signal", i);
        for (uint32_t j = 0; j < BEAM_IN_CHANNELS; j++)
        {
            if ((j % 8) == 0)
                printf("\n");
            printf("\t%d", beam_in_signal[j]);
        }
        printf("\n");
    }

    return 0;
}
MSH_CMD_EXPORT_ALIAS(beam_sig, bmsig, beam sig get);

static int32_t beam_in_gain(int32_t argc, char **argv)
{
    if (argc < 4)
    {
        printf("Usage: beam_in_gain [id] [chn] [gain]\n");
        return -1;
    }

    int32_t id = atoi(argv[1]);
    if (id >= BEAM_INSTANCE_NUMBER)
    {
        printf("beam id is out of range. should less than %u\n", BEAM_INSTANCE_NUMBER);
        return -1;
    }

    int32_t result = 0;
    int32_t chn = atoi(argv[2]);
    int32_t gain = atoi(argv[3]);

    result = database_write(beam_input1_gain_index + pbeam_handle[id]->database_base_index + chn, (database_value)gain);

    MSH_RESULT_PRINT(result);

    return 0;
}
MSH_CMD_EXPORT_ALIAS(beam_in_gain, bmingain, beam in gain set);

static int32_t beam_out_gain(int32_t argc, char **argv)
{
    if (argc < 3)
    {
        printf("Usage: beam_out_gain [id] [gain]\n");
        return -1;
    }

    int32_t id = atoi(argv[1]);
    if (id >= BEAM_INSTANCE_NUMBER)
    {
        printf("beam id is out of range. should less than %u\n", BEAM_INSTANCE_NUMBER);
        return -1;
    }

    int32_t result = 0;
    int32_t gain = atoi(argv[2]);

    result = database_write(beam_output_gain_index + pbeam_handle[id]->database_base_index, (database_value)gain);

    MSH_RESULT_PRINT(result);

    return 0;
}
MSH_CMD_EXPORT_ALIAS(beam_out_gain, bmoutgain, beam out gain set);

static int32_t beam_decay(int32_t argc, char **argv)
{
    if (argc < 2)
    {
        printf("Usage: beam_decay [id]\n");
        return -1;
    }

    int32_t id = atoi(argv[1]);
    if (id >= BEAM_INSTANCE_NUMBER)
    {
        printf("beam id is out of range. should less than %u\n", BEAM_INSTANCE_NUMBER);
        return -1;
    }

    uint8_t result = 0;

    printf("beam instance %u: decay", id);
    for (uint32_t j = 0; j < 8; j++)
    {
        beam_area_get_auto_mixer_decay(pbeam_handle[id]->handle, j, &result);
        printf("\t%hhu", result);
    }
    printf("\n");

    return 0;
}
MSH_CMD_EXPORT_ALIAS(beam_decay, bmdecay, beam decay get);

#endif /* BEAM_ENABLE */
