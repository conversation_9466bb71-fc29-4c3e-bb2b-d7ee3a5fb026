/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-02-27 15:54:09
 * @LastEditTime: 2024-12-06 12:08:58
 * @LastEditors: An<PERSON><PERSON><PERSON>
 * @Description:
 * @FilePath: /audio_mixer_rk3308/src/usb_audio.c
 * Copyright (c) 2006-2024, Yinkman Development Team
 */

#include "resample_mcu.h"

#include "ym_audio_mixer.h"
#include "ym_pid_control.h"
#include "ym_ring_queue.h"
#include "ym_slide_filter.h"

#define UAC_MULTI_FACTOR    1
#define UAC_BUFFER_FACTOR   1
#define BUFFER_SIZE         (FRAMES * 8)
#define CAPTURE_START_DELAY (FRAMES)
#define CAPTURE_AVAIL_MIN   (FRAMES)
#define PERIOD_SEZE         (FRAMES / 2)
#define PLAY_START_DELAY    (FRAMES * 4)
#define PLAY_AVAIL_MIN      (FRAMES)

#define UAC_TX_TICK_ASSERT       0
#define UAC_OUT_RESAMPLE_ONOFF   1
#define UAC_IN_RESAMPLE_ONOFF    1
#if UAC_OUT_RESAMPLE_ONOFF | UAC_IN_RESAMPLE_ONOFF
#define UAC_RESAMPLE_NEW_METHOD  1
#endif /* end UAC_OUT_RESAMPLE_ONOFF | UAC_IN_RESAMPLE_ONOFF */
#define UAC_OUT_RESAMPLE_COUNT   187
#define UAC_IN_RESAMPLE_COUNT    30

#define RESEMPLE_S_SIZE (12000)
#define RESEMPLE_D_SIZE (8000)

static const int uac_low_level_frames = FRAMES * UAC_MULTI_FACTOR;
#if 0
static const int uac_low_level_sizes = FRAMES * UAC_MULTI_FACTOR * USB_AUDIO_CHNS * sizeof(short);
#endif
#if 0
static const int uac_app_level_sizes = FRAMES * UAC_MULTI_FACTOR * USB_AUDIO_CHNS * sizeof(int);
#endif

static snd_pcm_t *play_pcm;
static snd_pcm_t *capture_pcm;

static sem_t usb_out_sem;

static uint32_t audio_out_mask;
static struct yinkman_stream *usb_in_stream;

static sliding_average_filter_p usb_in_interval_filter;
static sliding_average_filter_p usb_out_interval_filter;

static sliding_average_filter_p usb_in_avail_filter;
static sliding_average_filter_p usb_out_avail_filter;

static sliding_average_filter_p usb_in_delay_filter;
static sliding_average_filter_p usb_out_delay_filter;

static sliding_average_filter_p usb_in_diff_filter;
static sliding_average_filter_p usb_out_diff_filter;

extern uint32_t usb_out_xrun_count;
extern uint32_t usb_in_xrun_count;

static ring_queue_p usb_in_ringbuffer;

static float usb_in_resample_factor = 1.0f;
static float usb_out_resample_factor = 1.0f;

static pthread_mutex_t usb_in_resample_mutex;
static pthread_mutex_t usb_out_resample_mutex;
static pthread_mutex_t usb_in_ringbuffer_mutex;

static pid_control_p usb_out_pid_control;

static void *uac_rx_handle;
static void *uac_tx_handle;

extern bool usb_out_mute;
extern int usb_out_volume;
extern float usb_out_mag;
extern bool usb_in_mute;
extern int usb_in_volume;
extern float usb_in_mag;

#if !UAC_RESAMPLE_NEW_METHOD
static void just_usb_resample_factor(void *args);
#endif /* end !UAC_RESAMPLE_NEW_METHOD */

static uint8_t uac_tx_resample_s_buf[RESEMPLE_S_SIZE] __attribute__((aligned(512)));
static uint8_t uac_tx_resample_d_buf[RESEMPLE_D_SIZE] __attribute__((aligned(512)));

static uint8_t uac_rx_resample_s_buf[RESEMPLE_S_SIZE] __attribute__((aligned(512)));
static uint8_t uac_rx_resample_d_buf[RESEMPLE_D_SIZE] __attribute__((aligned(512)));


static int usb_sem_init(void)
{
    sem_init(&usb_out_sem, 0, 0);
    pthread_mutex_init(&usb_in_resample_mutex, NULL);
    pthread_mutex_init(&usb_out_resample_mutex, NULL);
    pthread_mutex_init(&usb_in_ringbuffer_mutex, NULL);
    log_i("usb_sem_init successful.");
    return 0;
}
INIT_SEM_EXPORT(usb_sem_init);

static void usb_stream_in_push_callback(void *args)
{
    uint32_t len;
    uint8_t *data;
    int32_t r;

    static int ringdata[FRAMES * USB_AUDIO_CHNS];
    static int is_start;
    int is_empty = 1;

    len = ring_queue_len(usb_in_ringbuffer);
    if (is_start)
    {
        if (len >= FRAMES * USB_AUDIO_CHNS)
        {
        __back:
            r = ring_queue_pull(usb_in_ringbuffer, ringdata, FRAMES * USB_AUDIO_CHNS);
            if (r == FRAMES * USB_AUDIO_CHNS)
            {
                is_empty = 0;
                data = yinkman_stream_malloc(usb_in_stream);
                if (data)
                {
                    audio32_interleaved_to_noninterleaved(ringdata,
                                                          (int *)data,
                                                          USB_AUDIO_CHNS,
                                                          FRAMES);
                    yinkman_stream_push(usb_in_stream, data);
                }
            }
            else
            {
                log_e("r:%d", r);
            }
        }
        else
        {
            is_start = 0;
        }
    }
    else
    {
        if (len >= FRAMES * USB_AUDIO_CHNS * 4)
        {
            is_start = 1;
            goto __back;
        }
    }

    if (is_empty)
    {
        yinkman_stream_push(usb_in_stream, yinkman_stream_calloc(usb_in_stream));
    }
}

int usb_status(int argc, char **argv)
{
    printf("CAPTURE:\n");
    printf("  interval :%f\n", database_get(pcm_usb_in_interval_index)->f32);
    printf("  delay    :%f\n", database_get(pcm_usb_in_delay_index)->f32);
    printf("  avail    :%f\n", database_get(pcm_usb_in_avail_index)->f32);
    printf("  xrun     :%d\n", usb_in_xrun_count);
    printf("  factor   :%f\n", usb_in_resample_factor);
    printf("  mute     :%d\n", usb_in_mute);
    printf("  volume   :%d\n", usb_in_volume);

    printf("PALY:\n");
    printf("  interval :%f\n", database_get(pcm_usb_out_interval_index)->f32);
    printf("  delay    :%f\n", database_get(pcm_usb_out_delay_index)->f32);
    printf("  avail    :%f\n", database_get(pcm_usb_out_avail_index)->f32);
    printf("  xrun     :%d\n", usb_out_xrun_count);
    printf("  factor   :%f\n", usb_out_resample_factor);
    printf("  mute     :%d\n", usb_out_mute);
    printf("  volume   :%d\n", usb_out_volume);

    return 0;
}
MSH_CMD_EXPORT(usb_status, usb status);

static void usb_stream_out_notify(void *args)
{
    sem_post(&usb_out_sem);
}

static int usb_stream_init(void)
{
    struct yinkman_stream *stream;
    if (!yinkman_stream_create("usb_in", FRAMES, USB_AUDIO_CHNS * (FORMAT) / 8, 1))
    {
        return -1;
    }
    stream = yinkman_stream_create("usb_out", FRAMES, USB_AUDIO_CHNS * (FORMAT) / 8, 1);
    if (stream == NULL)
    {
        return -1;
    }
    audio_out_mask = yinkman_stream_register(stream, usb_stream_out_notify, stream);
    log_i("usb_stream_init successful.");
    return 0;
}
// INIT_STREAM_EXPORT(usb_stream_init);

#if UAC_RESAMPLE_NEW_METHOD
static int uac_resample_factor_update(snd_pcm_sframes_t *prev_avail, snd_pcm_sframes_t *avail, float *factor, int target)
{
    int err = 0;

    if ((prev_avail == NULL) || (avail == NULL) || (factor == NULL))
    {
        err = -1;
    }
    else
    {
        int low_threshold = target / 2;
        int high_threshold = target * 3 / 2;
        float ft = *factor;

        if (*avail < low_threshold)
        {
            ft = 1.0f + 48.0f / 48000;
        }
        else if (*avail > high_threshold)
        {
            ft = 1.0f - 48.0f / 48000;
        }
        else
        {
            if (*factor > 1.0f) // up
            {
                if (*avail > target)
                    ft = 1.0f;
            }
            else if (*factor < 1.0f) // down
            {
                if (*avail < target)
                    ft = 1.0f;
            }
        }

        *prev_avail = *avail;

        if (ft != *factor)
        {
            err = 1;
            *factor = ft;
        }
    }

    return err;
}
#endif /* end UAC_RESAMPLE_NEW_METHOD */

static uint8_t uac_tx_data16[USB_DATA16_LEN * 2]; /* x2 in case of resample number more than 2 channels fixed words */
static uint8_t uac_tx_data32[USB_DATA32_LEN * 2];
static uint8_t uac_tx_resample_out[USB_DATA32_LEN * 4]; /* x2 in case of resample number more than 2 channels fixed words */
static void *uac_tx_thread(void *args)
{
    uint32_t ssize = 0;
    uint32_t dsize = 0;

    void *usb_out_resample_handle = NULL;
    if (mcu_resample_querymem(FRAMES,
                              USB_AUDIO_CHNS,
                              &ssize,
                              &dsize) == 0)
    {
        log_i("ssize:%d, dsize:%d", ssize, dsize);
        if (ssize > RESEMPLE_S_SIZE || dsize > RESEMPLE_D_SIZE)
        {
            log_e("resample buffer size is too small.");
            return NULL;
        }
        memset(uac_tx_resample_s_buf, 0, ssize);
        memset(uac_tx_resample_d_buf, 0, dsize);
        usb_out_resample_handle = mcu_resample_open(1, /* high quality */
                                                    FRAMES,
                                                    USB_AUDIO_CHNS,
                                                    uac_tx_resample_s_buf,
                                                    uac_tx_resample_d_buf);
        if (usb_out_resample_handle)
        {
            mcu_setSrcFactor(usb_out_resample_factor, usb_out_resample_handle);
            log_i("usb out resample open successful.");
        }
    }
    struct yinkman_stream *stream = yinkman_stream_get_by_name("usb_out");
    yinkman_stream_release_all(stream);

    uint8_t *data;

    int err = FRAMES;
    uint64_t tick = ym_tick_get_us();
    database_value tmp;
    snd_pcm_sframes_t olddelay = 0;
    snd_pcm_sframes_t avail, delay;

#if UAC_RESAMPLE_NEW_METHOD
    snd_pcm_sframes_t prev_delay = 0;
#endif /* end UAC_RESAMPLE_NEW_METHOD */

#if UAC_OUT_RESAMPLE_ONOFF
    int is_odd = 0;
#endif /* end UAC_OUT_RESAMPLE_ONOFF */

    snd_pcm_state_t state;
    int count = 0;
    float mag;
    int overflow_flag = 0;

#if UAC_TX_TICK_ASSERT
    uint64_t prev_tick, start_tick, end_tick;
    float tick_diff;
    prev_tick = start_tick = end_tick = ym_tick_get_us();
#endif /* end UAC_TX_TICK_ASSERT */

    memset(uac_tx_data16, 0, USB_DATA16_LEN * 2);
    memset(uac_tx_data32, 0, USB_DATA32_LEN * 2);
    memset(uac_tx_resample_out, 0, USB_DATA32_LEN * 4);
    ym_copySrcFactor(usb_out_resample_handle);
    mcu_resample_process(usb_out_resample_handle,
                         (int *)uac_tx_data32,
                         (int *)uac_tx_resample_out,
                         FRAMES * USB_AUDIO_CHNS * 2,
                         USB_AUDIO_CHNS,
                         USB_AUDIO_CHNS);

    pthread_testcancel();

    for (;;)
    {
        sem_wait(&usb_out_sem);
        if (yinkman_stream_pull(stream, (void **)&data, audio_out_mask))
            continue;

#if UAC_TX_TICK_ASSERT
        prev_tick = start_tick;
        start_tick = ym_tick_get_us();
#endif /* end UAC_TX_TICK_ASSERT */

        memcpy(uac_tx_data32, data, USB_DATA32_LEN);
        yinkman_stream_free(stream, data, audio_out_mask);

        pthread_testcancel();

        state = snd_pcm_state(play_pcm);
        if (state == SND_PCM_STATE_XRUN)
        {
            log_e("play xrun");
            play_xrun_recovery(play_pcm);
        }

        snd_pcm_avail_delay(play_pcm, &avail, &delay);
        tmp.f32 = sliding_average_filter(usb_out_avail_filter, avail);
        database_write(pcm_usb_out_avail_index, tmp);
        tmp.f32 = sliding_average_filter(usb_out_delay_filter, delay);
        database_write(pcm_usb_out_delay_index, tmp);
        tmp.f32 = sliding_average_filter(usb_out_interval_filter, ym_tick_get_us() - tick);
        tick = ym_tick_get_us();
        database_write(pcm_usb_out_interval_index, tmp);
#if !UAC_RESAMPLE_NEW_METHOD
        if (avail < (FRAMES * 2))
        {
            if (!overflow_flag)
                printf("uac-p(%ld) overflow\n", avail);
            overflow_flag = 1;
            continue;
        }
        else
        {
            overflow_flag = 0;
        }
#endif /* end !UAC_RESAMPLE_NEW_METHOD */
        tmp.i32 = abs(delay - olddelay) > 128 ? 0 : (delay - olddelay);
        olddelay = delay;
        tmp.f32 = sliding_average_filter(usb_out_diff_filter, tmp.i32);
        database_write(pcm_usb_out_diff_index, tmp);

#if !UAC_RESAMPLE_NEW_METHOD
        if (++count > UAC_OUT_RESAMPLE_COUNT)
        {
            count = 0;
            just_usb_resample_factor(NULL);
            mcu_setSrcFactor(usb_out_resample_factor, usb_out_resample_handle);
        }
#else
        if (++count > UAC_OUT_RESAMPLE_COUNT)
        {
            count = uac_resample_factor_update(&prev_delay, &delay, &usb_out_resample_factor, FRAMES * 3);
            if (count > 0)
            {
                mcu_setSrcFactor(usb_out_resample_factor, usb_out_resample_handle);
            }
            count = 0;
        }
#endif /* end !UAC_RESAMPLE_NEW_METHOD */

#if UAC_OUT_RESAMPLE_ONOFF
        // return one channel samples
        ym_copySrcFactor(usb_out_resample_handle);
        err = mcu_resample_process(usb_out_resample_handle,
                                   (int *)uac_tx_data32,
                                   (int *)uac_tx_resample_out + is_odd * USB_AUDIO_CHNS,
                                   FRAMES * USB_AUDIO_CHNS * 2,
                                   USB_AUDIO_CHNS,
                                   USB_AUDIO_CHNS) + is_odd;
        if (err < 0)
        {
            printf("uac-p resample failed\n");
            continue;
        }
        is_odd = err % PERIOD_SEZE;
        err -= is_odd;

        avail = snd_pcm_avail(play_pcm);
        if (!avail)
        {
            if (!overflow_flag)
                printf("uac-p overflow\n");
            overflow_flag = 1;
            continue;
        }
        else
        {
            overflow_flag = 0;
        }
        if (err > avail)
        {
            is_odd = (err - avail);
            if (is_odd > (PERIOD_SEZE-1))
            {
                if (is_odd > FRAMES) // drop it in case of overflow
                {
                    printf("uac-p drop %d\n", is_odd);
                    is_odd = 0;
                }
            }
            err = avail;
        }
        bit32_to_16(uac_tx_resample_out, uac_tx_data16, USB_AUDIO_CHNS, err);
        if (is_odd)
            memmove(uac_tx_resample_out, uac_tx_resample_out + err * USB_AUDIO_CHNS * sizeof(int), is_odd * USB_AUDIO_CHNS * sizeof(int));
#else
        err = FRAMES;
        bit32_to_16(uac_tx_data32, uac_tx_data16, USB_AUDIO_CHNS, err);
#endif /* end UAC_OUT_RESAMPLE_ONOFF */

        if (usb_out_mute)
        {
            memset(uac_tx_data16, 0, err * USB_AUDIO_CHNS * 2);
        }
        else
        {
            short *tmp = (short *)uac_tx_data16;
            mag = usb_out_mag;
            for (size_t i = 0; i < err * USB_AUDIO_CHNS; i++)
            {
                tmp[i] = (short)(tmp[i] * mag);
            }
        }

        usb_pcm_write(play_pcm, uac_tx_data16, err);

#if UAC_TX_TICK_ASSERT
        end_tick = ym_tick_get_us();

        tick_diff = (float)(start_tick - prev_tick);
        if (fabs((tick_diff - FRAMES_UNIT_US) > 1000))
        {
            log_e("%s gap:%.2fus", __FUNCTION__, tick_diff);
        }

        tick_diff = (float)(end_tick - start_tick);
        if (tick_diff > FRAMES_UNIT_US)
        {
            log_e("%s over load:%.2fus", __FUNCTION__, tick_diff);
        }
#endif /* end UAC_TX_TICK_ASSERT */
    }
    return NULL;
}

static uint8_t uac_rx_data16[USB_DATA16_LEN];
static uint8_t uac_rx_data32[USB_DATA32_LEN];
static uint8_t uac_rx_resample_out[USB_DATA32_LEN * 2];
static void *uac_rx_thread(void *args)
{
    uint32_t ssize = 0;
    uint32_t dsize = 0;

    void *usb_in_resample_handle = NULL;

    yinkman_stream_release_all(usb_in_stream);

    if (mcu_resample_querymem(uac_low_level_frames,
                              USB_AUDIO_CHNS,
                              &ssize,
                              &dsize) == 0)
    {
        if (ssize > RESEMPLE_S_SIZE || dsize > RESEMPLE_D_SIZE)
        {
            log_e("resample buffer size is too small.");
            return NULL;
        }

        log_i("ssize:%d, dsize:%d", ssize, dsize);
        memset(uac_rx_resample_s_buf, 0, ssize);
        memset(uac_rx_resample_d_buf, 0, dsize);
        usb_in_resample_handle = mcu_resample_open(0,
                                                   uac_low_level_frames,
                                                   USB_AUDIO_CHNS,
                                                   uac_rx_resample_s_buf,
                                                   uac_rx_resample_d_buf);
        if (usb_in_resample_handle)
        {
            mcu_setSrcFactor(usb_in_resample_factor, usb_in_resample_handle);
            log_i("usb in resample open successful.");
        }
    }
    int r;

    database_value tmp;
    uint64_t tick = 0;
    snd_pcm_sframes_t avail, delay;
    float mag;
    int count = 0;
#if UAC_RESAMPLE_NEW_METHOD
    snd_pcm_sframes_t prev_avail = 0;
#endif /* end UAC_RESAMPLE_NEW_METHOD */

    memset(uac_rx_data16, 0, USB_DATA16_LEN);
    memset(uac_rx_data32, 0, USB_DATA32_LEN);
    memset(uac_rx_resample_out, 0, USB_DATA32_LEN * 2);

    int err_words = uac_low_level_frames;
    ym_copySrcFactor(usb_in_resample_handle);
    mcu_resample_process(usb_in_resample_handle,
                         (int *)uac_rx_data32,
                         (int *)uac_rx_resample_out,
                         uac_low_level_frames * USB_AUDIO_CHNS * 2,
                         USB_AUDIO_CHNS,
                         USB_AUDIO_CHNS);

    pthread_testcancel();
    pthread_setcanceltype(PTHREAD_CANCEL_ASYNCHRONOUS, NULL);

    for (;;)
    {
        r = usb_pcm_read(capture_pcm, uac_rx_data16, uac_low_level_frames);
        if (r == uac_low_level_frames)
        {
            tmp.f32 = sliding_average_filter(usb_in_interval_filter, ym_tick_get_us() - tick);
            tick = ym_tick_get_us();
            database_write(pcm_usb_in_interval_index, tmp);

            snd_pcm_avail_delay(capture_pcm, &avail, &delay);

            tmp.f32 = sliding_average_filter(usb_in_avail_filter, avail);
            database_write(pcm_usb_in_avail_index, tmp);
            tmp.f32 = sliding_average_filter(usb_in_delay_filter, delay);
            database_write(pcm_usb_in_delay_index, tmp);

            if (usb_in_mute)
            {
                memset(uac_rx_data16, 0, r * USB_AUDIO_CHNS * 2);
            }
            else
            {
                {
                    short *tmp16 = (short *)uac_rx_data16;
                    mag = usb_in_mag;
                    for (size_t i = 0; i < r * USB_AUDIO_CHNS; i++)
                    {
                        tmp16[i] = (short)(tmp16[i] * mag);
                    }
                }
            }
            swap16Bits((uint32_t *)uac_rx_data16, r * USB_AUDIO_CHNS); /* r * USB_AUDIO_CHNS * 2 / 2 */
            bit16_to_32(uac_rx_data16, uac_rx_data32, USB_AUDIO_CHNS, r);

            // resample process
#if !UAC_RESAMPLE_NEW_METHOD
            // memcpy(resample_out, data32, USB_DATA32_LEN);
            if (++count > UAC_IN_RESAMPLE_COUNT)
            {
                count = 0;
                mcu_setSrcFactor(usb_in_resample_factor, usb_in_resample_handle);
            }
#else
            count = uac_resample_factor_update(&prev_avail, &avail, &usb_in_resample_factor, FRAMES);
            if (count > 0)
                mcu_setSrcFactor(usb_in_resample_factor, usb_in_resample_handle);
#endif /* end UAC_RESAMPLE_NEW_METHOD */

#if UAC_IN_RESAMPLE_ONOFF
            ym_copySrcFactor(usb_in_resample_handle);
            err_words = mcu_resample_process(usb_in_resample_handle,
                                             (int *)uac_rx_data32,
                                             (int *)uac_rx_resample_out,
                                             uac_low_level_frames * USB_AUDIO_CHNS * 2,
                                             USB_AUDIO_CHNS,
                                             USB_AUDIO_CHNS);
            if (err_words < 0)
            {
                printf("uac-c resample failed\n");
                continue;
            }
            ring_queue_push(usb_in_ringbuffer, (int *)uac_rx_resample_out, err_words * USB_AUDIO_CHNS);
#else
            err_words = uac_low_level_frames;
            ring_queue_push(usb_in_ringbuffer, (int *)uac_rx_data32, err_words * USB_AUDIO_CHNS);
#endif /* end UAC_IN_RESAMPLE_ONOFF */
        }
    }
    return NULL;
}

static int _capture_pcm_init(const char *name)
{
    snd_pcm_hw_params_t *hwparams;
    snd_pcm_sw_params_t *swparams;

    snd_pcm_hw_params_alloca(&hwparams);
    snd_pcm_sw_params_alloca(&swparams);
    int err;

    if ((err = snd_pcm_open(&capture_pcm, name, SND_PCM_STREAM_CAPTURE, 0)) < 0)
    {
        printf("Capture open error: %s\n", snd_strerror(err));
        return -1;
    }

    if ((err = set_hwparams(capture_pcm, hwparams, SND_PCM_ACCESS_RW_INTERLEAVED, BUFFER_SIZE, PERIOD_SEZE)) < 0)
    {
        printf("Setting of hwparams failed: %s\n", snd_strerror(err));
        return -1;
    }

    if ((err = set_swparams(capture_pcm, swparams, CAPTURE_START_DELAY, BUFFER_SIZE, CAPTURE_AVAIL_MIN)) < 0)
    {
        printf("Setting of hwparams failed: %s\n", snd_strerror(err));
        return -1;
    }

    err = snd_pcm_prepare(capture_pcm);
    if (err < 0)
        log_e("prepare failed: %s", snd_strerror(err));

    err = snd_pcm_start(capture_pcm);
    if (err < 0)
        log_e("start failed: %s", snd_strerror(err));

    return 0;
}

static int _play_pcm_init(const char *name)
{
    snd_pcm_hw_params_t *hwparams;
    snd_pcm_sw_params_t *swparams;
    snd_pcm_hw_params_alloca(&hwparams);
    snd_pcm_sw_params_alloca(&swparams);
    int err;

    if ((err = snd_pcm_open(&play_pcm, name, SND_PCM_STREAM_PLAYBACK, 0)) < 0)
    {
        printf("Playback open error: %s\n", snd_strerror(err));
        return -1;
    }

    if ((err = set_hwparams(play_pcm, hwparams, SND_PCM_ACCESS_RW_INTERLEAVED, BUFFER_SIZE, PERIOD_SEZE)) < 0)
    {
        printf("Setting of hwparams failed: %s\n", snd_strerror(err));
        return -1;
    }
    if ((err = set_swparams(play_pcm, swparams, PLAY_START_DELAY, BUFFER_SIZE, PLAY_AVAIL_MIN)) < 0)
    {
        printf("Setting of swparams failed: %s\n", snd_strerror(err));
        return -1;
    }

    return 0;
}

#if !UAC_RESAMPLE_NEW_METHOD
static void just_usb_resample_factor(void *args)
{
    usb_out_resample_factor = 1.0f + pid_control_process(usb_out_pid_control, database_get(pcm_usb_out_diff_index)->f32);
    if (usb_out_resample_factor > 1.1f)
    {
        usb_out_resample_factor = 1.1f;
        pid_control_reset(usb_out_pid_control);
    }
    else if (usb_out_resample_factor < 0.9f)
    {
        usb_out_resample_factor = 0.9f;
        pid_control_reset(usb_out_pid_control);
    }
    usb_in_resample_factor = 2.0f - usb_out_resample_factor;
}
#endif /* end !UAC_RESAMPLE_NEW_METHOD */

static int usb_audio_init(void)
{
    usb_in_interval_filter = sliding_average_filter_create(100);
    usb_out_interval_filter = sliding_average_filter_create(100);

    usb_in_avail_filter = sliding_average_filter_create(100);
    usb_out_avail_filter = sliding_average_filter_create(100);

    usb_in_delay_filter = sliding_average_filter_create(100);
    usb_out_delay_filter = sliding_average_filter_create(100);

    usb_in_diff_filter = sliding_average_filter_create((int)(1000.0 / 5.3333f));
    usb_out_diff_filter = sliding_average_filter_create((int)(1000.0 / 5.3333f)); // 根据过往1s的数据调节速率

    usb_in_ringbuffer = ring_queue_create(uac_low_level_frames * UAC_BUFFER_FACTOR * 20);
    if (!usb_in_ringbuffer)
        log_e("malloc err.");
    usb_out_pid_control = pid_control_create(0.000002, 0.000001, 0.000001, 0.0f, 0.0, 1);

    yinkman_stream_register_no_data(yinkman_stream_get_by_name(GLOBAL_AUDIO_CLOCK_NAME), usb_stream_in_push_callback, NULL);
    usb_in_stream = yinkman_stream_get_by_name("usb_in");

    return 0;
}
// INIT_APP_EXPORT(usb_audio_init);

void usb_audio_configured(void)
{
    int card;
    char name[32] = {0};
    // database_value tmp;

    // 获取uac1 声卡id
    card = get_uac_card();
    if (card < 0)
    {
        log_e("can't get uac card.");
        return;
    }
    log_i("UAC card:%d", card);
    sprintf(name, "hw:%d,0", card);
#if 0 // TODO: using alsa api instead of tinyalsa
    // volume mute init
    struct mixer *mixer;
    struct mixer_ctl *ctl;
    mixer = mixer_open(card);
    if (!mixer)
    {
        log_e("Failed to open mixer.");
    }
    else
    {
        ctl = mixer_get_ctl(mixer, 4);
        tmp.i32 = !mixer_ctl_get_value(ctl, 0);
        usb_in_mute = tmp.i32;

        ctl = mixer_get_ctl(mixer, 5);
        tmp.i32 = mixer_ctl_get_value(ctl, 0);
        tmp.i32 = tmp.i32 * 256 - 25600;
        usb_in_volume = tmp.i32;
        tmp.f32 = pow(10, (double)(tmp.i32 / 256) / 20.0);
        tmp.f32 = tmp.f32 > 1.0f ? 1.0 : tmp.f32;
        tmp.f32 = tmp.f32 < 0.0f ? 0.0 : tmp.f32;
        usb_in_mag = tmp.f32;
        log_i("usb in volume:%d mag:%f", usb_in_volume, usb_in_mag);

        ctl = mixer_get_ctl(mixer, 1);
        tmp.i32 = !mixer_ctl_get_value(ctl, 0);
        usb_out_mute = tmp.i32;

        ctl = mixer_get_ctl(mixer, 2);
        tmp.i32 = mixer_ctl_get_value(ctl, 0);
        tmp.i32 = tmp.i32 * 256 - 25600;
        usb_out_volume = tmp.i32;
        tmp.f32 = pow(10, (double)(tmp.i32 / 256) / 20.0);
        tmp.f32 = tmp.f32 > 1.0f ? 1.0 : tmp.f32;
        tmp.f32 = tmp.f32 < 0.0f ? 0.0 : tmp.f32;
        usb_out_mag = tmp.f32;
        log_i("usb out volume:%d mag:%f", usb_out_volume, usb_out_mag);
        mixer_close(mixer);
    }
#endif
    if (_capture_pcm_init(name) == 0)
    {
        uac_rx_handle = ym_thread_create("uac_rx",
                                         audio_in_thread_cpuid,
                                         usb_rx_thread_prio,
                                         uac_rx_thread,
                                         NULL);
        if (uac_rx_handle)
            ym_thread_run(uac_rx_handle);
        else
            log_e("%s ym_thread_create err", __func__);
    }
    if (_play_pcm_init(name) == 0)
    {
        uac_tx_handle = ym_thread_create("uac_tx",
                                         audio_out_thread_cpuid,
                                         usb_tx_thread_prio,
                                         uac_tx_thread,
                                         NULL);
        if (uac_tx_handle)
            ym_thread_run(uac_tx_handle);
        else
            log_e("%s ym_thread_create err", __func__);
    }
}

void usb_audio_disconnected(void)
{
    if (uac_rx_handle)
    {
        ym_thread_destroy(uac_rx_handle);
        uac_rx_handle = 0;
    }

    if (uac_tx_handle)
    {
        ym_thread_destroy(uac_tx_handle);
        uac_tx_handle = 0;
    }

    if (capture_pcm)
    {
        snd_pcm_close(capture_pcm);
        capture_pcm = NULL;
    }

    if (play_pcm)
    {
        snd_pcm_close(play_pcm);
        play_pcm = NULL;
    }
}
