#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <pthread.h>
#include <alsa/asoundlib.h>
#include <semaphore.h>
#include <stdatomic.h>

#include "config.h"
#include "ym_shell.h"
#include "ym_log.h"
#include "ym_stream.h"
#include "ym_audio_tools.h"
#include "ym_slide_filter.h"
#include "ym_timer.h"
#include "ym_startup.h"
#include "ym_audio_mixer.h"

typedef struct audio_raw_buffer
{
    void *interleaved_buffer;
    void *noninterleaved_buffer;
    uint32_t channels;
    uint32_t frames;
    uint32_t format_bytes;
    void **chns; // point to noninterleaved_buffer
} audio_raw_buffer_t, *audio_raw_buffer_p;

typedef struct output_stream_notify
{
    uint32_t offset;
    pthread_mutex_t *mutex;
    atomic_uint *output_mask;
    sem_t *output_notify_sem;
} output_stream_notify_t, *output_stream_notify_p;

typedef struct audio_io_dev
{
    snd_pcm_t *pcm;
    yinkman_stream_p *stream;
    void *thread_handle;
    audio_raw_buffer_p buffer;
    uint32_t xrun_count;
    sliding_average_filter_p interval_tick_filter;
    float interval_tick;
    uint64_t interval_tick_pos;

    /**
     * only for output_dev
     */
    atomic_uint output_mask;
    sem_t output_notify_sem;
    pthread_mutex_t mutex;
    output_stream_notify_p *output_notify;
    sliding_average_filter_p delay_tick_filter;
    float delay_tick;

    // struct pcm_status_def status;
} audio_io_dev_t, *audio_io_dev_p;

typedef struct audio_io
{
    const char *name;
    const int32_t card;
    const uint8_t ichns; // actually used  input channels
    const uint8_t ochns; // actually used output channels
    const uint8_t imax;  // maximum input channels
    const uint8_t omax;  // maximum output channels

    audio_io_dev_t input_dev;
    audio_io_dev_t output_dev;

    pthread_mutex_t mutex;       // 该结构体的互斥量
    void *monitor_thread_handle; // 监控底层IO线程句柄，底层IO错误后通知到该线程
    sem_t *event_sem;            // 事件信号量，用于通知监控线程
    volatile uint32_t event;     // 事件，用于通知监控线程

    // uint32_t test_flag;

} audio_io_t, *audio_io_p;

#define AUDIO_IO_INIT(_name, _card, _ichns, _ochns, _imax, _omax) \
    {                                               \
        .name = (_name),                            \
        .card = (_card),                            \
        .ichns = (_ichns),                          \
        .ochns = (_ochns),                          \
        .imax  = (_imax),                           \
        .omax  = (_omax),                           \
        .input_dev = {                              \
            .pcm = NULL,                            \
            .stream = NULL,                         \
            .thread_handle = NULL,                  \
            .buffer = NULL,                         \
            .xrun_count = 0,                        \
            .interval_tick_filter = NULL,           \
            .interval_tick = 0,                     \
            .interval_tick_pos = 0,                 \
            .output_mask = 0,                       \
            .output_notify_sem = {{0, 0}},          \
            .mutex = PTHREAD_MUTEX_INITIALIZER,     \
            .output_notify = NULL,                  \
            .delay_tick_filter = NULL,              \
            .delay_tick = 0,                        \
        },                                          \
        .output_dev = {                             \
            .pcm = NULL,                            \
            .stream = NULL,                         \
            .thread_handle = NULL,                  \
            .buffer = NULL,                         \
            .xrun_count = 0,                        \
            .interval_tick_filter = NULL,           \
            .interval_tick = 0,                     \
            .interval_tick_pos = 0,                 \
            .output_mask = 0,                       \
            .output_notify_sem = {{0, 0}},          \
            .mutex = PTHREAD_MUTEX_INITIALIZER,     \
            .output_notify = NULL,                  \
            .delay_tick_filter = NULL,              \
            .delay_tick = 0,                        \
        },                                          \
        .mutex = PTHREAD_MUTEX_INITIALIZER,         \
        .monitor_thread_handle = NULL,              \
        .event_sem = NULL,                          \
        .event = 0,                                 \
    }

audio_raw_buffer_p audio_raw_buffer_create(uint32_t channels, uint32_t frames, uint32_t format_bytes);
void audio_raw_buffer_free(audio_raw_buffer_p buffer);
void audio_raw_buffer_to_interleaved(audio_raw_buffer_p buffer);
void audio_raw_buffer_to_noninterleaved(audio_raw_buffer_p buffer);
void *audio_raw_buffer_get_channel(audio_raw_buffer_p buffer, uint32_t index);

audio_io_t audio_io[] = {
#if defined(BD_MIXER_MATRIX_MIC)
    AUDIO_IO_INIT("es7210", 0, 32, 0, 32, 0),
    AUDIO_IO_INIT("es7149", 1, 0, 4, 0, 4),
    AUDIO_IO_INIT("nau88l21_0", 2, 2, 2, 2, 2),
    AUDIO_IO_INIT("nau88l21_1", 3, 2, 2, 2, 2)
#else
    AUDIO_IO_INIT("acodec", CACODEC_CARD, 8, 0, 8 ,0),
#endif
};

typedef enum
{
    audio_out_xrun = 1 << 0,
    audio_in_xrun = 1 << 1,
} audio_io_event_t;

#define AUDIO_IO_STREAM_BLOCK_COUNT (1)
#define READ_FRAMES (FRAMES)
#define READ_COUNT (2)
#define AUDIO_IO_TICK_FILTER_SIZE (100)

#define WRITE_FRAMES (256)

static void *audio_io_input_thread_handle(void *arg);
static void audio_io_output_thread_handle(void);

static uint8_t zero_data[MIXER_INPUT_CHN_MAX * FRAMES * FORMAT / 8];

static pthread_mutex_t outxrun_mutex = PTHREAD_MUTEX_INITIALIZER;

static void io_xrun_post(void)
{
    pthread_mutex_lock(&outxrun_mutex);
    audio_io[0].event |= audio_out_xrun;
    pthread_mutex_unlock(&outxrun_mutex);
    log_e("io xrun post");
}

// static int pcm_mmap_readi(snd_pcm_t *pcm_handle, int *noninterleaved_data, int chns)
// {
//     int err;
//     const snd_pcm_channel_area_t *areas;
//     snd_pcm_uframes_t offset, frames = FRAMES;
//     snd_pcm_sframes_t avail_frames;

//     // 检查捕获缓冲区中可用的帧数
//     avail_frames = snd_pcm_avail_update(pcm_handle);
//     if (avail_frames < 0)
//     {
//         if (avail_frames == -EPIPE)
//         {
//             // 重置设备
//             // io_xrun_post();
//         }
//         return -1;
//     }
//     else if (avail_frames < FRAMES)
//     {
//         return -1;
//     }

//     err = snd_pcm_mmap_begin(pcm_handle, &areas, &offset, &frames);
//     if (err < 0)
//     {
//         log_e("snd_pcm_mmap_begin err: %s", snd_strerror(err));
//         return -1;
//     }

//     if (frames < FRAMES)
//     {
//         log_i("snd_pcm_mmap_begin get frames too small");
//         return -1;
//     }

//     int *base_addr = (int *)((uint8_t *)areas[0].addr + (areas[0].first / 8));
//     base_addr += offset * chns;

//     audio32_interleaved_to_noninterleaved(base_addr, noninterleaved_data, chns, FRAMES);

//     // 提交数据
//     err = snd_pcm_mmap_commit(pcm_handle, offset, frames);
//     if (err < 0 || (snd_pcm_uframes_t)err != frames)
//     {
//         log_e("snd_pcm_mmap_commit err: %s", snd_strerror(err));
//         return -2;
//     }
//     return 0;
// }

static int pcm_audio_readi(snd_pcm_t *pcm_handle, void *interleaved_data)
{
    int err;
    uint32_t cptr = FRAMES;
    uint8_t *ptr = (uint8_t *)interleaved_data;

    while (cptr > 0)
    {
        if ((err = snd_pcm_readi(pcm_handle, ptr, cptr)) < 0)
        {
            if (err == -EAGAIN)
            {
                log_e("Read error: %s", snd_strerror(err));
                continue;
            }
            else if (err == -ENODEV)
            {
                log_e("Device is removed: %s", snd_strerror(err));
                exit(0);
            }
            else
            {
                log_e("Read error: %s", snd_strerror(err));
                io_xrun_post();
                break;
            }
            continue;
        }

        ptr += snd_pcm_frames_to_bytes(pcm_handle, err);
        cptr -= err;
    }
    return 0;
}

#ifndef LOG_DELAY_TIME_FOR_PCM
#define LOG_DELAY_TIME_FOR_PCM 480000 //avoid high-freq same log. once maybe 125us, 1min is 480,000
#endif
static int pcm_audio_writei(snd_pcm_t *pcm_handle, void *interleaved_data)
{
    int err;
    uint32_t cptr = FRAMES;
    static uint32_t log_time = LOG_DELAY_TIME_FOR_PCM;
    uint8_t *ptr = (uint8_t *)interleaved_data;

    while (cptr > 0)
    {
        if ((err = snd_pcm_writei(pcm_handle, ptr, cptr)) < 0)
        {
            if (err == -EAGAIN)
            {
                if(log_time++ >= LOG_DELAY_TIME_FOR_PCM)
                {
                    log_e("Write pcm error: %s", snd_strerror(err));
                    log_time = 0;
                }
                continue;
            }
            else if (err == -ENODEV)
            {
                log_e("Device is removed: %s", snd_strerror(err));
                exit(0);
            }
            else
            {
                log_e("Write error: %s", snd_strerror(err));
                io_xrun_post();
                break;
            }
            continue;
        }

        ptr += snd_pcm_frames_to_bytes(pcm_handle, err);
        cptr -= err;
    }
    return 0;
}

static int pcm_set_hwparams(snd_pcm_t *handle,
                            snd_pcm_hw_params_t *params,
                            unsigned int chn,
                            snd_pcm_access_t access,
                            snd_pcm_uframes_t buffer_size,
                            snd_pcm_uframes_t period_size)
{
    unsigned int rrate;
    int err;

    /* choose all parameters */
    err = snd_pcm_hw_params_any(handle, params);
    if (err < 0)
    {
        log_e("Broken configuration: no configurations available: %s", snd_strerror(err));
        return err;
    }
    /* set the interleaved read/write format */
    err = snd_pcm_hw_params_set_access(handle, params, access);
    if (err < 0)
    {
        log_e("Access type not available for: %s", snd_strerror(err));
        return err;
    }

    /* set the sample format */
    err = snd_pcm_hw_params_set_format(handle, params, SND_PCM_FORMAT_S32_LE);
    if (err < 0)
    {
        log_e("Sample format not available: %s", snd_strerror(err));
        return err;
    }

    /* set the count of channels */
    err = snd_pcm_hw_params_set_channels(handle, params, chn);
    if (err < 0)
    {
        log_e("Channels count (2) not available: %s", snd_strerror(err));
        return err;
    }

    /* set the stream rate */
    rrate = RATE;
    err = snd_pcm_hw_params_set_rate_near(handle, params, &rrate, 0);
    if (err < 0)
    {
        log_e("Rate 48000Hz not available: %s", snd_strerror(err));
        return err;
    }
    if (rrate != RATE)
    {
        log_e("Rate doesn't match (requested 48000Hz, get %iHz)", err);
        return -EINVAL;
    }

    /* set the buffer size */
    err = snd_pcm_hw_params_set_buffer_size(handle, params, buffer_size);
    if (err < 0)
    {
        log_e("Unable to set buffer: %s", snd_strerror(err));
        return err;
    }

    /* set the period size */
    err = snd_pcm_hw_params_set_period_size(handle, params, period_size, 0);
    if (err < 0)
    {
        log_e("Unable to set period size: %s", snd_strerror(err));
        return err;
    }

    /* write the parameters to device */
    err = snd_pcm_hw_params(handle, params);
    if (err < 0)
    {
        log_e("Unable to set hw params for playback: %s", snd_strerror(err));
        return err;
    }
    return 0;
}

static int pcm_set_swparams(snd_pcm_t *handle,
                            snd_pcm_sw_params_t *swparams,
                            int start,
                            int stop,
                            int min)
{
    int err;

    /* get the current swparams */
    err = snd_pcm_sw_params_current(handle, swparams);
    if (err < 0)
    {
        log_e("Unable to determine current swparams: %s", snd_strerror(err));
        return err;
    }

    /* start the transfer when the buffer is almost full: */
    err = snd_pcm_sw_params_set_start_threshold(handle, swparams, start);
    if (err < 0)
    {
        log_e("Unable to set start threshold mode: %s", snd_strerror(err));
        return err;
    }

    err = snd_pcm_sw_params_set_stop_threshold(handle, swparams, stop);
    if (err < 0)
    {
        log_e("Unable to set stop threshold mode: %s", snd_strerror(err));
        return err;
    }

    /* allow the transfer when at least period_size samples can be processed */
    /* or disable this mechanism when period event is enabled (aka interrupt like style processing) */
    err = snd_pcm_sw_params_set_avail_min(handle, swparams, min);
    if (err < 0)
    {
        log_e("Unable to set avail min: %s", snd_strerror(err));
        return err;
    }

    /* write the parameters to the playback device */
    err = snd_pcm_sw_params(handle, swparams);
    if (err < 0)
    {
        log_e("Unable to set sw params: %s", snd_strerror(err));
        return err;
    }
    return 0;
}

static void audio_io_stop(void)
{
    audio_io_p paudio;

    for (int x = 0; x < ARRAY_SIZE(audio_io); x++)
    {
        paudio = &audio_io[x];

        if (paudio->ochns > 0)
        {
            pthread_mutex_lock(&paudio->output_dev.mutex);
            snd_pcm_close(paudio->output_dev.pcm);
            pthread_mutex_unlock(&paudio->output_dev.mutex);
        }

        if (paudio->ichns > 0)
        {
            pthread_mutex_lock(&paudio->input_dev.mutex);
            snd_pcm_close(paudio->input_dev.pcm);
            pthread_mutex_unlock(&paudio->input_dev.mutex);
        }
    }
}

static void audio_io_start(void)
{
    int err;
    char name[32];
    snd_pcm_hw_params_t *hwparams;
    snd_pcm_sw_params_t *swparams;
    snd_pcm_hw_params_alloca(&hwparams);
    snd_pcm_sw_params_alloca(&swparams);
    audio_io_p paudio;

    for (int x = 0; x < ARRAY_SIZE(audio_io); x++)
    {
        paudio = &audio_io[x];
        memset(name, 0, sizeof(name));
        snprintf(name, sizeof(name) - 1, "hw:%d,0", x);
        if (paudio->ochns > 0)
        {
            pthread_mutex_lock(&paudio->output_dev.mutex);
            sliding_average_filter_reset(paudio->output_dev.interval_tick_filter);
            sliding_average_filter_reset(paudio->output_dev.delay_tick_filter);

            if ((err = snd_pcm_open(&paudio->output_dev.pcm, name, SND_PCM_STREAM_PLAYBACK, SND_PCM_NONBLOCK)) < 0)
            {
                log_e("Capture open error: %s\n", snd_strerror(err));
                exit(0);
            }

            if (pcm_set_hwparams(paudio->output_dev.pcm,
                                 hwparams,
                                 (paudio->omax > paudio->ochns) ? paudio->omax : paudio->ochns,
                                 SND_PCM_ACCESS_RW_INTERLEAVED,
                                 WRITE_FRAMES * 12,
                                 WRITE_FRAMES))
            {
                log_e("pcm set hwparams err:%s", paudio->name);
                exit(0);
            }

            if (pcm_set_swparams(paudio->output_dev.pcm, swparams, WRITE_FRAMES, ~0, WRITE_FRAMES))
            {
                log_e("pcm set hwparams err:%s", paudio->name);
                exit(0);
            }

            if (snd_pcm_prepare(paudio->output_dev.pcm) != 0)
            {
                log_e("snd_pcm_prepare err:%s", paudio->name);
                exit(0);
            }

            snd_pcm_writei(paudio->output_dev.pcm, zero_data, WRITE_FRAMES - 1);

            log_i("%s open PCM OUT device successful", paudio->name);

            pthread_mutex_unlock(&paudio->output_dev.mutex);
        }

        if (paudio->ichns > 0)
        {
            pthread_mutex_lock(&paudio->input_dev.mutex);
            sliding_average_filter_reset(paudio->input_dev.interval_tick_filter);
            // if ((err = snd_pcm_open(&paudio->input_dev.pcm, name, SND_PCM_STREAM_CAPTURE, x == 1 ? 0 : SND_PCM_NONBLOCK)) < 0)
            if ((err = snd_pcm_open(&paudio->input_dev.pcm, name, SND_PCM_STREAM_CAPTURE, 0)) < 0)
            {
                log_e("Capture open error: %s\n", snd_strerror(err));
                exit(0);
            }

            if (pcm_set_hwparams(paudio->input_dev.pcm,
                                 hwparams,
                                 (paudio->imax > paudio->ichns) ? paudio->imax : paudio->ichns,
                                //  x == 1 ? SND_PCM_ACCESS_RW_INTERLEAVED : SND_PCM_ACCESS_MMAP_INTERLEAVED,
                                 SND_PCM_ACCESS_RW_INTERLEAVED,
                                 FRAMES * 10,
                                 FRAMES))
            {
                log_e("pcm set hwparams err:%s", paudio->name);
                exit(0);
            }

            if (pcm_set_swparams(paudio->input_dev.pcm, swparams, 0, ~0, FRAMES))
            {
                log_e("pcm set hwparams err:%s", paudio->name);
                exit(0);
            }

            if (snd_pcm_prepare(paudio->input_dev.pcm) != 0)
            {
                log_e("snd_pcm_prepare err:%s", paudio->name);
                exit(0);
            }

            log_i("%s open PCM IN device successful", paudio->name);
            pthread_mutex_unlock(&paudio->input_dev.mutex);
        }

        log_i("Card %d have %d ichns, %d imax, %d ochns, %d omax", x,
            paudio->ichns, paudio->imax, paudio->ochns, paudio->omax);
    }

    for (int x = 0; x < ARRAY_SIZE(audio_io); x++)
    {
        paudio = &audio_io[x];
        if (paudio->ichns > 0)
        {
            if (snd_pcm_start(paudio->input_dev.pcm))
            {
                log_e("pcm start err.");
                exit(0);
            }
        }
    }
}

static void *audio_io_input_thread_handle(void *arg)
{
    audio_io_p paudio;
    audio_io_dev_p pdev;
    void *chn_data;

    struct sched_param param;
    int policy = SCHED_FIFO;
    int max_priority = sched_get_priority_max(policy);

    param.sched_priority = max_priority;

    // 设置线程的调度策略和优先级
    if (pthread_setschedparam(pthread_self(), policy, &param) != 0)
    {
        perror("pthread_setschedparam failed");
        exit(EXIT_FAILURE);
    }

    audio_io_start();
    log_i("audio_io_input_thread_handle start");

    for (;;)
    {
        for (int x = 0; x < ARRAY_SIZE(audio_io); x++)
        {
            paudio = &audio_io[x];
            if (paudio->ichns <= 0)
                continue;
            pdev = &paudio->input_dev;
            pdev->interval_tick = sliding_average_filter(pdev->interval_tick_filter, ym_tick_get_ns() - pdev->interval_tick_pos);
            pdev->interval_tick_pos = ym_tick_get_ns();
            pdev->xrun_count++;
            // if (x == 1)
            {
                pcm_audio_readi(pdev->pcm, pdev->buffer->interleaved_buffer);
                audio_raw_buffer_to_noninterleaved(pdev->buffer);
            }
            // else
            // {
            //     pcm_mmap_readi(pdev->pcm, pdev->buffer->noninterleaved_buffer, paudio->ichns);
            // }

            for (int i = 0; i < paudio->ichns; i++)
            {
                chn_data = yinkman_stream_malloc(pdev->stream[i]);
                if (chn_data)
                {
                    memcpy(chn_data, audio_raw_buffer_get_channel(pdev->buffer, i), READ_FRAMES * (FORMAT) / 8);
                    if (yinkman_stream_push(pdev->stream[i], chn_data))
                    {
                        log_e("stream push err.");
                    }
                }
            }
        }
        // log_i("input pcm data to stream done.");

        audio_io_output_thread_handle();

        pthread_mutex_lock(&outxrun_mutex);
        if (audio_io[0].event & audio_out_xrun)
        {
            audio_io[0].event &= ~audio_out_xrun;
            audio_io_stop();
            audio_io_start();
        }
        pthread_mutex_unlock(&outxrun_mutex);
    }
    return NULL;
}

static void audio_io_output_thread_handle(void)
{
    audio_io_p paudio;
    audio_io_dev_p pdev;
    void *chn_data;
    uint32_t mask;
    for (int x = 0; x < ARRAY_SIZE(audio_io); x++)
    {
        paudio = &audio_io[x];
        if (paudio->ochns <= 0)
            continue;
        pdev = &paudio->output_dev;
        mask = (1U << paudio->ochns) - 1;

        // pthread_mutex_lock(&pdev->mutex);
        if (atomic_load(&pdev->output_mask) != mask)
        {
            // pthread_mutex_unlock(&pdev->mutex);
            pcm_audio_writei(pdev->pcm, zero_data);
            continue;
        }
        else
        {
            atomic_store(&pdev->output_mask, 0);
        }
        // pthread_mutex_unlock(&pdev->mutex);

        pdev->interval_tick = sliding_average_filter(pdev->interval_tick_filter, ym_tick_get_ns() - pdev->interval_tick_pos);
        pdev->interval_tick_pos = ym_tick_get_ns();

        for (int i = 0; i < paudio->ochns; i++)
        {
            if (yinkman_stream_pull(pdev->stream[i], (void **)&chn_data, 0x01))
            {
                memset(audio_raw_buffer_get_channel(pdev->buffer, i), 0, FRAMES * (FORMAT) / 8);
            }
            else
            {
                memcpy(audio_raw_buffer_get_channel(pdev->buffer, i), chn_data, FRAMES * (FORMAT) / 8);
                yinkman_stream_free(pdev->stream[i], chn_data, 0x01);
            }
        }

        audio_raw_buffer_to_interleaved(pdev->buffer);
        pcm_audio_writei(pdev->pcm, pdev->buffer->interleaved_buffer);
    }

    return;
}

static int audio_io_sem_init(void)
{
    audio_io_p paudio;
    for (int x = 0; x < ARRAY_SIZE(audio_io); x++)
    {
        paudio = &audio_io[x];
        paudio->event_sem = (sem_t *)malloc(sizeof(sem_t));
        sem_init(paudio->event_sem, 0, 0);

        if (paudio->imax > 0)
        {
            paudio->input_dev.buffer = audio_raw_buffer_create(paudio->imax, READ_FRAMES, 1 * (FORMAT) / 8);
            paudio->input_dev.interval_tick_filter = sliding_average_filter_create(AUDIO_IO_TICK_FILTER_SIZE);
        }

        if (paudio->omax > 0)
        {
            paudio->output_dev.buffer = audio_raw_buffer_create(paudio->omax, FRAMES, 1 * (FORMAT) / 8);
            paudio->output_dev.interval_tick_filter = sliding_average_filter_create(AUDIO_IO_TICK_FILTER_SIZE);
            paudio->output_dev.delay_tick_filter = sliding_average_filter_create(AUDIO_IO_TICK_FILTER_SIZE);
        }
    }

    log_i("audio io sem init successful.");
    return 0;
}
INIT_SEM_EXPORT(audio_io_sem_init);

static void audio_io_out_stream_notify(void *args)
{
    output_stream_notify_p notify = (output_stream_notify_p)args;
    pthread_mutex_lock(notify->mutex);
    atomic_fetch_or(notify->output_mask, 1 << notify->offset);
    pthread_mutex_unlock(notify->mutex);
    sem_post(notify->output_notify_sem);
}

static void audio_io_out_stream_auto_notify(void *args)
{
    yinkman_stream_p stream = (yinkman_stream_p)args;
    void *data = yinkman_stream_malloc(stream);
    if (data)
    {
        memset(data, 0, (FORMAT / 8) * FRAMES);
        yinkman_stream_push(stream, data);
    }
}

static int audio_io_stream_init(void)
{
    char name[32];
    audio_io_p paudio;
    for (int x = 0; x < ARRAY_SIZE(audio_io); x++)
    {
        paudio = &audio_io[x];
        if (paudio->ichns > 0)
        {
            paudio->input_dev.stream = (yinkman_stream_p *)malloc(sizeof(yinkman_stream_p) * paudio->ichns);
            for (int j = 0; j < paudio->ichns; j++)
            {
                memset(name, 0, sizeof(name));
                snprintf(name, sizeof(name) - 1, "%s_src%d", paudio->name, j);
                paudio->input_dev.stream[j] = yinkman_stream_create(name, READ_FRAMES, 1 * (FORMAT) / 8, AUDIO_IO_STREAM_BLOCK_COUNT);
            }
        }

        if (paudio->ochns > 0)
        {
            paudio->output_dev.stream = (yinkman_stream_p *)malloc(sizeof(yinkman_stream_p) * paudio->ochns);
            paudio->output_dev.output_notify = (output_stream_notify_p *)malloc(sizeof(output_stream_notify_p) * paudio->ochns);
            for (int j = 0; j < paudio->ochns; j++)
            {
                memset(name, 0, sizeof(name));
                snprintf(name, sizeof(name) - 1, "%s_sink%d", paudio->name, j);
                paudio->output_dev.stream[j] = yinkman_stream_create(name, FRAMES, 1 * (FORMAT) / 8, AUDIO_IO_STREAM_BLOCK_COUNT);
                paudio->output_dev.output_notify[j] = (output_stream_notify_p)malloc(sizeof(output_stream_notify_t));
                paudio->output_dev.output_notify[j]->mutex = &paudio->output_dev.mutex;
                paudio->output_dev.output_notify[j]->output_mask = &paudio->output_dev.output_mask;
                paudio->output_dev.output_notify[j]->output_notify_sem = &paudio->output_dev.output_notify_sem;
                paudio->output_dev.output_notify[j]->offset = j;
                yinkman_stream_register(paudio->output_dev.stream[j], audio_io_out_stream_notify, paudio->output_dev.output_notify[j]);
            }
        }
    }

    log_i("audio io stream init successful.");
    return 0;
}
INIT_STREAM_EXPORT(audio_io_stream_init);

int audio_io_app_init(void)
{
    audio_io_p paudio;
    char name[32] = {0};
    for (int x = 0; x < ARRAY_SIZE(audio_io); x++)
    {
        paudio = &audio_io[x];
        for (int j = 0; j < paudio->ochns; j++)
        {
            memset(name, 0, sizeof(name));
            snprintf(name, sizeof(name) - 1, "%s_sink%d", paudio->name, j);
            // 判断该sink是否由mixer输出，如果不是则注册到全局时钟，自动输出空数据
            if (!is_used_output_stream(name))
            {
                yinkman_stream_register_no_data(yinkman_stream_get_by_name(GLOBAL_AUDIO_CLOCK_NAME), audio_io_out_stream_auto_notify, paudio->output_dev.stream[j]);
            }
        }
    }

    ym_thread_run(ym_thread_create("audio_input",
                                   audio_in_thread_cpuid,
                                   acodec_in_thread_prio,
                                   audio_io_input_thread_handle,
                                   NULL));

    log_i("audio io app init successful.");
    return 0;
}

audio_raw_buffer_p audio_raw_buffer_create(uint32_t channels, uint32_t frames, uint32_t format_bytes)
{
    audio_raw_buffer_p buffer = (audio_raw_buffer_p)malloc(sizeof(audio_raw_buffer_t));
    if (!buffer)
    {
        log_e("malloc err!");
        return NULL;
    }
    buffer->interleaved_buffer = malloc(channels * frames * format_bytes);
    buffer->noninterleaved_buffer = malloc(channels * frames * format_bytes);
    buffer->chns = (void **)malloc(sizeof(void *) * channels);
    for (uint32_t i = 0; i < channels; i++)
    {
        buffer->chns[i] = buffer->noninterleaved_buffer + i * frames * format_bytes;
    }
    buffer->channels = channels;
    buffer->frames = frames;
    buffer->format_bytes = format_bytes;
    return buffer;
}

void audio_raw_buffer_free(audio_raw_buffer_p buffer)
{
    if (buffer)
    {
        if (buffer->interleaved_buffer)
        {
            free(buffer->interleaved_buffer);
        }
        if (buffer->noninterleaved_buffer)
        {
            free(buffer->noninterleaved_buffer);
        }
        if (buffer->chns)
        {
            free(buffer->chns);
        }
        free(buffer);
    }
}

void audio_raw_buffer_to_interleaved(audio_raw_buffer_p buffer)
{
    if (buffer)
    {
        if (buffer->format_bytes == 2)
        {
            audio16_noninterleaved_to_interleaved(buffer->noninterleaved_buffer, buffer->interleaved_buffer, buffer->channels, buffer->frames);
        }
        else if (buffer->format_bytes == 4)
        {
            audio32_noninterleaved_to_interleaved(buffer->noninterleaved_buffer, buffer->interleaved_buffer, buffer->channels, buffer->frames);
        }
    }
}

void audio_raw_buffer_to_noninterleaved(audio_raw_buffer_p buffer)
{
    if (buffer)
    {
        if (buffer->format_bytes == 2)
        {
            audio16_interleaved_to_noninterleaved(buffer->interleaved_buffer, buffer->noninterleaved_buffer, buffer->channels, buffer->frames);
        }
        else if (buffer->format_bytes == 4)
        {
            audio32_interleaved_to_noninterleaved(buffer->interleaved_buffer, buffer->noninterleaved_buffer, buffer->channels, buffer->frames);
        }
    }
}

void *audio_raw_buffer_get_channel(audio_raw_buffer_p buffer, uint32_t index)
{
    if (buffer && index < buffer->channels)
    {
        return buffer->chns[index];
    }
    return NULL;
}

static int pcm_in_delay(int argc, char **argv)
{
    audio_io_p paudio;
    printf("device\t\t\t    inter\t      count\n");
    printf("------------------------------------------------\n");
    for (int x = 0; x < ARRAY_SIZE(audio_io); x++)
    {
        paudio = &audio_io[x];

        if (paudio->ichns > 0)
        {
            printf("%-20s\t\t%f\t%8d\n", paudio->name, paudio->input_dev.interval_tick, paudio->input_dev.xrun_count);
        }
    }
    return 0;
}
MSH_CMD_EXPORT(pcm_in_delay, pcm delay);

static int pcm_out_delay(int argc, char **argv)
{
    audio_io_p paudio;
    printf("device\t\t\t    inter\t      count\t     delay\t     state\t     avail\n");
    printf("-----------------------------------------------------------------------------------------\n");
    for (int x = 0; x < ARRAY_SIZE(audio_io); x++)
    {
        paudio = &audio_io[x];

        if (paudio->ochns > 0)
        {
            printf("%-15s\t\t%f\t%8d\t%f\n",
                   paudio->name,
                   paudio->output_dev.interval_tick,
                   paudio->output_dev.xrun_count,
                   paudio->output_dev.delay_tick);
        }
    }
    return 0;
}
MSH_CMD_EXPORT(pcm_out_delay, pcm delay);
