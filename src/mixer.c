#include <stdio.h>
#include <string.h>
#include <time.h>
#include <unistd.h>
#include <stdlib.h>
#include <signal.h>
#include <stdatomic.h>
#include <stdint.h>

#include "ym_audio_mixer.h"
#include "ym_slide_filter.h"
#include "ym_watchdog.h"

#define MIXER_SIG_FILTER_COUNT (20)
#define WATCHDOG_TIMEOUT (10000) // ms

static void *mixer_thread_handle(void *arg);
static void *audio_out_mixer_handle(void *arg);

void *ym_mixer_master_handle;
void *ym_mixer_slave_handle;

MIXER_DEF_INIT(master_mixer, MIXER_INPUT_CHN_MAX, MIXER_OUTPUT_CHN_MAX);
MIXER_DEF_INIT(slave_mixer, MIXER_INPUT_CHN_MAX, MIXER_OUTPUT_CHN_MAX);

static uint32_t is_pause;
static atomic_uint stream_counter_master = 0u;
static atomic_uint stream_counter_slave = 0u;

void resume_mixer(void)
{
    is_pause = 0;
    atomic_store(&stream_counter_master, 0u);
    atomic_store(&stream_counter_slave, 0u);
    log_i("MIXER RESUME!!!");
}
MSH_CMD_EXPORT_ALIAS(resume_mixer, resume, resume mixer);

void pause_mixer(void)
{
    is_pause = 1;
    log_i("MIXER PAUSE!!!");
}
MSH_CMD_EXPORT_ALIAS(pause_mixer, pause, pause mixer);

static int32_t mixer_query_open(mixer_def_p pmixer)
{
    int32_t err = -1;
    uint32_t f_size = 0; /* fast static memory size */
    uint32_t s_size = 0; /* static memory size */
    uint32_t d_size = 0; /* dynamic memory size */
    void *p_fast = NULL;
    void *p_static = NULL;
    void *p_dynamic = NULL;

    log_i("%s mixer query open.", pmixer->name);

    pthread_mutex_init(&pmixer->pmutex, NULL);

    pmixer->p_mixer_in = malloc(sizeof(int32_t *) * pmixer->ichns);
    pmixer->p_mixer_out = malloc(sizeof(int32_t *) * pmixer->ochns);

    pmixer->load_filter = sliding_average_filter_create(100);

    err = ym_matrix_querymem[pmixer->latency_id](
                             (pmixer->latency_id == LATENCY_5MS) ? MIXER_BLOCK_SIZE_5MS : MIXER_BLOCK_SIZE_10MS,
                             &f_size,
                             &s_size,
                             &d_size,
                             pmixer->ichns, /* input channel number */
                             pmixer->ochns, /* output channel number */
                             NULL);
    if (err == 0)
    {
        log_i("mixer query mem successful.");
        log_i("%d\t%d\t%d", f_size, s_size, d_size);
        p_fast = aligned_malloc(f_size, 1024);
        p_static = aligned_malloc(s_size, 1024);
        p_dynamic = aligned_malloc(d_size, 1024);
        if (p_fast == NULL || p_static == NULL || p_dynamic == NULL)
        {
            log_e("mixer malloc err.");
        }
        else
        {
            log_i("p_fast   :0x%p", p_fast);
            log_i("p_static :0x%p", p_static);
            log_i("p_dynamic:0x%p", p_dynamic);
            memset(p_fast, 0, f_size);
            memset(p_static, 0, s_size);
            memset(p_dynamic, 0, d_size);
            pmixer->handle = ym_matrix_open[pmixer->latency_id](YM_MIXER_SAMPLING_RATE,
                                            (pmixer->latency_id == LATENCY_5MS) ? MIXER_BLOCK_SIZE_5MS : MIXER_BLOCK_SIZE_10MS,
                                            p_fast,
                                            p_static,
                                            p_dynamic,
                                            pmixer->ichns,
                                            pmixer->ochns,
                                            d_size,
                                            NULL);
            if (pmixer->handle)
            {
                log_i("%s mixer lib: open successful.", pmixer->name);
                mixer_set_mixer_id[pmixer->latency_id](pmixer->handle, pmixer->name[0]);
                err = 0;
            }
            else
            {
                log_e("%s mixer lib: open failed.", pmixer->name);
                return (-2);
            }
        }
    }
    else
    {
        log_e("%s mixer query mem failed.result:%d", pmixer->name, err);
        usleep(1000 * 10);
        return (-1);
    }

    return err;
}

static void mixer_stream_in_notify(void *args)
{
    mixer_stream_in_p pstream = (mixer_stream_in_p)args;
    uint32_t mask = 0;
    for (int32_t i = 0; i < pstream->channels; i++)
    {
        mask |= 1 << (i + pstream->offset);
    }

    atomic_fetch_or(pstream->input_mask, mask);
    sem_post(pstream->notify_sem);
}

void mixer_stream_in_init(mixer_stream_in_p pstream, mixer_def_p pmixer)
{
    int32_t offset = 0;
    mixer_stream_in_p pos;
    pstream->stream = yinkman_stream_get_by_name(pstream->name);
    if (pstream->stream)
    {
        pstream->mutex = &pmixer->pmutex;
        pstream->input_mask = &pmixer->input_mask;
        pstream->notify_sem = &pmixer->input_notify_sem;

        pstream->mask = yinkman_stream_register(pstream->stream, mixer_stream_in_notify, pstream);

        DL_FOREACH(pmixer->stream_in, pos)
        {
            offset += pos->channels;
        }
        pstream->offset = offset;
        DL_APPEND(pmixer->stream_in, pstream);
    }
    else
        log_e("%s stream not found.", pstream->name);
}

void mixer_stream_in_chn_remap_register(pstream_chn_remap callback)
{
    if (callback)
    {
        master_mixer.stream_in_chn_remap = callback;
        slave_mixer.stream_in_chn_remap = callback;
    }
}

void mixer_stream_out_chn_remap_register(pstream_chn_remap callback)
{
    if (callback)
    {
        master_mixer.stream_out_chn_remap = callback;
        slave_mixer.stream_out_chn_remap = callback;
    }
}

void beam_process_callback_register(pbeam_proc_callback callback, int32_t m_s)
{
    if (callback)
    {
        if (m_s) /* master */
            master_mixer.beam_proc_callback = callback;
        else
            slave_mixer.beam_proc_callback = callback;
    }
}

void beam_semphore_callback_register(pbeam_proc_callback callback, int32_t m_s)
{
    if (callback)
    {
        if (m_s) /* master */
            master_mixer.beam_sem_callback = callback;
        else
            slave_mixer.beam_sem_callback = callback;
    }
}

#define MIXER_10MS_BUFFER_SIZE  (3)
static int32_t *mixer_10ms_buffer_in;
static int32_t *mixer_10ms_buffer_in_address[2][MIXER_INPUT_CHN_MAX];
static int32_t *mixer_10ms_buffer_out;
static int32_t *mixer_10ms_buffer_out_slave[MIXER_10MS_BUFFER_SIZE][MIXER_OUTPUT_CHN_MAX];  /* 2 * 10.6ms buffer */
static int32_t *mixer_10ms_buffer_out_master[MIXER_10MS_BUFFER_SIZE * 2][MIXER_OUTPUT_CHN_MAX]; /* 4 * 5.3ms buffer */

static void mixer_10ms_buffer_init(void)
{
    mixer_10ms_buffer_in = (int32_t *)calloc(MIXER_BLOCK_SIZE_10MS * MIXER_INPUT_CHN_MAX, sizeof(int32_t));
    mixer_10ms_buffer_out = (int32_t *)calloc(MIXER_BLOCK_SIZE_10MS * MIXER_OUTPUT_CHN_MAX * MIXER_10MS_BUFFER_SIZE, sizeof(int32_t));

    for (uint32_t i = 0; i < MIXER_INPUT_CHN_MAX; i++)
    {
        mixer_10ms_buffer_in_address[0][i] = &mixer_10ms_buffer_in[i * MIXER_BLOCK_SIZE_10MS];
        mixer_10ms_buffer_in_address[1][i] = &mixer_10ms_buffer_in[i * MIXER_BLOCK_SIZE_10MS + (MIXER_BLOCK_SIZE_10MS >> 1)];
    }

    for (uint32_t i = 0; i < MIXER_OUTPUT_CHN_MAX; i++)
    {
        mixer_10ms_buffer_out_slave[0][i] = &mixer_10ms_buffer_out[i * MIXER_BLOCK_SIZE_10MS];
        mixer_10ms_buffer_out_slave[1][i] = &mixer_10ms_buffer_out[(i + MIXER_OUTPUT_CHN_MAX) * MIXER_BLOCK_SIZE_10MS];
        mixer_10ms_buffer_out_slave[2][i] = &mixer_10ms_buffer_out[(i + MIXER_OUTPUT_CHN_MAX * 2) * MIXER_BLOCK_SIZE_10MS];
        mixer_10ms_buffer_out_master[0][i] = &mixer_10ms_buffer_out[i * MIXER_BLOCK_SIZE_10MS];
        mixer_10ms_buffer_out_master[1][i] = &mixer_10ms_buffer_out[i * MIXER_BLOCK_SIZE_10MS + (MIXER_BLOCK_SIZE_10MS >> 1)];
        mixer_10ms_buffer_out_master[2][i] = &mixer_10ms_buffer_out[(i + MIXER_OUTPUT_CHN_MAX) * MIXER_BLOCK_SIZE_10MS];
        mixer_10ms_buffer_out_master[3][i] = &mixer_10ms_buffer_out[(i + MIXER_OUTPUT_CHN_MAX) * MIXER_BLOCK_SIZE_10MS + (MIXER_BLOCK_SIZE_10MS >> 1)];
        mixer_10ms_buffer_out_master[4][i] = &mixer_10ms_buffer_out[(i + MIXER_OUTPUT_CHN_MAX * 2) * MIXER_BLOCK_SIZE_10MS];
        mixer_10ms_buffer_out_master[5][i] = &mixer_10ms_buffer_out[(i + MIXER_OUTPUT_CHN_MAX * 2) * MIXER_BLOCK_SIZE_10MS + (MIXER_BLOCK_SIZE_10MS >> 1)];
    }
}

static int32_t hw_mixer_init(void)
{
    master_mixer.database_base_index = MIXER1_BASE_INDEX;
    slave_mixer.database_base_index = MIXER2_BASE_INDEX;

    ym_mixer_latency_5ms_init();
    ym_mixer_latency_10ms_init();

    master_mixer.latency_id = LATENCY_5MS;
    slave_mixer.latency_id = LATENCY_10MS;

    mixer_query_open(&master_mixer);
    mixer_query_open(&slave_mixer);

    sem_init(&master_mixer.input_notify_sem, 0, 0);
    sem_init(&slave_mixer.input_notify_sem, 0, 0);

    ym_mixer_master_handle = master_mixer.handle;
    ym_mixer_slave_handle = slave_mixer.handle;

#if !defined(SYSTEM_NO_AUDIO_STREAM) && !defined(WATCHDOG_DISABLE)
    watchdog_init(WATCHDOG_TIMEOUT);
#endif /* end SYSTEM_NO_AUDIO_STREAM */

    mixer_10ms_buffer_init();

    return 0;
}
INIT_HW_EXPORT(hw_mixer_init);

static int32_t mixer_audio_out_stream_init(void)
{
    master_mixer.audio_out_stream = yinkman_stream_with_sem_create("m_audio_out", MIXER_BLOCK_SIZE_5MS, master_mixer.ochns * (FORMAT) / 8, 1);
    if (master_mixer.audio_out_stream == NULL)
    {
        log_e("master_audio_out stream init failed.");
        return -1;
    }

    log_i("mixer_audio_out_stream_init successful.");
    return 0;
}
INIT_STREAM_EXPORT(mixer_audio_out_stream_init);

static void mmixer_process_count_callback(int32_t index, database_value old_value, database_value new_value)
{
    if (new_value.u32 % 50 == 0)
    {
        // led_toggle();
#if !defined(SYSTEM_NO_AUDIO_STREAM) && !defined(WATCHDOG_DISABLE)
        watchdog_feed();
#endif /* end SYSTEM_NO_AUDIO_STREAM */
    }
}

static int32_t mixer_thread_init(void)
{
    database_value tmp = {.i32 = 96 * YM_MATRIX_FRAME_DB_QUANT};
    uint32_t base = master_mixer.database_base_index;
    for (uint32_t i = 0; i < mixer_input_channel1_sig_db_ms_unit; i++)
    {
        database_write(mixer_input_channel1_sig_db_ms_index + base + i, tmp);
    }
    for (uint32_t i = 0; i < mixer_output_channel1_sig_db_ms_unit; i++)
    {
        database_write(mixer_output_channel1_sig_db_ms_index + base + i, tmp);
    }

    base = slave_mixer.database_base_index;
    for (uint32_t i = 0; i < mixer_input_channel1_sig_db_ms_unit; i++)
    {
        database_write(mixer_input_channel1_sig_db_ms_index + base + i, tmp);
    }
    for (uint32_t i = 0; i < mixer_output_channel1_sig_db_ms_unit; i++)
    {
        database_write(mixer_output_channel1_sig_db_ms_index + base + i, tmp);
    }

    database_register(mixer_process_count_ms_index + MIXER1_BASE_INDEX, mmixer_process_count_callback);

    ym_thread_create("audio_out_mixer",
                     mmixer_thread_cpuid,
                     smixer_thread_prio,
                     audio_out_mixer_handle,
                     NULL);

    ym_thread_create("mmixer",
                     mmixer_thread_cpuid,
                     mmixer_thread_prio,
                     mixer_thread_handle,
                     &master_mixer);

    ym_thread_create("smixer",
                     smixer_thread_cpuid,
                     smixer_thread_prio,
                     mixer_thread_handle,
                     &slave_mixer);

    log_i("mixer_thread_init successful.");
    return 0;
}
INIT_APP_BEFORE_EXPORT(mixer_thread_init);

void mixer_wait_audio_in(mixer_def_p pmixer)
{
    mixer_stream_in_p stream_pos;
    uint32_t mask = 0;
    DL_FOREACH(pmixer->stream_in, stream_pos)
    {
        for (int32_t i = 0; i < stream_pos->channels; i++)
        {
            mask |= 1 << (i + stream_pos->offset);
        }
    }

    while (1)
    {
        sem_wait(&pmixer->input_notify_sem);
        if (atomic_load(&pmixer->input_mask) == mask)
        {
            break;
        }
    }

    atomic_store(&pmixer->input_mask, 0);

    DL_FOREACH(pmixer->stream_in, stream_pos)
    {
        if (yinkman_stream_pull(stream_pos->stream, (void **)&stream_pos->data, stream_pos->mask))
        {
            log_e("%s pull %s stream failed.", pmixer->name, stream_pos->name);
            continue;
        }
        audio_set_chn_map(&(pmixer->p_mixer_in[stream_pos->offset]), (int32_t *)(stream_pos->data), stream_pos->channels, FRAMES);
    }
}

int32_t mixer_out_stream_alloc(mixer_def_p pmixer)
{
    uint8_t *data = yinkman_stream_malloc(pmixer->audio_out_stream);
    if (data)
    {
        audio_set_chn_map(pmixer->p_mixer_out, (int32_t *)data, pmixer->ochns, ((pmixer->latency_id == LATENCY_5MS) ? MIXER_BLOCK_SIZE_5MS : MIXER_BLOCK_SIZE_10MS));
        return 0;
    }

    return 1;
}

void mixer_audio_in_free(mixer_def_p pmixer)
{
    mixer_stream_in_p stream_pos;
    DL_FOREACH(pmixer->stream_in, stream_pos)
    {
        yinkman_stream_free(stream_pos->stream, stream_pos->data, stream_pos->mask);
    }
}

static void audio_out_mixer(int32_t *master_out, uint32_t index)
{
    uint8_t *data = NULL;
    mixer_stream_out_p stream_pos;
    int32_t *slave_out = NULL;

    for (uint32_t i = 0; i < master_mixer.ochns; i++)
    {
        slave_out = mixer_10ms_buffer_out_master[index][i];
        for (uint32_t j = 0; j < FRAMES; j++)
        {
            master_out[i * FRAMES + j] += slave_out[j];
        }
    }

    DL_FOREACH(master_mixer.stream_out, stream_pos)
    {
        data = yinkman_stream_malloc(stream_pos->stream);
        if (data)
        {
            if (stream_pos->channels == 1)
            {
                memcpy(data, master_mixer.p_mixer_out[stream_pos->offset], stream_pos->channels * (FORMAT / 8) * FRAMES);
            }
            else
            {
                if (stream_pos->interleaved)
                {
                    audio32_noninterleaved_to_interleaved(master_mixer.p_mixer_out[stream_pos->offset],
                                                          (int32_t *)data,
                                                          stream_pos->channels,
                                                          FRAMES);
                }
                else
                {
                    memcpy(data, master_mixer.p_mixer_out[stream_pos->offset], stream_pos->channels * (FORMAT / 8) * FRAMES);
                }
            }

            if (yinkman_stream_push(stream_pos->stream, data) != 0)
            {
                log_e("Failed to push data to stream: %s", stream_pos->stream ? yinkman_stream_name(stream_pos->stream) : "unknown");
            }
        }
        else
        {
            log_e("Failed to allocate stream buffer, memory pool might be exhausted");
        }
    }
}

static void mixer_first_frame_handle(mixer_def_p pmixer, uint32_t *max_sig_level)
{
    uint32_t frames = (pmixer->latency_id == LATENCY_5MS) ? MIXER_BLOCK_SIZE_5MS : MIXER_BLOCK_SIZE_10MS;
    int32_t *in_buff = malloc(sizeof(int32_t) * frames * pmixer->ichns);
    int32_t *out_buff = malloc(sizeof(int32_t) * frames * pmixer->ochns);
    audio_set_chn_map(pmixer->p_mixer_in, in_buff, pmixer->ichns, frames);
    audio_set_chn_map(pmixer->p_mixer_out, out_buff, pmixer->ochns, frames);
    pthread_mutex_lock(&pmixer->pmutex);
    ym_matrix_process[pmixer->latency_id](pmixer->handle,
                      pmixer->p_mixer_in,
                      pmixer->p_mixer_out,
                      1, 1, (uint32_t *)max_sig_level);
    pthread_mutex_unlock(&pmixer->pmutex);
    free(in_buff);
    free(out_buff);
}

static void *mixer_thread_handle(void *arg)
{
    mixer_def_p pmixer = (mixer_def_p)arg;
    static uint32_t stream_buffer_id[6] = {2u, 2u, 0u, 0u, 1u, 1u};

    const int32_t base = pmixer->database_base_index;
    pmixer->p_mixer_in_bak = malloc(sizeof(int32_t *) * pmixer->ichns);
    pmixer->p_mixer_out_bak = malloc(sizeof(int32_t *) * pmixer->ochns);
    pmixer->max_sig_level = malloc(sizeof(database_value) * (pmixer->ichns + pmixer->ochns));

    int32_t i;
    pmixer->input_sig_filter = malloc(sizeof(sliding_average_filter_t) * pmixer->ichns);
    pmixer->output_sig_filter = malloc(sizeof(sliding_average_filter_t) * pmixer->ochns);

    for (i = 0; i < pmixer->ichns; i++)
    {
        sliding_average_filter_init(&pmixer->input_sig_filter[i], MIXER_SIG_FILTER_COUNT);
    }
    for (i = 0; i < pmixer->ochns; i++)
    {
        sliding_average_filter_init(&pmixer->output_sig_filter[i], MIXER_SIG_FILTER_COUNT);
    }

    database_value tmp_value;
    uint64_t start_tick;
    uint64_t end_tick;
    double frame_tick = (pmixer->latency_id == LATENCY_5MS) ? FRAMES_UNIT_US : (FRAMES_UNIT_US * 2);
#if defined(BEAM_ENABLE)
    uint64_t beam_start_tick = 0u;
    uint64_t beam_end_tick = 0u;
    if (pmixer->beam_sem_callback)
        pmixer->beam_sem_callback(1, NULL);
#endif
    mixer_first_frame_handle(pmixer, (uint32_t *)pmixer->max_sig_level);

    for (;;)
    {
#if defined(BEAM_ENABLE)
        if (pmixer->beam_sem_callback)
        {
            if (pmixer->beam_sem_callback(0, NULL) != 0)
                continue;
        }

        beam_start_tick = ym_tick_get_us();

        if (pmixer->beam_proc_callback)
        {
            if (pmixer->beam_proc_callback(0, pmixer) != 0)
                continue;
        }

        beam_end_tick = ym_tick_get_us();
#endif

        mixer_wait_audio_in(pmixer);

        /* put high latency stream data into buffer, stream buffer is 256 frames, but high latency buffer is 512 frames */
        if (pmixer->latency_id == LATENCY_10MS)
        {
            /* pause state, do not need to copy data */
            if (!is_pause)
            {
                for (i = 0; i < pmixer->ichns; i++)
                {
                    memcpy(mixer_10ms_buffer_in_address[(atomic_load(&stream_counter_slave) & 0x1)][i], pmixer->p_mixer_in[i], FRAMES * sizeof(int32_t));
                }
            }

            mixer_audio_in_free(pmixer);
            if ((atomic_load(&stream_counter_slave) & 0x1) == 0)
            {
                atomic_fetch_add(&stream_counter_slave, 1u);
                continue;
            }
        }
        else
        {
            if (mixer_out_stream_alloc(pmixer))
            {
                mixer_audio_in_free(pmixer);
                continue;
            }
        }

        start_tick = ym_tick_get_us();

        tmp_value.u32 = database_get(mixer_process_count_ms_index + base)->u32 + 1;
        database_write(mixer_process_count_ms_index + base, tmp_value);

        if (is_pause)
        {
            /* pause state, clear output data and do not run mixer */
            if (pmixer->latency_id == LATENCY_5MS)
            {
                for (i = 0; i < pmixer->ochns; i++)
                {
                    memset(pmixer->p_mixer_out[i], 0, FRAMES * sizeof(int32_t));
                }
            }
            else
            {
                memset(mixer_10ms_buffer_out_slave[stream_buffer_id[(atomic_fetch_add(&stream_counter_slave, 1u) % 6u)]][0], 0, MIXER_BLOCK_SIZE_10MS * MIXER_OUTPUT_CHN_MAX * sizeof(int32_t));
            }
#if defined(FIRMWARE_UPGRADE_SUPPORT_MIC_MATRIX)
            extern int32_t slave_firmware_load_pieces(int32_t *pout[], int32_t chns, int32_t nums);
            if (pmixer->database_base_index == MIXER1_BASE_INDEX)
            {
                slave_firmware_load_pieces(pmixer->p_mixer_out, pmixer->ochns, FRAMES);
            }
#endif /* end #if defined(FIRMWARE_UPGRADE_SUPPORT_MIC_MATRIX) */
        }
        else
        {
            if (pmixer->latency_id == LATENCY_5MS)
            {
                memcpy(pmixer->p_mixer_in_bak, pmixer->p_mixer_in, sizeof(int32_t *) * pmixer->ichns);
                memcpy(pmixer->p_mixer_out_bak, pmixer->p_mixer_out, sizeof(int32_t *) * pmixer->ochns);
            }
            else
            {
                memcpy(pmixer->p_mixer_in_bak, &mixer_10ms_buffer_in_address[0][0], sizeof(int32_t *) * pmixer->ichns);
                memcpy(pmixer->p_mixer_out_bak, &mixer_10ms_buffer_out_slave[stream_buffer_id[(atomic_fetch_add(&stream_counter_slave, 1u) % 6u)]][0], sizeof(int32_t *) * pmixer->ochns);
            }

            if (pmixer->stream_in_chn_remap)
                pmixer->stream_in_chn_remap(pmixer->p_mixer_in_bak, pmixer->ichns);

            if (pmixer->stream_out_chn_remap)
                pmixer->stream_out_chn_remap(pmixer->p_mixer_out_bak, pmixer->ochns);

            pthread_mutex_lock(&pmixer->pmutex);
            ym_matrix_process[pmixer->latency_id](pmixer->handle,
                              pmixer->p_mixer_in_bak,
                              pmixer->p_mixer_out_bak,
                              1, 1, (uint32_t *)pmixer->max_sig_level);
            pthread_mutex_unlock(&pmixer->pmutex);
        }

        if (pmixer->latency_id == LATENCY_5MS)
        {
            mixer_audio_in_free(pmixer);
            yinkman_stream_push(pmixer->audio_out_stream, pmixer->p_mixer_out[0]);
        }

        for (i = 0; i < pmixer->ichns; i++)
        {
            pmixer->max_sig_level[i].i32 = (int32_t)sliding_average_filter(&pmixer->input_sig_filter[i],
                                                                       int2dB(pmixer->max_sig_level[i].i32));
            database_write(mixer_input_channel1_sig_db_ms_index + base + i, pmixer->max_sig_level[i]);
        }

        for (i = 0; i < pmixer->ochns; i++)
        {
            pmixer->max_sig_level[pmixer->ichns + i].i32 = (int32_t)sliding_average_filter(&pmixer->output_sig_filter[i],
                                                                                       int2dB(pmixer->max_sig_level[pmixer->ichns + i].i32));
            database_write(mixer_output_channel1_sig_db_ms_index + base + i, pmixer->max_sig_level[pmixer->ichns + i]);
        }

        end_tick = ym_tick_get_us();
        tmp_value.f32 = (float)(end_tick - start_tick);

#if defined(BEAM_ENABLE)
        tmp_value.f32 += (float)(beam_end_tick - beam_start_tick);
#endif

        if (tmp_value.f32 > frame_tick)
        {
            log_e("over load:%fus", tmp_value.f32);
            // 清空一次输入
            // mixer_wait_audio_in(pmixer);
            // mixer_audio_in_free(pmixer);
        }
        tmp_value.f32 = sliding_average_filter(pmixer->load_filter, tmp_value.f32);
        database_write(mixer_process_tick_ms_index + base, tmp_value);
        tmp_value.i32 = (int32_t)(tmp_value.f32 * 100 / frame_tick);
        database_write(cpu_load_ms_index + base, tmp_value);
    }
    return NULL;
}

static void *audio_out_mixer_handle(void *arg)
{
    int32_t *master_out= NULL;

    for (;;)
    {
        master_out = NULL;
        yinkman_stream_wait(master_mixer.audio_out_stream);
        yinkman_stream_pull(master_mixer.audio_out_stream, (void **)&master_out, 0x01);

        if (master_out)
        {
#if !defined(FIRMWARE_UPGRADE_SUPPORT_MIC_MATRIX)
            if (!is_pause)
#endif /* end !defined(FIRMWARE_UPGRADE_SUPPORT_MIC_MATRIX) */
            {
                audio_out_mixer(master_out, (atomic_fetch_add(&stream_counter_master, 1) % 6u));
            }
            yinkman_stream_free(master_mixer.audio_out_stream, (unsigned char *)master_out, 0x01);
        }
        else
            log_e("audio_out_mixer_handle failed.");
    }

    return NULL;
}

static int32_t audio_analyse(int32_t argc, char **argv)
{
    printf("beam_process  : %8dus   %-8d\n", (int32_t)(database_get(beam_process_tick_index + BEAM_BASE_INDEX)->f32),
           database_get(beam_process_count_index + BEAM_BASE_INDEX)->u32);

    printf("mmixer_process: %8dus   %-8d\n", (int32_t)(database_get(mixer_process_tick_ms_index + MIXER1_BASE_INDEX)->f32),
           database_get(mixer_process_count_ms_index + MIXER1_BASE_INDEX)->u32);

    printf("smixer_process: %8dus   %-8d\n", (int32_t)(database_get(mixer_process_tick_ms_index + MIXER2_BASE_INDEX)->f32),
           database_get(mixer_process_count_ms_index + MIXER2_BASE_INDEX)->u32);
    return 0;
}
MSH_CMD_EXPORT(audio_analyse, audio analyse);

void mixer_lib_lock(int32_t ms)
{
    if (ms == 1)
    {
        pthread_mutex_lock(&master_mixer.pmutex);
    }
    else if (ms == 2)
    {
        pthread_mutex_lock(&slave_mixer.pmutex);
    }
}

void mixer_lib_unlock(int32_t ms)
{
    if (ms == 1)
    {
        pthread_mutex_unlock(&master_mixer.pmutex);
    }
    else if (ms == 2)
    {
        pthread_mutex_unlock(&slave_mixer.pmutex);
    }
}

used_output_stream_p used_output_stream_list = NULL;

bool is_used_output_stream(const char *name)
{
    used_output_stream_p pos;
    DL_FOREACH(used_output_stream_list, pos)
    {
        if (!strcmp(name, pos->name))
            return true;
    }
    return false;
}

static int32_t aecdmx(int32_t argc, char **argv)
{
    printf("-----------------------------------------------\n");
    mixer_printAecDmxInfo[LATENCY_5MS](ym_mixer_master_handle);
    printf("-----------------------------------------------\n");
    mixer_printAecDmxInfo[LATENCY_10MS](ym_mixer_slave_handle);
    printf("-----------------------------------------------\n");
    return 0;
}
MSH_CMD_EXPORT(aecdmx, print aec downmix info);

static int32_t aecg(int32_t argc, char **argv)
{
    if (argc < 3)
    {
        printf("Synopsis:\n");
        printf("\taecg [channel] [core]\n");
        printf("\t[channel]: aec channel, only 0 or 1 is valid\n");
        printf("\t[core]: 0 master core, 1 slave core\n");
        return -1;
    }

    uint32_t iMic = atoi(argv[1]);
    uint32_t core = atoi(argv[2]);
    uint32_t iMax;
    uint32_t blkNum;
    uint32_t i, startSample;
    float totEng = 0.0f, aecenergy;
    latency_e latency = (core == 0) ? LATENCY_5MS : LATENCY_10MS;

    mixer_lib_lock(core ? 2 : 1);
    float *restrict p_blkEng = mixerGetAECFilterBlkEng[latency](core ? ym_mixer_slave_handle : ym_mixer_master_handle,
                                                       iMic,
                                                       &iMax,
                                                       &totEng,
                                                       &blkNum,
                                                       &startSample);
    mixer_lib_unlock(core ? 2 : 1);
    if (iMic == 0)
    {
        aecenergy = (float)database_get(aec_0_energy_ms_index + core ? MIXER2_BASE_INDEX : MIXER1_BASE_INDEX)->i32;
    }
    else
    {
        aecenergy = (float)database_get(aec_1_energy_ms_index + core ? MIXER2_BASE_INDEX : MIXER1_BASE_INDEX)->i32;
    }

    log_i("FilEng: %.5e Measured :%.5e, maxIdx[%d] eng %.5e; start sample %d, last eng %.5e",
          totEng, aecenergy, iMax, p_blkEng[iMax], startSample, p_blkEng[blkNum - 1]);
    if (blkNum > 3)
        for (i = 0; i < blkNum - 3; i += 4)
        {
            log_i("%.5f, %.5f, %.5f, %.5f", p_blkEng[i + 0], p_blkEng[i + 1], p_blkEng[i + 2], p_blkEng[i + 3]);
        }
    if ((blkNum & 0x3) == 1)
    {
        log_i("%.5f", p_blkEng[blkNum - 1]);
    }
    if ((blkNum & 0x3) == 2)
    {
        log_i("%.5f, %.5f", p_blkEng[blkNum - 2], p_blkEng[blkNum - 1]);
    }
    if ((blkNum & 0x3) == 3)
    {
        log_i("%.5f, %.5f, %.5f", p_blkEng[blkNum - 3], p_blkEng[blkNum - 2], p_blkEng[blkNum - 1]);
    }

    return 0;
}
MSH_CMD_EXPORT(aecg, get aec filter blk energy);