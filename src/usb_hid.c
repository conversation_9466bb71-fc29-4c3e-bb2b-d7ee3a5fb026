#include <stdio.h>
#include <string.h>
#include <fcntl.h>
#include <errno.h>
#include <stdlib.h>
#include <unistd.h>
#include <pthread.h>
#include <semaphore.h>
#include <stdbool.h>
#include "ym_shell.h"
#include "usb_hid.h"
#include "ym_thread.h"
#include "ym_log.h"
#include "ym_database.h"
#include "config.h"


#define HID_DEV_NAME "/dev/hidg0"
#define HID_TRANS_BUFFER_SIZE  128

#define HID_REPORT_DESC_COMM       0
#define HID_REPORT_DESC_VOL_CTRL   1
#define HID_REPORT_DESC_VOL_SET    0

#if HID_REPORT_DESC_VOL_CTRL
#define HID_REPORT_DESC_VOL_CTRL_DEBUG 0

typedef struct taghid_vol_ctrl_t
{
	int flag;
	int event;
	pthread_mutex_t hid_mutex;
	sem_t hid_sem;
	sem_t hid_uac_sync_sem;
} hid_vol_ctrl_t, *phid_vol_ctrl_t;
#endif /* end HID_REPORT_DESC_VOL_CTRL */

typedef struct taghid_mgmt_t
{
	int fd;
	void *hid_thread_handle;
#if HID_REPORT_DESC_VOL_CTRL
	void *uac_sync_thread_handle;
	hid_vol_ctrl_t hid_vol_ctrl;
#endif /* end HID_REPORT_DESC_VOL_CTRL */
} hid_mgmt_t, *phid_mgmt_t;

static hid_mgmt_t hid_mgmt;


static int hid_resource_alloc(void)
{
	int err = 0;
#if HID_REPORT_DESC_VOL_CTRL
	err = pthread_mutex_init(&hid_mgmt.hid_vol_ctrl.hid_mutex, NULL);
	if (err != 0)
		log_e("Init hid mutex failed!");

	err = sem_init(&hid_mgmt.hid_vol_ctrl.hid_sem, 0, 0);
	if (err != 0)
		log_e("Init hid sem failed!");

	err = sem_init(&hid_mgmt.hid_vol_ctrl.hid_uac_sync_sem, 0, 0);
	if (err != 0)
		log_e("Init hid_uac_sync_sem failed!");

	hid_mgmt.hid_vol_ctrl.flag = 13245768;
#endif /* end HID_REPORT_DESC_VOL_CTRL */
	return err;
}

static int hid_resource_release(void)
{
int err = 0;
#if HID_REPORT_DESC_VOL_CTRL
	if (hid_mgmt.hid_vol_ctrl.flag == 13245768)
	{
		err = pthread_mutex_destroy(&hid_mgmt.hid_vol_ctrl.hid_mutex);

		err = sem_destroy(&hid_mgmt.hid_vol_ctrl.hid_sem);

		err = sem_destroy(&hid_mgmt.hid_vol_ctrl.hid_uac_sync_sem);
	}
	hid_mgmt.hid_vol_ctrl.flag = 0;
#endif /* end HID_REPORT_DESC_VOL_CTRL */
	return err;
}

int hid_init(void)
{
	memset(&hid_mgmt, 0, sizeof(hid_mgmt_t));
	hid_resource_alloc();

#if HID_REPORT_DESC_COMM
	hid_mgmt.fd = open(HID_DEV_NAME, O_RDWR | O_DSYNC, 0666);
#elif HID_REPORT_DESC_VOL_CTRL
	hid_mgmt.fd = open(HID_DEV_NAME, O_WRONLY | O_DSYNC, 0222);
#elif HID_REPORT_DESC_VOL_SET
	hid_mgmt.fd = open(HID_DEV_NAME, O_WRONLY | O_DSYNC, 0222);
#else
	#error "No hid report was specified!"
#endif
	if (hid_mgmt.fd < 0)
	{
		log_e("open %s failed", HID_DEV_NAME);
		return -1;
	}
	else
	{
		log_i("open %s succeed", HID_DEV_NAME);
	}
	hid_thread_init();

	return 0;
}

int hid_close(void)
{
	if (hid_mgmt.hid_thread_handle)
    {
        ym_thread_destroy(hid_mgmt.hid_thread_handle);
        hid_mgmt.hid_thread_handle = NULL;
    }
#if HID_REPORT_DESC_VOL_CTRL
	if (hid_mgmt.uac_sync_thread_handle)
    {
		ym_thread_destroy(hid_mgmt.uac_sync_thread_handle);
        hid_mgmt.uac_sync_thread_handle = NULL;
    }
#endif /* end HID_REPORT_DESC_VOL_CTRL */
    if (hid_mgmt.fd > 0)
    {
        close(hid_mgmt.fd);
        hid_mgmt.fd = -1;
    }

    hid_resource_release();

    return 0;
}

/* HID userdefined communication example */
#if HID_REPORT_DESC_COMM
static void *hid_thread_handle(void *arg)
{
	ssize_t rd_size;
	hid_mgmt_t *phid = (hid_mgmt_t *)arg;
	int fd = phid->fd;
	int ret;
	fd_set rfds;
	unsigned char rdbuf[HID_TRANS_BUFFER_SIZE];
	if (fd < 0)
		pthread_exit(NULL);

	for (;;)
	{
		FD_ZERO(&rfds);
        FD_SET(fd, &rfds);
        ret = select(fd + 1, &rfds, NULL, NULL, NULL);
        if (ret == -1 && errno == EINTR)
            continue;
        if (ret < 0) {
            perror("select()");
            break;
        }

        if (FD_ISSET(fd, &rfds))
        {
        	memset(rdbuf, 0, HID_TRANS_BUFFER_SIZE);
			rd_size = read(fd, rdbuf, HID_TRANS_BUFFER_SIZE - 1);
			if (rd_size > 0)
			{

				/* loopback */
				write(fd, rdbuf, rd_size);
			}
		}
	}
	return NULL;
}
#endif /* end HID_REPORT_DESC_COMM */

#if HID_REPORT_DESC_VOL_CTRL
static void *hid_thread_handle(void *arg)
{
	hid_mgmt_t *phid = (hid_mgmt_t *)arg;
	int fd = phid->fd;
	int ret;
	unsigned char wrbuf[HID_TRANS_BUFFER_SIZE];
	if (fd < 0)
		pthread_exit(NULL);

	memset(wrbuf, 0, HID_TRANS_BUFFER_SIZE);

	for (;;)
	{
#if HID_REPORT_DESC_VOL_CTRL_DEBUG
		/* mute/unmute */
		wrbuf[0] = 0x04;
		ret = write(fd, wrbuf, 1);
		if (ret)
		{
			usleep(40000);
			wrbuf[0] = 0x00;
			ret = write(fd, wrbuf, 1);
		}
		usleep(3000000);

		/* play/pause */
		wrbuf[0] = 0x08;
		ret = write(fd, wrbuf, 1);
		if (ret)
		{
			usleep(40000);
			wrbuf[0] = 0x00;
			ret = write(fd, wrbuf, 1);
		}
		usleep(3000000);

		/* volume+, twice */
		for (int i = 0; 2 > i; i++)
		{
			wrbuf[0] = 0x01;
			ret = write(fd, wrbuf, 1);
			if (ret)
			{
				usleep(40000);
				wrbuf[0] = 0x00;
				ret = write(fd, wrbuf, 1);
				if (ret < 0)
					break;
			}
			usleep(2000000);
		}

		/* volume-, twice */
		for (int i = 0; 2 > i; i++)
		{
			wrbuf[0] = 0x02;
			ret = write(fd, wrbuf, 1);
			if (ret)
			{
				usleep(40000);
				wrbuf[0] = 0x00;
				ret = write(fd, wrbuf, 1);
				if (ret < 0)
					break;
			}
			usleep(2000000);
		}
#else
		if (sem_wait(&phid->hid_vol_ctrl.hid_sem) == -1)
			continue;

		pthread_mutex_lock(&phid->hid_vol_ctrl.hid_mutex);
		wrbuf[0] = phid->hid_vol_ctrl.event;
		pthread_mutex_unlock(&phid->hid_vol_ctrl.hid_mutex);

		ret = write(fd, wrbuf, 1);
		if (ret == 1)
		{
			usleep(10000);
			wrbuf[0] = 0x00;
			ret = write(fd, wrbuf, 1);
			if (ret != 1)
				printf("clear hid event failed!\n");
		}
		else if (ret < 0)
		{
			printf("send hid event failed(errno=%d)\n", errno);
		}
#endif /* end HID_REPORT_DESC_VOL_CTRL_DEBUG */
	}
	return NULL;
}

int hid_post_vol_ctrl_event(HID_CTRL_VOL_ENUM e)
{
	int err;
	pthread_mutex_lock(&hid_mgmt.hid_vol_ctrl.hid_mutex);
	hid_mgmt.hid_vol_ctrl.event = e;
	pthread_mutex_unlock(&hid_mgmt.hid_vol_ctrl.hid_mutex);
	err = sem_post(&hid_mgmt.hid_vol_ctrl.hid_sem);
	if (err == -1)
		log_e("post hid semaphore failed!");
	return err;
}

int hid_post_uac_sync_event(void)
{
	int err = sem_post(&hid_mgmt.hid_vol_ctrl.hid_uac_sync_sem);
	if (err == -1)
		log_e("post hid semaphore failed!");
	return err;
}

#define WINDOWS_VOLUME_STEP 2
#if defined(ANDROID_WINDOWS_VOL_SYNC)
extern void android_playback_vol_set(int vol);
extern int android_playback_vol_get(void);
extern void uac_capture_vol_set(int vol);
extern int uac_capture_vol_get(void);
extern int windows_volume_report(int vol);
extern bool usb_in_mute;
extern bool usb_out_mute;

static int hid_calc_windows_vol_step(int step_times, int is_sub_plus)
{
	int windows_vol_step = 0;
	int vol[2];
	int iswin_vol_0_and_muted = 0;
#if 0
	sleep(15);
#endif
	for (int i = step_times; i > 0; i--)
	{
		if (is_sub_plus)
			hid_post_vol_ctrl_event(HID_CTRL_VOL_SUB);
		else
			hid_post_vol_ctrl_event(HID_CTRL_VOL_PLUS);
		usleep(100000);
		vol[0] = uac_capture_vol_get();
		if (vol[0] == 100) /* 0 & muted */
		{
			iswin_vol_0_and_muted = 1;
		}
		else if (usb_in_mute)
		{
			if (vol[0] == 0)
				iswin_vol_0_and_muted = 1;
		}
		if (is_sub_plus)
			hid_post_vol_ctrl_event(HID_CTRL_VOL_PLUS);
		else
			hid_post_vol_ctrl_event(HID_CTRL_VOL_SUB);
		usleep(100000);
		vol[1] = uac_capture_vol_get();
		windows_vol_step = abs(vol[1] - vol[0]);
		log_i("The windows actual volume step is abs(%d-%d)=%d", vol[1], vol[0], windows_vol_step);
	}
	windows_vol_step &= 0xFFFFFFE; // must be even number
	if ((windows_vol_step == 0) || (windows_vol_step >= 100))
	{
		log_e("Detect windows volume step error!");
		windows_vol_step = WINDOWS_VOLUME_STEP;
	}
	if (iswin_vol_0_and_muted)
	{
		hid_post_vol_ctrl_event(HID_CTRL_VOL_SUB); // decrease volume to 0
		usleep(20000);
		hid_post_vol_ctrl_event(HID_CTRL_VOL_SUB); // make sure uac was muted
	}
#if 0
	/* check uac is muted or not */
	if (usb_in_mute) // muted
	{
		hid_post_vol_ctrl_event(HID_CTRL_VOL_MUTE);
		usleep(20000);
	}
	/* the windows volume is not same with android volume at starup */
	if (uac_capture_vol_get() != android_playback_vol_get())
		hid_post_uac_sync_event();
#else /* do not synchronize volume & mute for customer request */
#endif
	return windows_vol_step;
}
#endif /* end ANDROID_WINDOWS_VOL_SYNC */

static void *hid_uac_sync_thread_handle(void *arg)
{
	int uac_capture_volume = 0;
	int android_playback_vol = 0;
	int windows_vol_step = 0;
	int hid_calc_vol_step_flag = 0;
	int diff;
	int err;
	struct timespec ts;
#if defined(ANDROID_WINDOWS_VOL_SYNC)
	sleep(1);
	uac_capture_volume = uac_capture_vol_get();
	printf("The os init volume is %d\n", uac_capture_volume);
#endif /* end ANDROID_WINDOWS_VOL_SYNC */

	for (;;)
	{
		/* If a blocked sem_wait() call is interrupted by a signal handler, then it fails with the error EINTR */
		/* reset timeout */
		clock_gettime(CLOCK_REALTIME, &ts);
		ts.tv_nsec += 100000000; /* 100ms */
		if (ts.tv_nsec > 1000000000)
		{
			ts.tv_nsec -= 1000000000;
			ts.tv_sec++;
		}
		err = sem_timedwait(&hid_mgmt.hid_vol_ctrl.hid_uac_sync_sem, &ts); /* wait android playback volume event */
		if (err < 0) /* timeout */
		{
#if defined(ANDROID_WINDOWS_VOL_SYNC)
			if (uac_capture_volume != uac_capture_vol_get())
			{
				uac_capture_volume = uac_capture_vol_get();
				windows_volume_report(uac_capture_volume);
			}
#endif /* end ANDROID_WINDOWS_VOL_SYNC */
			continue;
		}
		else
		{
			if (!hid_calc_vol_step_flag)
			{
				hid_calc_vol_step_flag = 1;
				/* trigger event to update uac volume */
#if defined(ANDROID_WINDOWS_VOL_SYNC)
				if (uac_capture_volume < 10)
					windows_vol_step = hid_calc_windows_vol_step(1, 0);
				else
					windows_vol_step = hid_calc_windows_vol_step(1, 1);
				uac_capture_volume = uac_capture_vol_get();
#endif /* end ANDROID_WINDOWS_VOL_SYNC */
			}
		}

#if defined(ANDROID_WINDOWS_VOL_SYNC)
		uac_capture_volume = uac_capture_vol_get();
		android_playback_vol = android_playback_vol_get();
#else
		uac_capture_volume = android_playback_vol = 0;
#endif /* end ANDROID_WINDOWS_VOL_SYNC */

		diff = android_playback_vol - uac_capture_volume;
		if (abs(diff) >= windows_vol_step)
		{
#if 0
			log_i("uac %d vs android %d",uac_capture_volume,android_playback_vol);
#endif
			if (diff > 0)
			{
				while (diff >= windows_vol_step)
				{
					diff -= windows_vol_step;
					hid_post_vol_ctrl_event(HID_CTRL_VOL_PLUS);
					usleep(20000);
				}
			}
			else
			{
				while (diff <= -windows_vol_step)
				{
					diff += windows_vol_step;
					hid_post_vol_ctrl_event(HID_CTRL_VOL_SUB);
					usleep(20000);
				}
			}
		}
		/* patch */
#if defined(ANDROID_WINDOWS_VOL_SYNC)
		usleep(300000);
		diff = android_playback_vol - uac_capture_vol_get();
		if ((abs(diff) > (windows_vol_step/2)) && (windows_vol_step > 1))
		{
			if (diff > 0)
			{
				diff -= windows_vol_step;
				hid_post_vol_ctrl_event(HID_CTRL_VOL_PLUS);
			}
			else
			{
				diff += windows_vol_step;
				hid_post_vol_ctrl_event(HID_CTRL_VOL_SUB);
			}
			usleep(20000);
		}
#endif /* end ANDROID_WINDOWS_VOL_SYNC */
		if ((windows_vol_step/2) >= WINDOWS_VOLUME_STEP)
		{
			if (abs(diff) > (windows_vol_step/2))
			{
				if (diff > 0)
				{
					diff -= windows_vol_step;
					hid_post_vol_ctrl_event(HID_CTRL_VOL_PLUS);
				}
				else
				{
					diff += windows_vol_step;
					hid_post_vol_ctrl_event(HID_CTRL_VOL_SUB);
				}
				usleep(20000);
			}
		}
	}
	return NULL;
}
#endif /* end HID_REPORT_DESC_VOL_CTRL */

#if HID_REPORT_DESC_VOL_SET
static void *hid_thread_handle(void *arg)
{
	hid_mgmt_t *phid = (hid_mgmt_t *)arg;
	int fd = phid->fd;
	int ret;
	int vol = 0;
	unsigned char wrbuf[HID_TRANS_BUFFER_SIZE];
	if (fd < 0)
		pthread_exit(NULL);

	memset(wrbuf, 0, HID_TRANS_BUFFER_SIZE);

	for (;;)
	{
		wrbuf[0] = ++vol;
		if (vol >= 100)
			vol = 1;
		wrbuf[0] = 55;
		ret = write(fd, wrbuf, 1);
		if (ret < 0)
		{
			printf("set host volume failed\n");
		}
		usleep(2000000);
	}
	return NULL;
}
#endif /* end HID_REPORT_DESC_VOL_SET */

int hid_thread_init(void)
{
    hid_mgmt.hid_thread_handle = ym_thread_create("hid",
							                      default_thread_cpuid,
							                      default_thread_prio,
							                      hid_thread_handle,
							                      &hid_mgmt);
    if (hid_mgmt.hid_thread_handle == NULL)
    {
    	log_e("create hid thread failed");
    	return -1;
    }
    else
    {
        ym_thread_run(hid_mgmt.hid_thread_handle);
    }
#if HID_REPORT_DESC_VOL_CTRL
    hid_mgmt.uac_sync_thread_handle = ym_thread_create("hid-uac-sync",
									                    default_thread_cpuid,
									                    default_thread_prio,
									                    hid_uac_sync_thread_handle,
									                    NULL);
    if (hid_mgmt.uac_sync_thread_handle == NULL)
    {
    	log_e("create hid-uac-sync thread failed");
    	return -1;
    }
    else
    {
        ym_thread_run(hid_mgmt.uac_sync_thread_handle);
    }
#endif /* end HID_REPORT_DESC_VOL_CTRL */
    return 0;
}

#ifndef USB_HID_ON_SHELL_SERIAL_OFF
int hidctrl(int argc, char **argv)
{
    if (argc > 1)
    {
        if ((strncmp("+", argv[1], 1) == 0) || (strncmp("1", argv[1], 1) == 0))
        {
            hid_post_vol_ctrl_event(HID_CTRL_VOL_PLUS);
        }
        else if ((strncmp("-", argv[1], 1) == 0) || (strncmp("0", argv[1], 1) == 0))
        {
            hid_post_vol_ctrl_event(HID_CTRL_VOL_SUB);
        }
        else if ((strncmp("x", argv[1], 1) == 0) || (strncmp("2", argv[1], 1) == 0))
        {
            hid_post_vol_ctrl_event(HID_CTRL_VOL_MUTE);
        }
        else
        {
            goto HIDCTRL_ERROR;
        }
    }
    else
    {
        goto HIDCTRL_ERROR;
    }
    return 0;
HIDCTRL_ERROR:
    printf("Synopsis:\n");
    printf("\t hidctrl [option]\n");
    printf("\t[option]: +/1(volume+), -/0(volume-), x/2(mute/unmute)\n");
    return -1;
}
MSH_CMD_EXPORT(hidctrl, hid control volume);
#endif /* end #ifndef USB_HID_ON_SHELL_SERIAL_OFF */
