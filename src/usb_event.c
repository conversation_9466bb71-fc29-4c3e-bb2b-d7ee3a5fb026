/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-09-08 11:14:09
 * @LastEditTime: 2024-12-06 15:05:56
 * @LastEditors: Anzhichao
 * @Description:
 * @FilePath: /audio_mixer_rk3308/src/usb_event.c
 * Copyright (c) 2006-2024, Yinkman Development Team
 */

#include <libudev.h>

#include "usb_hid.h"
#include "ym_audio_mixer.h"
#include "ym_usb_serial.h"

typedef enum usb_otg_state
{
    usb_otg_unkown,
    usb_otg_connected,
    usb_otg_disconnected,
    usb_otg_configured,
    usb_otg_max
} usb_otg_state_e;

typedef struct usb_otg_event
{
    usb_otg_state_e otg_state;
    sem_t sem;
    pthread_mutex_t mutex;
} usb_otg_event_t, *usb_otg_event_p;

static usb_otg_event_t usb_otg_event;

void usb_audio_configured(void);
static void uac_event_configured(void);

void usb_audio_disconnected(void);
static void uac_event_disconnected(void);

bool usb_out_mute;
int usb_out_volume;
float usb_out_mag;
bool usb_in_mute;
int usb_in_volume;
float usb_in_mag;

#if defined(ANDROID_WINDOWS_VOL_SYNC)
extern void uac_playback_vol_set(int vol);
extern void uac_capture_vol_set(int vol);
#endif /* end #if defined(ANDROID_WINDOWS_VOL_SYNC) */

static void usb_configured(void)
{
    uac_event_configured();
    usb_serial_configured();
    // usb_audio_configured();
#ifdef USB_HID_ON_SHELL_SERIAL_OFF
    hid_init();
#endif /* end #ifdef USB_HID_ON_SHELL_SERIAL_OFF */
}

static void usb_disconnect_without_udc_restart(void)
{
    // usb_audio_disconnected();
    uac_event_disconnected();
    usb_serial_disconnected();
#ifdef USB_HID_ON_SHELL_SERIAL_OFF
    hid_close();
#endif /* end #ifdef USB_HID_ON_SHELL_SERIAL_OFF */
}

static void usb_disconnected(void)
{
    usb_disconnect_without_udc_restart();
    if (system("usbdevice.sh restart") < 0)
    {
        log_i("usbdevice.sh restart failed.");
    }
}

static usb_otg_state_e get_usb_state(void)
{
    FILE *usb_state_file = fopen("/sys/devices/virtual/android_usb/android0/state", "r");
    char usb_state[20] = {0};
    usb_otg_state_e state = usb_otg_unkown;
    if (usb_state_file)
    {
        if (fread(usb_state, 1, 20, usb_state_file) > 0)
        {
            if (!strncmp(usb_state, "DISCONNECTED", 11))
            {
                state = usb_otg_disconnected;
            }
            else if (!strncmp(usb_state, "CONNECTED", 8))
            {
                state = usb_otg_connected;
            }
            else if (!strncmp(usb_state, "CONFIGURED", 10))
            {
                state = usb_otg_configured;
            }
        }
        else
        {
            log_e("can't read usb state file.");
        }
        fclose(usb_state_file);
    }
    else
    {
        log_e("can't open usb state file.");
    }
    return state;
}

static void *usb_event_thread_handle(void *arg)
{
    int ret = 0;
    int fd;
    fd_set fds;
    usb_otg_state_e otg_state;
    struct udev *uac_udev;
    struct udev_monitor *mon;
    usb_otg_event_p pusb_otg_event = (usb_otg_event_p)arg;

    ret |= system("usbdevice.sh stop");
    ret |= system("usbdevice.sh config");
    ret |= system("usbdevice.sh start");
    if (ret)
    {
        log_i("usbdevice.sh start failed.");
    }
    sleep(2);

    uac_udev = udev_new();
    if (!uac_udev)
    {
        log_e("udev new err.");
        return NULL;
    }

    mon = udev_monitor_new_from_netlink(uac_udev, "udev");
    if (!mon)
    {
        log_e("Failed to create udev monitor.");
        udev_unref(uac_udev);
        return NULL;
    }
    udev_monitor_filter_add_match_subsystem_devtype(mon, "android_usb", NULL);
    udev_monitor_enable_receiving(mon);

    fd = udev_monitor_get_fd(mon);

    otg_state = get_usb_state();
    if (otg_state == usb_otg_configured)
    {
        usb_configured();
    }
    else
    {
        printf("usb is %d\n", otg_state);
    }
    FD_ZERO(&fds);
    FD_SET(fd, &fds);

#if defined(ANDROID_WINDOWS_VOL_SYNC)
    sleep(3);
    usb_disconnect_without_udc_restart();
    ret = system("usbdevice.sh stop;usbdevice.sh delete;usbdevice.sh config;usbdevice.sh start;");
    if (ret != 0)
        printf("reconnect usb for windows/mac volume failed");
#endif /* end #if defined(ANDROID_WINDOWS_VOL_SYNC) */

    for (;;)
    {
        if (select(fd + 1, &fds, NULL, NULL, NULL) > 0)
        {
            if (FD_ISSET(fd, &fds))
            {
                struct udev_device *dev = udev_monitor_receive_device(mon);
                if (dev)
                {
                    otg_state = usb_otg_unkown;
                    const char *usb_state = udev_device_get_property_value(dev, "USB_STATE");
                    if (!strcmp(usb_state, "DISCONNECTED"))
                    {
                        otg_state = usb_otg_disconnected;
                    }
                    else if (!strcmp(usb_state, "CONNECTED"))
                    {
                        otg_state = usb_otg_connected;
                    }
                    else if (!strcmp(usb_state, "CONFIGURED"))
                    {
                        otg_state = usb_otg_configured;
                    }
                    udev_device_unref(dev);

                    pthread_mutex_lock(&pusb_otg_event->mutex);
                    pusb_otg_event->otg_state = otg_state;
                    pthread_mutex_unlock(&pusb_otg_event->mutex);
                    if (sem_post(&pusb_otg_event->sem) == -1)
                        printf("post otg sem failed!\n");
                }
                else
                {
                    log_e("monitor udev event failed!");
                }
            }
        }
    }

    return NULL;
}

static int usb_event_proc_disabled;
void usb_event_proc_disable_flag_set(int dis)
{
    usb_event_proc_disabled = dis;
}

typedef enum
{
    UAC_HOST_IS_UNKNOWN,
    UAC_HOST_IS_WINDOWS,
    UAC_HOST_IS_MACOS
} UAC_HOST_OS_TYPE;
static UAC_HOST_OS_TYPE uac_host_os_type = UAC_HOST_IS_UNKNOWN;
static int usb_state_req_confirm(void)
{
    static const char state_log[] = "/tmp/usb_state_log";
    static const char state_script[] =
    {
        "rm /tmp/usb_state_log;\
        str1=$(dmesg | grep -i -A 2 'USB_STATE=CONNECTED' | tail -n 3);\
        echo ${str1} | grep 'bRequestType=22,bRequest=1,value=100' > /tmp/usb_state_log"
    };
    static const char mac_req_type[] = {"bRequestType=22,bRequest=1"};

    int is_req_0x22 = -1;

    sleep(3); // wait kernel print completed
    if (system(state_script) == 0)
    {
        int fd = open(state_log, O_RDONLY);
        if (fd >= 0)
        {
            char tmp[512];
            int ret = read(fd, tmp, 512);
            if (ret > sizeof(mac_req_type))
            {
                is_req_0x22 = 1;
            }
            else if (ret < 0)
            {
                printf("read %s failed\n", state_log);
            }
        }
        else
        {
            printf("open %s failed\n", state_log);
        }
    }
    return is_req_0x22;
}

static void *usb_event_proc_thread_handle(void *arg)
{
    usb_otg_state_e pre_state = usb_otg_unkown;
    usb_otg_state_e otg_state;
    usb_otg_event_p pusb_otg_event = (usb_otg_event_p)arg;
    usb_event_proc_disable_flag_set(0);

    for (;;)
    {
        if (sem_wait(&pusb_otg_event->sem) != 0)
            printf("wait otg sem failed!\n");
        pthread_mutex_lock(&pusb_otg_event->mutex);
        otg_state = pusb_otg_event->otg_state;
        pthread_mutex_unlock(&pusb_otg_event->mutex);
        if (usb_event_proc_disabled)
            continue;
        if (otg_state == pre_state)
            continue;
        else
            pre_state = otg_state;
        switch (otg_state)
        {
        case usb_otg_connected:
            log_i("USB CONNECTED.");
            break;
        case usb_otg_disconnected:
            log_i("USB DISCONNECTED.");
            usb_disconnected();
            sleep(3); // delay 3s in case of usb connect immediately
            break;
        case usb_otg_configured:
            log_i("USB CONFIGURED.");
            usb_configured();
            if (usb_state_req_confirm() < 1)
            {
                uac_host_os_type = UAC_HOST_IS_WINDOWS;
                log_i("assume uac host Windows");
#if defined(ANDROID_WINDOWS_VOL_SYNC)
                uac_capture_vol_set(uac_vol_int2per(usb_in_volume, 0));
#endif /* end ANDROID_WINDOWS_VOL_SYNC */
            }
            else
            {
                uac_host_os_type = UAC_HOST_IS_MACOS;
                log_i("assume uac host macOS");
#if defined(ANDROID_WINDOWS_VOL_SYNC)
                uac_capture_vol_set(uac_vol_int2per(usb_in_volume, 1));
#endif /* end ANDROID_WINDOWS_VOL_SYNC */
            }
            break;
        default:
            break;
        }
    }
    return NULL;
}

struct uac_event_def
{
    struct udev *uac_udev;
    struct udev_monitor *mon;
    int fd;
    struct udev_device *dev;
};

#if defined(ANDROID_WINDOWS_VOL_SYNC)
static const float winos_vol_db2per_table[] = // windows
{
    0, // 100%
    -0.1523,
    -0.3047,
    -0.4609,
    -0.6172,
    -0.7773,
    -0.9375,
    -1.0977,
    -1.2617,
    -1.4297,
    -1.5977,
    -1.7656,
    -1.9375,
    -2.1133,
    -2.2891,
    -2.4648,
    -2.6445,
    -2.8242,
    -3.0117,
    -3.1953,
    -3.3828,
    -3.5742,
    -3.7695,
    -3.9648,
    -4.1641,
    -4.3633,
    -4.5664,
    -4.7734,
    -4.9844,
    -5.1953,
    -5.4102,
    -5.6289,
    -5.8516,
    -6.0742,
    -6.3047,
    -6.5352,
    -6.7695,
    -7.0078,
    -7.25,
    -7.4961,
    -7.75,
    -8.0039,
    -8.2617,
    -8.5273,
    -8.793,
    -9.0664,
    -9.3477,
    -9.6289,
    -9.918,
    -10.2109,
    -10.5117,
    -10.8203,
    -11.1328,
    -11.4492,
    -11.7773,
    -12.1094,
    -12.4492,
    -12.7969,
    -13.1563,
    -13.5195,
    -13.8945,
    -14.2773,
    -14.6719,
    -15.0742,
    -15.4883,
    -15.918,
    -16.3555,
    -16.8086,
    -17.2734,
    -17.7539,
    -18.25,
    -18.7617,
    -19.293,
    -19.8438,
    -20.4141,
    -21.0078,
    -21.625,
    -22.2656,
    -22.9375,
    -23.6406,
    -24.3789,
    -25.1523,
    -25.9688,
    -26.8281,
    -27.7422,
    -28.7148,
    -29.7539,
    -30.8711,
    -32.0742,
    -33.3789,
    -34.8086,
    -36.3867,
    -38.1484,
    -40.1406,
    -42.4336,
    -45.1367,
    -48.4258,
    -52.6289,
    -58.4609,
    -68.0391, // 1%
    -95.0600, // 0.1%
    -99.9961, // 0%
};

static const float macos_vol_db2per_table[] = // macOS
{
    0.00, // 100%
    -0.50,
    -1.00,
    -1.50,
    -2.00,
    -2.50,
    -3.00,
    -3.50,
    -4.00,
    -4.50,
    -5.00,
    -5.50,
    -6.00,
    -6.50,
    -7.00,
    -8.00,
    -8.50,
    -9.00,
    -9.50,
    -10.00,
    -10.50,
    -11.00,
    -11.50,
    -12.00,
    -13.00,
    -13.50,
    -14.00,
    -14.50,
    -15.00,
    -16.00,
    -16.50,
    -17.00,
    -17.50,
    -18.00,
    -19.00,
    -19.50,
    -20.00,
    -20.50,
    -21.00,
    -22.00,
    -22.50,
    -23.00,
    -24.00,
    -24.50,
    -25.00,
    -26.00,
    -26.50,
    -27.00,
    -28.00,
    -28.50,
    -29.00,
    -30.00,
    -31.00,
    -31.50,
    -32.00,
    -33.00,
    -34.00,
    -34.50,
    -35.00,
    -36.00,
    -37.00,
    -37.50,
    -38.00,
    -39.00,
    -40.00,
    -41.00,
    -42.00,
    -42.50,
    -43.00,
    -44.00,
    -45.00,
    -46.00,
    -47.00,
    -48.00,
    -49.00,
    -50.00,
    -51.00,
    -52.00,
    -53.00,
    -54.00,
    -55.00,
    -56.00,
    -57.00,
    -59.00,
    -60.00,
    -61.00,
    -63.00,
    -64.00,
    -65.00,
    -67.00,
    -69.00,
    -70.00,
    -72.00,
    -74.00,
    -75.00,
    -77.00,
    -79.00,
    -84.00,
    -86.00,
    -92.00, // 1%
    -96.00, // 0.5%
    -100.00, // 0%
};

static int uac_db2percentage_by_table(short db, const float tab[], int ele)
{
    float dbval = db / 256.0f - 0.05;
    int per = 0;
    for (int i = 0; ele > i; i++)
    {
        if (dbval < tab[i])
        {
            if (100 >= i) // 100% ~ 0.1%
                per = 100 - i;
            else // 0%
                per = 0;
        }
        else
        {
            break;
        }
    }
    return per;
}

static int uac_db2percentage(short db)
{
    if (db == 0)
        return 100;

    int os_type = (int)((int)db * 1000 / 256.0f);
    if (((os_type % 100) == 0) || (uac_host_os_type == UAC_HOST_IS_MACOS))
    {
        return uac_db2percentage_by_table(db, macos_vol_db2per_table, sizeof(macos_vol_db2per_table) / sizeof(macos_vol_db2per_table[0]));
    }
    else
    {
        return uac_db2percentage_by_table(db, winos_vol_db2per_table, sizeof(winos_vol_db2per_table) / sizeof(winos_vol_db2per_table[0]));
    }
}

int uac_vol_int2per(int db, int is_macos)
{
    if (db == 0)
        return 100;
    if (is_macos != 0)
    {
        return uac_db2percentage_by_table(db, macos_vol_db2per_table, sizeof(macos_vol_db2per_table) / sizeof(macos_vol_db2per_table[0]));
    }
    else
    {
        return uac_db2percentage_by_table(db, winos_vol_db2per_table, sizeof(winos_vol_db2per_table) / sizeof(winos_vol_db2per_table[0]));
    }
}

void uac_host_os_set(int is_macos)
{
    if (is_macos != 0)
        uac_host_os_type = UAC_HOST_IS_MACOS;
    else
        uac_host_os_type = UAC_HOST_IS_WINDOWS;
}
#endif /* end ANDROID_WINDOWS_VOL_SYNC */

static void *uac_event_thread_handle(void *arg)
{
    struct uac_event_def *p_uac_event = (struct uac_event_def *)arg;

    fd_set fds;
    database_value tmp;
    int volume;

#if defined(ANDROID_WINDOWS_VOL_SYNC)
    uac_playback_vol_set(uac_db2percentage((short)usb_out_volume));
    uac_capture_vol_set(uac_db2percentage((short)usb_in_volume));
#endif /* end ANDROID_WINDOWS_VOL_SYNC */

    for (;;)
    {
        FD_ZERO(&fds);
        FD_SET(p_uac_event->fd, &fds);

        if (select(p_uac_event->fd + 1, &fds, NULL, NULL, NULL) > 0)
        {
            if (FD_ISSET(p_uac_event->fd, &fds))
            {
                p_uac_event->dev = udev_monitor_receive_device(p_uac_event->mon);
                if (p_uac_event->dev)
                {
                    const char *usb_state = udev_device_get_property_value(p_uac_event->dev, "USB_STATE");
                    if (!strcmp(usb_state, "SET_AUDIO_CLK"))
                    {
                        tmp.i32 = atoi(udev_device_get_property_value(p_uac_event->dev, "PPM"));
                        database_write(usb_ppm_index, tmp);
                    }
                    else if (!strcmp(usb_state, "SET_MUTE"))
                    {
                        if ((!strcmp(udev_device_get_property_value(p_uac_event->dev, "STREAM_DIRECTION"), "IN")))
                        {
                            // IN usb out,mute usb out
                            usb_out_mute = (bool)atoi(udev_device_get_property_value(p_uac_event->dev, "MUTE"));
                        }
                        else
                        {
                            // OUT usb in
                            usb_in_mute = (bool)atoi(udev_device_get_property_value(p_uac_event->dev, "MUTE"));
                        }
                    }
                    else if (!strcmp(usb_state, "SET_VOLUME"))
                    {
                        const char *stream_direction = udev_device_get_property_value(p_uac_event->dev, "STREAM_DIRECTION");
                        const char *volume_str = udev_device_get_property_value(p_uac_event->dev, "VOLUME");

                        if (stream_direction && !strcmp(stream_direction, "IN"))
                        {
                            if (volume_str)
                            {
                                char *endPtr;
                                volume = (short)strtol(volume_str, &endPtr, 16);

                                // 检查转换后的字符串是否为有效的16进制数
                                if (*endPtr != '\0' && *endPtr != '\n')
                                {
                                    volume = 0;
                                }

                                usb_out_volume = volume;
#if defined(ANDROID_WINDOWS_VOL_SYNC)
                                uac_playback_vol_set(uac_db2percentage(volume));
#endif /* end ANDROID_WINDOWS_VOL_SYNC */
                                tmp.f32 = pow(10, ((double)volume / 256.0) / 20.0);

                                if (!isnan(tmp.f32) && !isinf(tmp.f32))
                                {
                                    usb_out_mag = tmp.f32;
                                }
                            }
                        }
                        else if (stream_direction && !strcmp(stream_direction, "OUT"))
                        {
                            if (volume_str)
                            {
                                char *endPtr;
                                volume = (short)strtol(volume_str, &endPtr, 16);

                                // 检查转换后的字符串是否为有效的16进制数
                                if (*endPtr != '\0' && *endPtr != '\n')
                                {
                                    volume = 0;
                                }

                                usb_in_volume = volume;
#if defined(ANDROID_WINDOWS_VOL_SYNC)
                                uac_capture_vol_set(uac_db2percentage(volume));
#endif /* end ANDROID_WINDOWS_VOL_SYNC */
                                tmp.f32 = pow(10, (double)(volume / 256) / 20.0);

                                if (!isnan(tmp.f32) && !isinf(tmp.f32))
                                {
                                    usb_in_mag = tmp.f32;
                                }
                            }
                        }
                    }
                    udev_device_unref(p_uac_event->dev);
                    p_uac_event->dev = NULL;
                }
            }
        }
    }

    return NULL;
}

static struct uac_event_def _uac_event;
static void *uac_event_handle;

static void uac_event_configured(void)
{
    _uac_event.uac_udev = udev_new();
    if (!_uac_event.uac_udev)
    {
        log_e("udev new err.");
        return;
    }

    _uac_event.mon = udev_monitor_new_from_netlink(_uac_event.uac_udev, "udev");
    if (!_uac_event.mon)
    {
        log_e("Failed to create udev monitor.");
        udev_unref(_uac_event.uac_udev);
        _uac_event.uac_udev = NULL;
        return;
    }
    udev_monitor_filter_add_match_subsystem_devtype(_uac_event.mon, "u_audio", NULL);
    udev_monitor_enable_receiving(_uac_event.mon);

    _uac_event.fd = udev_monitor_get_fd(_uac_event.mon);

    uac_event_handle = ym_thread_create("uac_event",
                                        default_thread_cpuid,
                                        default_thread_prio,
                                        uac_event_thread_handle,
                                        &_uac_event);
    if (uac_event_handle)
    {
        ym_thread_run(uac_event_handle);
    }
    else
    {
        log_e("can't create uac event thread.");
        udev_monitor_unref(_uac_event.mon);
        udev_unref(_uac_event.uac_udev);
        memset(&_uac_event, 0, sizeof(struct uac_event_def));
    }
}

static void uac_event_disconnected(void)
{
    if (uac_event_handle)
    {
        ym_thread_destroy(uac_event_handle);
        if (_uac_event.dev)
        {
            log_i("kill uac event thread.");
            udev_device_unref(_uac_event.dev);
        }
        udev_monitor_unref(_uac_event.mon);
        udev_unref(_uac_event.uac_udev);
        memset(&_uac_event, 0, sizeof(struct uac_event_def));
        uac_event_handle = 0;
    }
}

static int usb_event_init(void)
{
    usb_otg_event.otg_state = usb_otg_unkown;
    int err = pthread_mutex_init(&usb_otg_event.mutex, NULL);
    if (err != 0)
        printf("Init usb_otg_event.mutex failed!");

    err = sem_init(&usb_otg_event.sem, 0, 0);
    if (err != 0)
        printf("Init usb_otg_event.sem failed!");

    ym_thread_create("usb_event",
                     default_thread_cpuid,
                     usb_event_thread_prio,
                     usb_event_thread_handle,
                     &usb_otg_event);

    ym_thread_create("usb_event_proc",
                     default_thread_cpuid,
                     usb_event_thread_prio,
                     usb_event_proc_thread_handle,
                     &usb_otg_event);

    return 0;
}
INIT_APP_EXPORT(usb_event_init);
