/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-07-10 15:20:09
 * @LastEditTime: 2024-07-11 15:19:34
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @Description:
 * @FilePath: /audio_mixer_rk3308/platform/pcba_test/pcba_gpio_list.h
 * Copyright (c) 2006-2024, Yinkman Development Team
 */

#ifndef _PCBA_GPIO_LIST_H_
#define _PCBA_GPIO_LIST_H_

#include <gpiod.h>

typedef struct _gpio_dest
{
    int chip_left;
    int line_left;

    int chip_right;
    int line_right;

    const char *name_left;
    const char *name_right;

    struct _gpio_dest *next, *prev;

} gpio_dest_t, *gpio_dest_p;

struct gpioname_def
{
    const char *l;
    const char *r;
};

extern gpio_dest_p gpio_dest_list;

#endif // !_PCBA_GPIO_LIST_H_
