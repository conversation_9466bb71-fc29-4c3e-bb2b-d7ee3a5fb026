#include <stdint.h>

#include "config.h"
#include "ym_audio_mixer.h"
#include "ym_log.h"

static int mixer_commponent_init(void)
{
    {
        // master stream init
        // es7210
        INIT_MASTER_MIXER_STREAM_IN(beam_out, 2);
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src0, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src1, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src2, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src3, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src4, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src5, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src6, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src7, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src8, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src9, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src10, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src11, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src12, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src13, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src14, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src15, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src16, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src17, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src18, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src19, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src20, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src21, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src22, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src23, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src24, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src25, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src26, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src27, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src28, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src29, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src30, 1)
        // INIT_MASTER_MIXER_STREAM_IN(es7210_src31, 1)

        // es7149
        INIT_MASTER_MIXER_STREAM_OUT(line_out0, 1, 0, es7149_sink0);
        INIT_MASTER_MIXER_STREAM_OUT(line_out1, 1, 0, es7149_sink1);
    }
    {
        INIT_SLAVE_MIXER_STREAM_IN(beam2_out, 2);
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src0, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src1, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src2, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src3, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src4, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src5, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src6, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src7, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src8, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src9, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src10, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src11, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src12, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src13, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src14, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src15, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src16, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src17, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src18, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src19, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src20, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src21, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src22, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src23, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src24, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src25, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src26, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src27, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src28, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src29, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src30, 1)
        // INIT_SLAVE_MIXER_STREAM_IN(es7210_src31, 1)
    }

    log_i("mixer_commponent_init successful");
    return 0;
}
INIT_COMPONENT_EXPORT(mixer_commponent_init);

void aec_afc_set_tune_mode(AEC_AFC_TUNE_MODE mode) {}

uint8_t *get_mic_input_index(uint32_t *micNum)
{
    static uint8_t tmp;
    return &tmp;
}

void platform_lib_param_default(void) {}

void platform_mode1_lib_param_default(void) {}