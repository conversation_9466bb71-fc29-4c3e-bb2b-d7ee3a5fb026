#ifndef _CONFIG_H_
#define _CONFIG_H_

#define BD_MIXER_MATRIX_MIC

#define YM_BACKEND_RK3308_FLOAT

// 长按tune key进入loader模式时间ms
#define TUNE_KEY_UPGRADE_TIMEOUT (10000)

#define ETH_PROTOCOL_PORT (2000)

#define FRAMES (256)
#define FORMAT (32)
#define RATE (48000)

#define FRAMES_UNIT_US ((1000000.0f / (RATE)) * (FRAMES))

#define BEAM_ENABLE

#ifdef BEAM_ENABLE
#define BEAM_INSTANCE_NUMBER (2)

#define BEAM_IN_CHANNELS (32)
#define BEAM_OUT_CHANNELS (2)

#define BEAM_PARAM_SHADOW_MAX (12)
#define BEAM_PARAM_PRIOR_MAX (5)
#define BEAM_SPK_SHADOW_START (4)
#define BEAM_SPK_SHADOW_END (BEAM_PARAM_SHADOW_MAX - 1)

#define BEAM_HDEGREE_MIN (-90)
#define BEAM_HDEGREE_MAX (450)
#define BEAM_VDEGREE_MIN (0)
#define BEAM_VDEGREE_MAX (90)

#define STREAM_OUT_BEAM_NAME "beam_out"
#define STREAM_OUT_BEAM2_NAME "beam2_out"
#endif

#define YM_MATRIX_FRAME_DB_QUANT (2)


#define MIXER_PARAM_PEQ_MAX (8)
#define MIXER_PARAM_GEQ_MAX (32)
#define MIXER_PARAM_MODE_NAME_MAX (8)
#define MIXER_PARAM_MODE_NAME_LEN (16)

#define MIXER_INPUT_CHN_MAX (2)
// #define AUTOMIXER_MIC_CHNS (8)   // mic
// #define AUTOMIXER_IAUX_CHNS (0)  // line in
// #define AUTOMIXER_WMIC_CHNS (0)  // 无线mic
// #define AUTOMIXER_SMIC_CHNS (0)  // 从机
#define AUTOMIXER_IREC_CHNS (2)  // usb
// #define AUTOMIXER_SLAVE_CHNS (0) // 从核
// #define AUTOMIXER_SLAVE_CHN1_INDEX (MIXER_INPUT_CHN_MAX - 2)
// #define AUTOMIXER_SLAVE_CHN2_INDEX (MIXER_INPUT_CHN_MAX - 1)

#define MIXER_OUTPUT_CHN_MAX (2)
#define AUTOMIXER_SPK_CHNS (0)  // spk
#define AUTOMIXER_HP_CHNS (0)   // hp
#define AUTOMIXER_OREC_CHNS (2) // orec

// #define HARDWARE_MIC_GAIN_OFFSET (0)
// #define HARDWARE_LINE_IN_GAIN_OFFSET (0)


// #define WATCHDOG_DISABLE


#endif /* _CONFIG_H_ */
