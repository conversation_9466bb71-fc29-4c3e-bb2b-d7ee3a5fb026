/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-06-16 16:12:50
 * @LastEditTime: 2024-11-22 12:29:21
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @Description:
 * @FilePath: /audio_mixer_rk3308/platform/wrong_board_id/config.h
 * Copyright (c) 2006-2024, Yinkman Development Team
 */
#ifndef __APP_CONFIG__
#define __APP_CONFIG__

#define YM_BACKEND_RK3308_FLOAT

// 长按tune key进入loader模式时间ms
#define TUNE_KEY_UPGRADE_TIMEOUT (10000)

#define ETH_PROTOCOL_PORT (2000)


#define FRAMES (256)
#define FORMAT (32)
#define RATE (48000)
#define FRAMES_COUNT (2)
#define AUDIO_OUT_FRAMES (32)
#define DUMMY_OUT_DELAY (48)

#define FRAMES_UNIT_US ((1000000.0f / (RATE)) * (FRAMES))

#define CACODEC_CARD (0) // yinkman_tx2rx8
#define CES7210_CARD (1) // yinkman_rx4

#define PACODEC_CARD (0) // yinkman_tx2rx8

#define ACODEC_MIXER (0)

#define ES7210_MIXER (1)

#define PDUMMY3_CARD (2)

// #define UAC_CARD_INFO_NAME ("Yinkman UAC1")

// #define APP_STACK_SIZE (64 * 1024)
// #define APP_PRIORITY (96)

// #define BOARD_TYPE_TRONGLONG_EVM_BOARD (0)
// #define BOARD_TYPE_YINYI_CORE_BOARD (1)

// /* according to Clock.tickPeriod at the *.cfg file */
// #define TASK_SLEEP_MS(ms) ((ms) * 10)
// #define TASK_Nx100US(us) (us)

// // #define ARM_USED_UART

// /* board info */
// #define FIRMWARE_VERSION_MAIN (0x01)
// #define FIRMWARE_VERSION_SUBV (0x07)
// #define FIRMWARE_VERSION_PATCH (0x00)
// #define FIRMWARE_VERSION ((FIRMWARE_VERSION_MAIN << 16) | (FIRMWARE_VERSION_SUBV << 8) | FIRMWARE_VERSION_PATCH)

// #define BOARD_VERSION_V10 (0x10)
// #define BOARD_VERSION_V20 (0x20)
// #define BOARD_VERSION_V21 (0x21)
// #define BOARD_VERSION_V22 (0x22)
// #define BOARD_VERSION_V23 (0x23)
// #define BOARD_VERSION_USED BOARD_VERSION_V21
// #define BOARD_ID_MIC_MATRIX (0x59590000 | BOARD_VERSION_USED)

// #define FIRMWARE_FLASH_BASE_ADDR (1 * 1024 * 1024)
// #define FIRMWARE_FLASH_MAX_SIZE (3 * 1024 * 1024)
// #define BACKUP_FLASH_BASE_ADDR (FIRMWARE_FLASH_BASE_ADDR + FIRMWARE_FLASH_MAX_SIZE)
// #define BACKUP_FLASH_MAX_SIZE FIRMWARE_FLASH_MAX_SIZE

// #define BOARD_PARAM_FLASH_BASE_ADDR (7 * 1024 * 1024)
// #define BOARD_PARAM_FLASH_SIZE (4 * 1024)
// #define MIXER_PARAM_FLASH_BASE_ADDR (BOARD_PARAM_FLASH_BASE_ADDR + BOARD_PARAM_FLASH_SIZE)
// #define MIXER_PARAM_FLASH_SIZE (4 * 1024)
// /* board_param + mixer_param + 8 x mixer_mode_param = 10 */
// #define SECURE_PARAM_FLASH_BASE_ADDR (BOARD_PARAM_FLASH_BASE_ADDR + BOARD_PARAM_FLASH_SIZE * 10)
// #define SECURE_PARAM_FLASH_SIZE (4 * 1024)
// /* board_param + mixer_param + 8 x mixer_mode_param + secure_param = 11 */
// #define BARCODE_FLASH_BASE_ADDR (SECURE_PARAM_FLASH_BASE_ADDR + SECURE_PARAM_FLASH_SIZE)
// #define BARCODE_FLASH_SIZE (4 * 1024)



// #define BARCODE_PARAM_MAX_CNT (10)

// #define BEAM_IN_CHANNELS (6)

// #define BEAM_OUT_CHANNELS (2)

#define YM_MATRIX_FRAME_DB_QUANT (2)

// #define BEAMF_PARAM_SHADOW_MAX (12)
// #define BEAMF_PARAM_PRIOR_MAX (5)
// #define BEAMF_SPK_SHADOW_START (4)
// #define BEAMF_SPK_SHADOW_END (BEAMF_PARAM_SHADOW_MAX - 1)

// #define BEAMF_HDEGREE_MIN (-90)
// #define BEAMF_HDEGREE_MAX (450)
// #define BEAMF_VDEGREE_MIN (0)
// #define BEAMF_VDEGREE_MAX (90)

// #define BEAMF_BEAM_CHN_MAX (16)

// #define BEAMF_PARAM_INSTALL_MODES (4)
// #define BEAMF_INSTALL_MODE_EMBEDDED_ROOF (0)
// #define BEAMF_INSTALL_MODE_HANGUP_ROOF (1)
// #define BEAMF_INSTALL_MODE_ON_SHELF (2)
// #define BEAMF_INSTALL_MODE_HANGUP_WALL (3)

// #define BEAMF_MIC_GAIN_MODIFY_ENABLE (1)

#define MIXER_PARAM_PEQ_MAX (8)
#define MIXER_PARAM_GEQ_MAX (32)
#define MIXER_PARAM_MODE_NAME_MAX (8)
#define MIXER_PARAM_MODE_NAME_LEN (16)

#define MIXER_INPUT_CHN_MAX (10)
#define AUTOMIXER_MIC_CHNS (8)   // mic
#define AUTOMIXER_IAUX_CHNS (0)  // line in
#define AUTOMIXER_WMIC_CHNS (0)  // 无线mic
#define AUTOMIXER_SMIC_CHNS (0)  // 从机
#define AUTOMIXER_IREC_CHNS (2)  // usb
#define AUTOMIXER_SLAVE_CHNS (0) // 从核
#define AUTOMIXER_SLAVE_CHN1_INDEX (MIXER_INPUT_CHN_MAX - 2)
#define AUTOMIXER_SLAVE_CHN2_INDEX (MIXER_INPUT_CHN_MAX - 1)

#define MIXER_OUTPUT_CHN_MAX (2)
#define AUTOMIXER_SPK_CHNS (0)  // spk
#define AUTOMIXER_HP_CHNS (0)   // hp
#define AUTOMIXER_OREC_CHNS (2) // orec

#define HARDWARE_MIC_GAIN_OFFSET (0)
#define HARDWARE_LINE_IN_GAIN_OFFSET (0)

#define USB_HOST_PLUG_MAX (0)

// #define PCBA_TEST

#define SYSTEM_NO_AUDIO_STREAM

#endif
