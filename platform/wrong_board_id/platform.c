/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-07-10 14:17:05
 * @LastEditTime: 2024-11-22 12:28:56
 * @LastEditors: An<PERSON><PERSON><PERSON>
 * @Description:
 * @FilePath: /audio_mixer_rk3308/platform/wrong_board_id/platform.c
 * Copyright (c) 2006-2024, Yinkman Development Team
 */

#include "ym_audio_mixer.h"

static struct gpiod_line *led_line;

void aec_afc_set_tune_mode(AEC_AFC_TUNE_MODE mode) {}

uint8_t *get_mic_input_index(uint32_t *micNum)
{
    static uint8_t tmp;
    return &tmp;
}

void platform_lib_param_default(void) {}

void platform_mode1_lib_param_default(void) {}

struct gpiod_chip *gpio_chip = NULL;

static int gpio_test_comp_init(void)
{

    gpio_chip = gpiod_chip_open_by_number(0);
    if (!gpio_chip)
    {
        log_e("open gpio chip 0 failed.");
        return -1;
    }

    led_line = gpiod_chip_get_line(gpio_chip, 20);
    if (led_line)
    {
        gpiod_line_request_output(led_line, "led", 0);
    }

    return 0;
}
INIT_COMPONENT_EXPORT(gpio_test_comp_init);

static int audio_in_stream_init(void)
{
    yinkman_stream_create("acodec_stream", FRAMES, 8 * (FORMAT) / 8, 2);
    return 0;
}
INIT_STREAM_EXPORT(audio_in_stream_init);

static int mixer_commponent_init(void)
{
    {
        INIT_MASTER_MIXER_STREAM_IN(acodec_src0, 1)
        INIT_MASTER_MIXER_STREAM_IN(acodec_src1, 1)
        INIT_MASTER_MIXER_STREAM_IN(acodec_src2, 1)
        INIT_MASTER_MIXER_STREAM_IN(acodec_src3, 1)
        INIT_MASTER_MIXER_STREAM_IN(acodec_src4, 1)
        INIT_MASTER_MIXER_STREAM_IN(acodec_src5, 1)
        INIT_MASTER_MIXER_STREAM_IN(acodec_src6, 1)
        INIT_MASTER_MIXER_STREAM_IN(acodec_src7, 1)

        INIT_MASTER_MIXER_STREAM_OUT(usb, 2, 1, usb_out)
    }

    {
        INIT_SLAVE_MIXER_STREAM_IN(acodec_src0, 1)
        INIT_SLAVE_MIXER_STREAM_IN(acodec_src1, 1)
        INIT_SLAVE_MIXER_STREAM_IN(acodec_src2, 1)
        INIT_SLAVE_MIXER_STREAM_IN(acodec_src3, 1)
        INIT_SLAVE_MIXER_STREAM_IN(acodec_src4, 1)
        INIT_SLAVE_MIXER_STREAM_IN(acodec_src5, 1)
        INIT_SLAVE_MIXER_STREAM_IN(acodec_src6, 1)
        INIT_SLAVE_MIXER_STREAM_IN(acodec_src7, 1)
    }

    return 0;
}
INIT_COMPONENT_EXPORT(mixer_commponent_init);

static void *wrong_board_id_thread_handle(void *arg)
{
    uint32_t count = 0;
    while (1)
    {
        usleep(100 * 1000);
        if (count % 50 == 0)
            log_i("WRONG BOARD ID,PLEASE CHECK BOARD ID RESISTOR");
        led_toggle();
        count++;
    }
    return NULL;
}

static int wrong_board_id_app_init(void)
{
    ym_thread_create("wrong_board",
                     default_thread_cpuid,
                     default_thread_prio,
                     wrong_board_id_thread_handle,
                     NULL);
    return 0;
}
INIT_APP_EXPORT(wrong_board_id_app_init);

// 设置输出引脚电平
static void set_gpio_value(struct gpiod_line *line, int value)
{
    if (gpiod_line_set_value(line, value) < 0)
    {
        perror("Set line output failed");
        return;
    }
}

// void led_toggle(void)
// {
//     static int led_state = 0;
//     led_state = !led_state;
//     if (led_line)
//         set_gpio_value(led_line, led_state);
// }
