/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-07-10 14:17:05
 * @LastEditTime: 2024-11-22 12:28:40
 * @LastEditors: Anzhi<PERSON><PERSON>
 * @Description:
 * @FilePath: /audio_mixer_rk3308/platform/pcba_test/platform.c
 * Copyright (c) 2006-2024, Yinkman Development Team
 */

#include "ym_audio_mixer.h"
#include "pcba_gpio_list.h"

gpio_dest_p gpio_dest_list = NULL;

static struct gpiod_line *led_line, *state_line;

static pthread_mutex_t gpio_test_mutex = PTHREAD_MUTEX_INITIALIZER;

static int gpio_test_func(struct gpiod_chip *chip_out, struct gpiod_chip *chip_in, int LINE_OUT, int LINE_IN);

void aec_afc_set_tune_mode(AEC_AFC_TUNE_MODE mode) {}

uint8_t *get_mic_input_index(uint32_t *micNum)
{
    static uint8_t tmp;
    return &tmp;
}

void platform_lib_param_default(void) {}

void platform_mode1_lib_param_default(void) {}

static int audio_in_stream_init(void)
{
    yinkman_stream_create("acodec_stream", FRAMES, 8 * (FORMAT) / 8, 2);
    return 0;
}
INIT_STREAM_EXPORT(audio_in_stream_init);

static int mixer_commponent_init(void)
{
    {
        INIT_MASTER_MIXER_STREAM_IN(acodec_src0, 1)
        INIT_MASTER_MIXER_STREAM_IN(acodec_src1, 1)
        INIT_MASTER_MIXER_STREAM_IN(acodec_src2, 1)
        INIT_MASTER_MIXER_STREAM_IN(acodec_src3, 1)
        INIT_MASTER_MIXER_STREAM_IN(acodec_src4, 1)
        INIT_MASTER_MIXER_STREAM_IN(acodec_src5, 1)
        INIT_MASTER_MIXER_STREAM_IN(acodec_src6, 1)
        INIT_MASTER_MIXER_STREAM_IN(acodec_src7, 1)

        INIT_MASTER_MIXER_STREAM_OUT(usb, 2, 1, usb_out)
    }

    {
        INIT_SLAVE_MIXER_STREAM_IN(acodec_src0, 1)
        INIT_SLAVE_MIXER_STREAM_IN(acodec_src1, 1)
        INIT_SLAVE_MIXER_STREAM_IN(acodec_src2, 1)
        INIT_SLAVE_MIXER_STREAM_IN(acodec_src3, 1)
        INIT_SLAVE_MIXER_STREAM_IN(acodec_src4, 1)
        INIT_SLAVE_MIXER_STREAM_IN(acodec_src5, 1)
        INIT_SLAVE_MIXER_STREAM_IN(acodec_src6, 1)
        INIT_SLAVE_MIXER_STREAM_IN(acodec_src7, 1)
    }

    return 0;
}
INIT_COMPONENT_EXPORT(mixer_commponent_init);

const struct gpioname_def gpio_name[] = {
    {"GPIO1_C5", "GPIO1_C2"},
    {"GPIO1_C3", "GPIO1_C0"},
    {"GPIO1_C4", "GPIO1_B5"},
    {"GPIO1_D1", "GPIO1_D0"},
    {"GPIO2_A4", "GPIO2_A2"},
    {"GPIO2_A3", "GPIO2_A5"},
    {"GPIO2_B1", "GPIO2_A6"},
    {"GPIO2_B7", "GPIO2_B6"},
    {"GPIO2_B3", "GPIO2_A1"},
    {"GPIO1_C1", "GPIO1_B4"},
    {"GPIO1_B7", "GPIO1_B6"},
    {"GPIO1_A2", "GPIO1_A6"},
    {"GPIO1_A5", "GPIO1_B1"},
    {"GPIO1_B2", "GPIO1_B3"},
    {"GPIO1_A3", "GPIO1_A0"},
    {"GPIO1_B0", "GPIO1_A7"},
    {"GPIO2_B5", "GPIO2_C0"},
    {"GPIO2_B2", "GPIO2_B0"},
    {"GPIO2_A7", "GPIO2_B4"},
    {"GPIO0_C2", "GPIO0_B1"},
    {"GPIO0_B6", "GPIO0_C3"},
    {"GPIO0_C1", "GPIO0_A1"},
    {"GPIO0_A2", "GPIO0_A6"},
    {"GPIO0_B7", "GPIO0_C0"},
    {"GPIO4_B5", "GPIO4_A4"},
    {"GPIO4_A2", "GPIO4_A0"},
    {"GPIO4_A5", "GPIO4_A6"},
    {"GPIO4_C0", "GPIO4_B7"},
    {"GPIO4_B4", "GPIO4_B3"},
    {"GPIO4_A3", "GPIO4_B0"},
    {"GPIO4_B1", "GPIO4_B2"},
    {NULL, NULL},
};

// const struct gpioname_def gpio_name[] = {
//     {"GPIO1_C5", "GPIO1_C2"},
//     {"GPIO1_C3", "GPIO1_C0"},
//     {"GPIO1_C4", "GPIO1_B5"},
//     {"GPIO1_C7", "GPIO1_A1"},
//     {"GPIO1_D1", "GPIO1_D0"},
//     {"GPIO2_A0", "GPIO1_A4"},
//     {"GPIO2_A4", "GPIO2_A2"},
//     {"GPIO2_A3", "GPIO2_A5"},
//     {"GPIO2_B1", "GPIO2_A6"},
//     {"GPIO2_B7", "GPIO2_B6"},
//     {"GPIO2_B3", "GPIO2_A1"},
//     {"GPIO1_C1", "GPIO1_B4"},
//     {"GPIO1_B7", "GPIO1_B6"},
//     {"GPIO1_A2", "GPIO1_A6"},
//     {"GPIO1_A5", "GPIO1_B1"},
//     {"GPIO1_B2", "GPIO1_B3"},
//     {"GPIO1_A3", "GPIO1_A0"},
//     {"GPIO1_B0", "GPIO1_A7"},
//     {"GPIO1_C6", "GPIO3_B5"},
//     {"GPIO3_B2", "GPIO3_B4"},
//     {"GPIO2_B5", "GPIO2_C0"},
//     {"GPIO2_B2", "GPIO2_B0"},
//     {"GPIO2_A7", "GPIO2_B4"},
//     {"GPIO0_C2", "GPIO0_B1"},
//     {"GPIO0_B6", "GPIO0_C3"},
//     {"GPIO0_C1", "GPIO0_A1"},
//     {"GPIO0_A2", "GPIO0_A6"},
//     {"GPIO0_B7", "GPIO0_C0"},
//     {"GPIO4_D0", "GPIO4_B6"},
//     {"GPIO4_B5", "GPIO4_A4"},
//     {"GPIO4_A2", "GPIO4_A0"},
//     {"GPIO4_A5", "GPIO4_A6"},
//     {"GPIO4_A1", "GPIO0_A7"},
//     {"GPIO0_A3", "GPIO0_A5"},
//     {"GPIO0_B0", "GPIO4_D6"},
//     {"GPIO4_D3", "GPIO4_D2"},
//     {"GPIO4_D4", "GPIO4_D5"},
//     {"GPIO4_D1", "GPIO0_B2"},
//     {"GPIO4_C0", "GPIO4_B7"},
//     {"GPIO4_B4", "GPIO4_B3"},
//     {"GPIO4_A3", "GPIO4_B0"},
//     {"GPIO4_B1", "GPIO4_B2"},
//     {NULL, NULL},
// };

struct gpiod_chip *gpio_chip[5] = {NULL};

static int gpio_test_comp_init(void)
{
    gpio_dest_p pgpio_dest;

    for (int i = 0; i < 5; i++)
    {
        gpio_chip[i] = gpiod_chip_open_by_number(i);
        if (!gpio_chip[i])
        {
            log_e("open gpio chip %d failed.", i);
            return -1;
        }
    }

    led_line = gpiod_chip_get_line(gpio_chip[0], 20);
    if (led_line)
    {
        gpiod_line_request_output(led_line, "led", 0);
    }

    state_line = gpiod_chip_get_line(gpio_chip[4], 7);
    if (state_line)
    {
        gpiod_line_request_output(state_line, "status", 0);
    }

    for (int i = 0; gpio_name[i].l != NULL; i++)
    {
        pgpio_dest = (gpio_dest_p)malloc(sizeof(gpio_dest_t));
        memset(pgpio_dest, 0, sizeof(gpio_dest_t));
        pgpio_dest->chip_left = gpio_name[i].l[4] - '0';
        pgpio_dest->line_left = (gpio_name[i].l[6] - 'A') * 8 + (gpio_name[i].l[7] - '0');
        pgpio_dest->chip_right = gpio_name[i].r[4] - '0';
        pgpio_dest->line_right = (gpio_name[i].r[6] - 'A') * 8 + (gpio_name[i].r[7] - '0');
        pgpio_dest->name_left = gpio_name[i].l;
        pgpio_dest->name_right = gpio_name[i].r;
        DL_APPEND(gpio_dest_list, pgpio_dest);
        log_i("gpio %-3d %-3d %s %-3d %-3d %s ", pgpio_dest->chip_left, pgpio_dest->line_left, pgpio_dest->name_left,
              pgpio_dest->chip_right, pgpio_dest->line_right, pgpio_dest->name_right);
    }

    return 0;
}
INIT_COMPONENT_EXPORT(gpio_test_comp_init);
static int gpio_test_state = 0;
static int gpio_test_cmd(int argc, char **argv)
{
    gpio_dest_p pgpio_dest;
    int ret = 0;
    gpio_test_state = 0;
    pthread_mutex_lock(&gpio_test_mutex);
    DL_FOREACH(gpio_dest_list, pgpio_dest)
    {
        ret = gpio_test_func(gpio_chip[pgpio_dest->chip_left],
                             gpio_chip[pgpio_dest->chip_right],
                             pgpio_dest->line_left,
                             pgpio_dest->line_right);
        gpio_test_state |= ret;

        log_i("gpio %-3d %-3d %s %-3d %-3d %s %s", pgpio_dest->chip_left, pgpio_dest->line_left, pgpio_dest->name_left,
              pgpio_dest->chip_right, pgpio_dest->line_right, pgpio_dest->name_right, ret == 0 ? "PASS" : "FAIL");
    }
    pthread_mutex_unlock(&gpio_test_mutex);
    return 0;
}
MSH_CMD_EXPORT_ALIAS(gpio_test_cmd, gpio_test, gpio test);

static void *gpio_test_thread_handle(void *arg)
{
    sleep(1);
    gpio_test_cmd(0, NULL);
    if (system("rm -rf /appcfg/*;sync;sync"))
    {
    }
    while (1)
    {
        if (gpio_test_state)
            sleep(5);
        else
            usleep(500 * 1000);
        pthread_mutex_lock(&gpio_test_mutex);
        led_toggle();
        pthread_mutex_unlock(&gpio_test_mutex);
    }
    return NULL;
}

static int gpio_test_app_init(void)
{
    ym_thread_create("gpio_test",
                     default_thread_cpuid,
                     default_thread_prio,
                     gpio_test_thread_handle,
                     NULL);
    return 0;
}
INIT_APP_EXPORT(gpio_test_app_init);

#define CONSUMER "PCBA Test"

// 设置输出引脚电平
static void set_gpio_value(struct gpiod_line *line, int value)
{
    if (gpiod_line_set_value(line, value) < 0)
    {
        perror("Set line output failed");
        return;
    }
}

// 读取输入引脚电平
static int get_gpio_value(struct gpiod_line *line)
{
    int value = gpiod_line_get_value(line);
    if (value < 0)
    {
        perror("Get line input failed");
        return -1;
    }
    return value;
}

// 主测试函数
static int gpio_test_func(struct gpiod_chip *chip_out, struct gpiod_chip *chip_in, int LINE_OUT, int LINE_IN)
{
    int ret = 0;
    struct gpiod_line *line_out, *line_in;

    // 请求输入引脚
    line_in = gpiod_chip_get_line(chip_in, LINE_IN);
    if (gpiod_line_request_input(line_in, CONSUMER) < 0)
    {
        perror("Request line as input failed");
        return -1;
    }

    // 请求输出引脚
    line_out = gpiod_chip_get_line(chip_out, LINE_OUT);
    if (gpiod_line_request_output(line_out, CONSUMER, 0) < 0)
    {
        perror("Request line as output failed");
        gpiod_line_release(line_in);
        return -1;
    }

    // 测试高电平
    set_gpio_value(line_out, 1);
    usleep(10 * 1000);
    if (get_gpio_value(line_in) != 1)
    {
        log_w("normal high level test failed");
        ret = -1;
    }

    // 测试低电平
    set_gpio_value(line_out, 0);
    usleep(10 * 1000);
    if (get_gpio_value(line_in) != 0)
    {
        log_w("normal low level test failed");
        ret = -1;
    }

    // 交换引脚角色并重复测试
    gpiod_line_release(line_out);
    gpiod_line_release(line_in);

    line_out = gpiod_chip_get_line(chip_out, LINE_IN);
    if (gpiod_line_request_output(line_out, CONSUMER, 0) < 0)
    {
        perror("Request line as output failed");
        return -1;
    }
    line_in = gpiod_chip_get_line(chip_in, LINE_OUT);
    if (gpiod_line_request_input(line_in, CONSUMER) < 0)
    {
        perror("Request line as input failed");
        gpiod_line_release(line_out);
        return -1;
    }

    // 测试高电平
    set_gpio_value(line_out, 1);
    usleep(10 * 1000);
    if (get_gpio_value(line_in) != 1)
    {
        log_w("high level test failed");
        ret = -1;
    }

    // 测试低电平
    set_gpio_value(line_out, 0);
    usleep(10 * 1000);
    if (get_gpio_value(line_in) != 0)
    {
        log_w("low level test failed");
        ret = -1;
    }

    // 将GPIO设置为输入
    gpiod_line_release(line_out);
    gpiod_line_release(line_in);

    line_in = gpiod_chip_get_line(chip_in, LINE_IN);
    if (gpiod_line_request_input(line_in, CONSUMER) < 0)
    {
        perror("Request line as input failed");
        return -1;
    }

    line_out = gpiod_chip_get_line(chip_out, LINE_OUT);
    if (gpiod_line_request_input(line_out, CONSUMER) < 0)
    {
        perror("Request line as input failed");
        gpiod_line_release(line_in);
        return -1;
    }

    // 释放GPIO资源
    gpiod_line_release(line_out);
    gpiod_line_release(line_in);

    return ret;
}

void led_toggle(void)
{
    static int led_state = 0;
    led_state = !led_state;
    if (led_line)
        set_gpio_value(led_line, led_state);
    if (state_line)
        set_gpio_value(state_line, !led_state);
}
