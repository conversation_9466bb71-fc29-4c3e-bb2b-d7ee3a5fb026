#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <stdlib.h>
#include <unistd.h>
#include <stdint.h>

#include "ym_log_trans.h"
#include "ym_log.h"

static int32_t log_fd = -1;

#define LOG_FILE "/userdata/ym_log_export.tar"

int32_t ym_log_trans_start(void)
{
    if (system("/usr/bin/ym_log_package.sh") < 0)
    {
        return -1;
    }

    log_fd = open(LOG_FILE, O_RDONLY);
    if (log_fd < 0)
    {
        return -1;
    }
    return 0;
}

int32_t ym_log_trans_stop(void)
{
    if (log_fd >= 0)
    {
        close(log_fd);
        log_fd = -1;
    }
    return 0;
}

uint8_t ym_log_trans_get(uint8_t *buf, uint32_t len)
{
    if (log_fd < 0)
    {
        return 0;
    }
    return (uint8_t)read(log_fd, buf, len);
}
