#define _USE_MATH_DEFINES
#define _GNU_SOURCE
#include <math.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <float.h>

#include "ym_audio_tools.h"

void audio32_noninterleaved_to_interleaved(const int32_t *input,
                                           int32_t *output,
                                           int32_t channels,
                                           int32_t frames)
{
    for (uint32_t i = 0; i < channels; i++)
    {
        for (uint32_t j = 0; j < frames; j++)
        {
            output[(j * channels + i)] = input[(i * frames + j)];
        }
    }
}

void audio32_interleaved_to_noninterleaved(const int32_t *input,
                                           int32_t *output,
                                           int32_t channels,
                                           int32_t frames)
{
    for (uint32_t i = 0; i < channels; i++)
    {
        for (uint32_t j = 0; j < frames; j++)
        {
            output[(i * frames + j)] = input[(j * channels + i)];
        }
    }
}

void audio16_interleaved_to_noninterleaved(const int16_t *input,
                                           int16_t *output,
                                           int32_t channels,
                                           int32_t frames)
{
    if (input == output)
    {
        int16_t *temp = malloc(channels * frames * sizeof(int16_t));
        if (!temp)
        {
            // handle memory allocation failure
            return;
        }

        for (int32_t i = 0; i < channels; i++)
        {
            for (int32_t j = 0; j < frames; j++)
            {
                temp[i * frames + j] = input[j * channels + i];
            }
        }

        memcpy(output, temp, channels * frames * sizeof(int16_t));
        free(temp);
    }
    else
    {
        for (int32_t i = 0; i < channels; i++)
        {
            for (int32_t j = 0; j < frames; j++)
            {
                output[i * frames + j] = input[j * channels + i];
            }
        }
    }
}

void audio16_noninterleaved_to_interleaved(const int16_t *input,
                                           int16_t *output,
                                           int32_t channels,
                                           int32_t frames)
{
    if (input == output)
    {
        int16_t *temp = malloc(channels * frames * sizeof(int16_t));
        if (!temp)
        {
            // handle memory allocation failure
            return;
        }

        for (int32_t i = 0; i < frames; i++)
        {
            for (int32_t j = 0; j < channels; j++)
            {
                temp[i * channels + j] = input[j * frames + i];
            }
        }

        memcpy(output, temp, channels * frames * sizeof(int16_t));
        free(temp);
    }
    else
    {
        for (int32_t i = 0; i < frames; i++)
        {
            for (int32_t j = 0; j < channels; j++)
            {
                output[i * channels + j] = input[j * frames + i];
            }
        }
    }
}

void audio_set_chn_map(int32_t *poff[], int32_t *pbuf, int32_t chns, int32_t word)
{
    while (chns > 0)
    {
        poff[chns - 1] = pbuf + (chns - 1) * word;
        chns--;
    }
}

void audio_set_chn_map16(int16_t *poff[], int16_t *pbuf, int32_t chns, int32_t word)
{
    while (chns > 0)
    {
        poff[chns - 1] = pbuf + (chns - 1) * word;
        chns--;
    }
}

void generate_sine(void *data, int32_t frames, int32_t is_float, int32_t is_noninterleaved,
                   int32_t rate, uint32_t bytes_in_sample, uint32_t channels,
                   float freq, float amp_of_percentage, double *_phase)
{
    static const double max_phase = 2. * M_PI;
    double phase = *_phase;
    double step = max_phase * freq / (double)rate;
    unsigned char *samples[channels];
    int32_t steps[channels];
    uint32_t chn;
    uint32_t maxval = (1 << (bytes_in_sample * 8 - 1)) - 1;
    for (chn = 0; chn < channels; chn++)
    {
        samples[chn] = (unsigned char *)(is_noninterleaved ? (data + chn * bytes_in_sample) : (data + chn * bytes_in_sample * channels));
        steps[chn] = is_noninterleaved ? bytes_in_sample * channels : bytes_in_sample;
    }
    /* fill the channel areas */
    while (frames-- > 0)
    {
        union
        {
            float f;
            int32_t i;
        } fval;
        int32_t res, i;
        if (is_float)
        {
            fval.f = sin(phase) * FLT_MAX * amp_of_percentage / 100.;
            res = fval.i;
        }
        else
            res = (int32_t)(sin(phase) * (double)maxval * amp_of_percentage / 100.);
        for (chn = 0; chn < channels; chn++)
        {
            for (i = 0; i < bytes_in_sample; i++)
                *(samples[chn] + i) = (res >> (i * 8)) & 0xff;
            samples[chn] += steps[chn];
        }
        phase += step;
        if (phase >= max_phase)
            phase -= max_phase;
    }
    *_phase = phase;
}

int32_t int2dB(int32_t val)
{
#define MIN_VOL_DB (-96 * 2)
#define DB_QUANT_FACTOR (64) /* to quant the floating-point dB value */
#define ONE_BITS_DB_QUANT (385)
    static const int16_t extra_dB_div16[16] = {0, 34, 65, 96, 124, 151, 177, 202, 225, 248, 270, 291, 311, 331, 349, 368};

    int32_t leftBits = __builtin_clz(val), idx, resDB;
    uint32_t nBits, realVa;

    if (leftBits >= 31)
        leftBits = 31;
    nBits = 31 - leftBits;
    if (nBits < 4)
        idx = (val << 4) >> nBits;
    else
        idx = val >> (nBits - 4);
    realVa = leftBits;
    idx &= 0x0f;
    resDB = (-realVa * ONE_BITS_DB_QUANT + extra_dB_div16[idx]);
    resDB /= DB_QUANT_FACTOR * 2 / 2;

    if (val == 0)
        resDB = MIN_VOL_DB;
    return -resDB;
}

float mixer_percent_to_db(int32_t vol)
{
    if (vol <= 0)
        return -96;
    else if (vol == 1)
        return -72;
    else if (vol == 2)
        return -60;
    else if (vol == 3)
        return -48;
    else if (vol == 4)
        return -42;
    else if (vol == 5)
        return -36;
    else if (vol < 15)
        return -33 + vol - 6;
    else if (vol < 41)
        return -24.5 + (vol - 15) * 0.5;
    else if (vol < 100)
        return -11.8 + (vol - 41) * 0.2;
    else if (vol == 100)
        return 0;
    else if (vol <= 220)
        return 0.1 + (vol - 101) * 0.1;
    else
        return 12;
}

int32_t mixer_db_to_percent(float dB)
{
    int32_t per = 0;

    if (dB < -72)
    {
        per = 0;
    }
    else if (dB <= -48 && dB >= -72)
    {
        per = (int32_t)((dB + 72) / 12) + 1;
    }
    else if (dB < -33 && dB > -48)
    {
        per = 3 + (int32_t)((dB + 48) / 6) + 1;
    }
    else if (dB <= -25 && dB >= -33)
    {
        per = (int32_t)(6 + dB + 33);
    }
    else if (dB <= -12 && dB > -25)
    {
        per = (int32_t)(15 + (dB + 25) * 2);
    }
    else if (dB <= 0 && dB > -12)
    {
        per = (int32_t)(40 + (dB + 12) * 5);
    }
    else if (dB <= 12 && dB > 0)
    {
        per = (int32_t)(100 + dB * 10);
    }
    else
        per = 220;
    return per;
}

void swap16Bits(uint32_t *arr, uint32_t size)
{
    for (int32_t i = 0; i < size; i++)
    {
        arr[i] = ((arr[i] & 0xFFFF) << 16) | ((arr[i] >> 16) & 0xFFFF);
    }
}

void bit32_to_16(uint8_t *src, uint8_t *dest, uint32_t channels, uint32_t frames)
{
    uint16_t *d = (uint16_t *)dest;
    uint32_t *s = (uint32_t *)src;
    uint32_t count = channels * frames;
    while (count--)
    {
        *d = ((*s + 0x8000) & 0xffff0000) >> 16;
        d++;
        s++;
    }
}

void bit16_to_32(uint8_t *src, uint8_t *dest, uint32_t channels, uint32_t frames)
{
    uint16_t *s = (uint16_t *)src;
    uint32_t *d = (uint32_t *)dest;
    uint32_t count = channels * frames;
    while (count--)
    {
        *d = ((*s) << 16) & 0xffff0000;
        d++;
        s++;
    }
}

void int16_to_float(const int16_t *in, float *out, int32_t len)
{
    for (int32_t i = 0; i < len; i++)
    {
        out[i] = in[i] / 32768.0f;
    }
}

void float_to_int16(const float *in, int16_t *out, int32_t len)
{
    for (int32_t i = 0; i < len; i++)
    {
        float sample = in[i] * 32768.0f;
        if (sample > 32767.0f)
            sample = 32767.0f;
        if (sample < -32768.0f)
            sample = -32768.0f;
        out[i] = (int16_t)sample;
    }
}

void int32_to_float(const int32_t *in, float *out, int32_t len)
{
    for (int32_t i = 0; i < len; i++)
    {
        out[i] = in[i] / 2147483648.0f;
    }
}

void float_to_int32(const float *in, int32_t *out, int32_t len)
{
    for (int32_t i = 0; i < len; i++)
    {
        float sample = in[i] * 2147483648.0f;
        if (sample > 2147483647.0f)
            sample = 2147483647.0f;
        if (sample < -2147483648.0f)
            sample = -2147483648.0f;
        out[i] = (int32_t)sample;
    }
}
