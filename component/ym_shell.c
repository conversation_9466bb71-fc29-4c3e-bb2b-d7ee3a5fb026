/*
 * Copyright (c) 2006-2021, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2006-04-30     Bernard      the first version for FinSH
 * 2006-05-08     Bernard      change finsh thread stack to 2048
 * 2006-06-03     Bernard      add support for skyeye
 * 2006-09-24     Bernard      remove the code related with hardware
 * 2010-01-18     <PERSON>      fix down then up key bug.
 * 2010-03-19     <PERSON>      fix backspace issue and fix device read in shell.
 * 2010-04-01     <PERSON>      add prompt output when start and remove the empty history
 * 2011-02-23     Bernard      fix variable section end issue of finsh shell
 *                             initialization when use GNU GCC compiler.
 * 2016-11-26     armink       add password authentication
 * 2018-07-02     aozima       add custom prompt support.
 */
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include <pthread.h>
#include <assert.h>
#include "ym_shell.h"
#include "version.h"
#include "ym_thread.h"
#include "ym_timer.h"
#include "config.h"

#define FINSH_PROMPT         "ym >"
#define STDOUT_BUFF_SIZE     (128)
#define FINSH_CMD_SIZE       (80)
#define FINSH_HISTORY_LINES  (10)

typedef enum input_stat
{
    WAIT_NORMAL,
    WAIT_SPEC_KEY,
    WAIT_FUNC_KEY,
} input_stat_e;

typedef struct finsh_shell
{
    input_stat_e stat;

    uint16_t current_history;
    uint16_t history_count;

    char cmd_history[FINSH_HISTORY_LINES][FINSH_CMD_SIZE];
    char line[FINSH_CMD_SIZE + 1];
    uint16_t line_position;
    uint16_t line_curpos;
} finsh_shell_t, *finsh_shell_p;

/* finsh symtab */
finsh_syscall_p _syscall_table_begin = NULL;
finsh_syscall_p _syscall_table_end = NULL;

finsh_syscall_p finsh_syscall_next(finsh_syscall_p call)
{
    uint32_t *ptr = (uint32_t *)(call + 1);
    while ((*ptr == 0) && ((uint32_t *)ptr < (uint32_t *)_syscall_table_end))
        ptr++;

    return (finsh_syscall_p)ptr;
}

#define FINSH_NEXT_SYSCALL(index) ((index) = finsh_syscall_next(index))
#define FINSH_ARG_MAX  (14)

static int32_t msh_help(int32_t argc, char **argv)
{
    (void)argc;
    (void)argv;

    printf("shell commands:\n");

    finsh_syscall_p index = _syscall_table_begin;
    for (; index < _syscall_table_end; FINSH_NEXT_SYSCALL(index))
        printf("%-20s - %s\n", index->name, index->desc);

    return 0;
}
MSH_CMD_EXPORT_ALIAS(msh_help, help, shell help);

static int32_t msh_split(char *cmd, size_t length, char *argv[FINSH_ARG_MAX])
{
    char *ptr;
    size_t position;
    size_t argc;
    size_t i;

    ptr = cmd;
    position = 0;
    argc = 0;

    while (position < length)
    {
        /* strip bank and tab */
        while ((*ptr == ' ' || *ptr == '\t') && position < length)
        {
            *ptr = '\0';
            ptr++;
            position++;
        }

        if (argc >= FINSH_ARG_MAX)
        {
            printf("Too many args ! We only Use:\n");
            for (i = 0; i < argc; i++)
            {
                printf("%s ", argv[i]);
            }
            printf("\n");
            break;
        }

        if (position >= length)
            break;

        /* handle string */
        if (*ptr == '"')
        {
            ptr++;
            position++;
            argv[argc] = ptr;
            argc++;

            /* skip this string */
            while (*ptr != '"' && position < length)
            {
                if (*ptr == '\\')
                {
                    if (*(ptr + 1) == '"')
                    {
                        ptr++;
                        position++;
                    }
                }
                ptr++;
                position++;
            }
            if (position >= length)
                break;

            /* skip '"' */
            *ptr = '\0';
            ptr++;
            position++;
        }
        else
        {
            argv[argc] = ptr;
            argc++;
            while ((*ptr != ' ' && *ptr != '\t') && position < length)
            {
                ptr++;
                position++;
            }
            if (position >= length)
                break;
        }
    }

    return argc;
}

static syscall_func msh_get_cmd(const char *cmd, const size_t size)
{
    finsh_syscall_p index = _syscall_table_begin;
    for (; index < _syscall_table_end; FINSH_NEXT_SYSCALL(index))
    {
        if ((strncmp(index->name, cmd, size) == 0) && (index->name[size] == '\0'))
            return (index->func);
    }

    return NULL;
}

static int32_t _msh_exec_cmd(char *cmd, size_t length, int32_t *retp)
{
    int32_t argc;
    size_t cmd0_size = 0;
    syscall_func cmd_func;
    char *argv[FINSH_ARG_MAX];

    assert(cmd);
    assert(retp);

    /* find the size of first command */
    while ((cmd[cmd0_size] != ' ' && cmd[cmd0_size] != '\t') && cmd0_size < length)
        cmd0_size++;
    if (cmd0_size == 0)
        return -1;

    cmd_func = msh_get_cmd(cmd, cmd0_size);
    if (cmd_func == NULL)
        return -1;

    /* split arguments */
    memset(argv, 0x00, sizeof(argv));
    argc = msh_split(cmd, length, argv);
    if (argc == 0)
        return -1;

    /* exec this command */
    *retp = cmd_func(argc, argv);
    return 0;
}

static int32_t msh_exec(char *cmd, size_t length)
{
    int32_t cmd_ret;

    /* strim the beginning of command */
    while ((length > 0) && ((*cmd == ' ') || (*cmd == '\t')))
    {
        cmd++;
        length--;
    }

    if (length == 0)
        return 0;

    /* Exec sequence:
     * 1. built-in command
     * 2. module(if enabled)
     */
    if (_msh_exec_cmd(cmd, length, &cmd_ret) == 0)
        return cmd_ret;

    /* truncate the cmd at the first space. */
    char *tcmd = cmd;
    while ((*tcmd != ' ') && (*tcmd != '\0'))
        tcmd++;
    *tcmd = '\0';

    printf("%s: command not found.\n", cmd);
    return -1;
}

static size_t str_common(const char *str1, const char *str2)
{
    const char *str = str1;

    while ((*str != '\0') && (*str2 != '\0') && (*str == *str2))
    {
        str++;
        str2++;
    }

    return (size_t)(str - str1);
}

static void msh_auto_complete(char *prefix)
{
    size_t length = 0u;
    size_t min_length = 0u;
    const char *name_ptr = NULL;
    const char *cmd_name = NULL;
    finsh_syscall_p index = NULL;

    if (*prefix == '\0')
    {
        msh_help(0, NULL);
        return;
    }

    printf("\n");

    /* checks in internal command */
    for (index = _syscall_table_begin; index < _syscall_table_end; FINSH_NEXT_SYSCALL(index))
    {
        /* skip finsh shell function */
        cmd_name = (const char *)index->name;
        if (strncmp(prefix, cmd_name, strlen(prefix)) == 0)
        {
            if (min_length == 0)
            {
                /* set name_ptr */
                name_ptr = cmd_name;
                /* set initial length */
                min_length = strlen(name_ptr);
            }

            length = str_common(name_ptr, cmd_name);
            if (length < min_length)
                min_length = length;

            printf("%s\n", cmd_name);
        }
    }

    /* auto complete string */
    if (name_ptr != NULL)
        strncpy(prefix, name_ptr, min_length);

    printf("%s%s", FINSH_PROMPT, prefix);
}

static int8_t finsh_getchar(FILE *in_file)
{
    int8_t c;

    // sleep(~0);
    pthread_testcancel();
    if (fread(&c, sizeof(int8_t), 1, in_file) == 1)
    {
        pthread_testcancel();
        return c;
    }
    pthread_testcancel();

    return EOF;
}

static void shell_handle_history(finsh_shell_p shell)
{
    printf("\033[2K\r");
    printf("%s%s", FINSH_PROMPT, shell->line);
}

static void shell_push_history(finsh_shell_p shell)
{
    if (shell->line_position != 0)
    {
        /* push history */
        if (shell->history_count >= FINSH_HISTORY_LINES)
        {
            /* if current cmd is same as last cmd, don't push */
            if (memcmp(&shell->cmd_history[FINSH_HISTORY_LINES - 1], shell->line, FINSH_CMD_SIZE))
            {
                /* move history */
                int index;
                for (index = 0; index < FINSH_HISTORY_LINES - 1; index++)
                {
                    memcpy(&shell->cmd_history[index][0],
                           &shell->cmd_history[index + 1][0], FINSH_CMD_SIZE);
                }
                memset(&shell->cmd_history[index][0], 0, FINSH_CMD_SIZE);
                memcpy(&shell->cmd_history[index][0], shell->line, shell->line_position);

                /* it's the maximum history */
                shell->history_count = FINSH_HISTORY_LINES;
            }
        }
        else
        {
            /* if current cmd is same as last cmd, don't push */
            if (shell->history_count == 0 || memcmp(&shell->cmd_history[shell->history_count - 1], shell->line, FINSH_CMD_SIZE))
            {
                shell->current_history = shell->history_count;
                memset(&shell->cmd_history[shell->history_count][0], 0, FINSH_CMD_SIZE);
                memcpy(&shell->cmd_history[shell->history_count][0], shell->line, shell->line_position);

                /* increase count and set current history position */
                shell->history_count++;
            }
        }
    }
    shell->current_history = shell->history_count;
}

static void ym_show_version(FILE *out_file)
{
    char convertedDate[11]; // "yyyy-mm-dd" 需要 10 个字符 + 1 个用于 null 终止符
    char board_name[30] = {0};
    FILE *board_fd = fopen("/proc/device-tree/app", "r");
    if (board_fd)
    {
        if (fread(board_name, sizeof(char), 30, board_fd))
        {
            board_name[strlen(board_name)] = '\0';
        }
        fclose(board_fd);
    }
    convertDate(__DATE__, convertedDate);
    /*
     ___    ___  __  .__   __.  __  ___ .___  ___.      ___      .__   __.
     \  \  /  / |  | |  \ |  | |  |/  / |   \/   |     /   \     |  \ |  |
      \  \/  /  |  | |   \|  | |  '  /  |  \  /  |    /  ^  \    |   \|  |
       \    /   |  | |  . `  | |    <   |  |\/|  |   /  /_\  \   |  . `  |
        |  |    |  | |  |\   | |  .  \  |  |  |  |  /  _____  \  |  |\   |
        |__|    |__| |__| \__| |__|\__\ |__|  |__| /__/     \__\ |__| \__|
    */
    fprintf(out_file, "___    ___  __   __   __   __  ___  ___  ___       ___       __   __  \n");
    fprintf(out_file, "\\  \\  /  / |  | |  \\ |  | |  |/  / |   \\/   |     /   \\     |  \\ |  | \n");
    fprintf(out_file, " \\  \\/  /  |  | |   \\|  | |  '  /  |  \\  /  |    /  ^  \\    |   \\|  | \n");
    fprintf(out_file, "  \\    /   |  | |  . `  | |    <   |  |\\/|  |   /  /_\\  \\   |  . `  | \n");
    fprintf(out_file, "   |  |    |  | |  |\\   | |  .  \\  |  |  |  |  /  _____  \\  |  |\\   | \n");
    fprintf(out_file, "   |__|    |__| |__| \\__| |__|\\__\\ |__|  |__| /__/     \\__\\ |__| \\__| \n");
    fprintf(out_file, "Board:%s\n", board_name);
    fprintf(out_file, "Audio mixer dsp system\n");
    fprintf(out_file, "Version V%d.%d.%d build %s %s\n",
            YM_VERSION, YM_SUBVERSION, YM_REVISION, convertedDate, __TIME__);
    fprintf(out_file, "Copyright by yinkman team\n");
}

void *finsh_thread_entry(void *param)
{
    int8_t ch;

    finsh_shell_t _shell;
    finsh_shell_p shell = &_shell;
    memset(shell, 0, sizeof(finsh_shell_t));

    FILE *file_stream = (FILE *)param;

    // TODO: login
    // show banner
    // clear screen
    fprintf(file_stream, "\e[1;1H\e[2J");
    ym_show_version(file_stream);
    fprintf(file_stream, FINSH_PROMPT);
    while (1)
    {
        ch = finsh_getchar(file_stream);
        if (ch < 0)
        {
            continue;
        }

        /*
         * handle control key
         * up key  : 0x1b 0x5b 0x41
         * down key: 0x1b 0x5b 0x42
         * right key:0x1b 0x5b 0x43
         * left key: 0x1b 0x5b 0x44
         */
        if (ch == 0x1b)
        {
            shell->stat = WAIT_SPEC_KEY;
            continue;
        }
        else if (shell->stat == WAIT_SPEC_KEY)
        {
            if (ch == 0x5b)
            {
                shell->stat = WAIT_FUNC_KEY;
                continue;
            }

            shell->stat = WAIT_NORMAL;
        }
        else if (shell->stat == WAIT_FUNC_KEY)
        {
            shell->stat = WAIT_NORMAL;

            if (ch == 0x41) /* up key */
            {
                /* prev history */
                if (shell->current_history > 0)
                    shell->current_history--;
                else
                {
                    shell->current_history = 0;
                    continue;
                }

                /* copy the history command */
                memcpy(shell->line, &shell->cmd_history[shell->current_history][0],
                       FINSH_CMD_SIZE);
                shell->line_curpos = shell->line_position = (uint16_t)strlen(shell->line);
                shell_handle_history(shell);
                continue;
            }
            else if (ch == 0x42) /* down key */
            {
                /* next history */
                if (shell->current_history < shell->history_count - 1)
                    shell->current_history++;
                else
                {
                    /* set to the end of history */
                    if (shell->history_count != 0)
                        shell->current_history = shell->history_count - 1;
                    else
                        continue;
                }

                memcpy(shell->line, &shell->cmd_history[shell->current_history][0],
                       FINSH_CMD_SIZE);
                shell->line_curpos = shell->line_position = (uint16_t)strlen(shell->line);
                shell_handle_history(shell);
                continue;
            }
            else if (ch == 0x44) /* left key */
            {
                if (shell->line_curpos)
                {
                    printf("\b");
                    shell->line_curpos--;
                }

                continue;
            }
            else if (ch == 0x43) /* right key */
            {
                if (shell->line_curpos < shell->line_position)
                {
                    printf("%c", shell->line[shell->line_curpos]);
                    shell->line_curpos++;
                }

                continue;
            }
        }

        /* received null or error */
        if (ch == '\0' || ch == 0xFF)
            continue;
        /* handle tab key */
        else if (ch == '\t')
        {
            int i;
            /* move the cursor to the beginning of line */
            for (i = 0; i < shell->line_curpos; i++)
                printf("\b");

            /* auto complete */
            msh_auto_complete(&shell->line[0]);
            /* re-calculate position */
            shell->line_curpos = shell->line_position = (uint16_t)strlen(shell->line);

            continue;
        }
        /* handle backspace key */
        else if (ch == 0x7f || ch == 0x08)
        {
            /* note that shell->line_curpos >= 0 */
            if (shell->line_curpos == 0)
                continue;

            shell->line_position--;
            shell->line_curpos--;

            if (shell->line_position > shell->line_curpos)
            {
                int i;

                memmove(&shell->line[shell->line_curpos],
                        &shell->line[shell->line_curpos + 1],
                        shell->line_position - shell->line_curpos);
                shell->line[shell->line_position] = 0;

                printf("\b%s  \b", &shell->line[shell->line_curpos]);

                /* move the cursor to the origin position */
                for (i = shell->line_curpos; i <= shell->line_position; i++)
                    printf("\b");
            }
            else
            {
                printf("\b \b");
                shell->line[shell->line_position] = 0;
            }

            continue;
        }

        /* handle end of line, break */
        if (ch == '\r' || ch == '\n')
        {
            shell_push_history(shell);

            printf("\n");
            msh_exec(shell->line, shell->line_position);

            printf(FINSH_PROMPT);
            memset(shell->line, 0, sizeof(shell->line));
            shell->line_curpos = shell->line_position = 0;
            continue;
        }

        /* it's a large line, discard it */
        if (shell->line_position >= FINSH_CMD_SIZE)
            shell->line_position = 0;

        /* normal character */
        if (shell->line_curpos < shell->line_position)
        {
            int i;

            memmove(&shell->line[shell->line_curpos + 1],
                    &shell->line[shell->line_curpos],
                    shell->line_position - shell->line_curpos);
            shell->line[shell->line_curpos] = ch;

            printf("%s", &shell->line[shell->line_curpos]);

            /* move the cursor to new position */
            for (i = shell->line_curpos; i < shell->line_position; i++)
                printf("\b");
        }
        else
        {
            shell->line[shell->line_position] = ch;

            printf("%c", ch);
        }

        ch = 0;
        shell->line_position++;
        shell->line_curpos++;
        if (shell->line_position >= FINSH_CMD_SIZE)
        {
            /* clear command line */
            shell->line_position = 0;
            shell->line_curpos = 0;
        }
    } /* end of device read */
    return NULL;
}

static void *stdout_thread(void *parameter)
{
    int32_t stdout_fd = *(int *)parameter;
#ifndef USB_HID_ON_SHELL_SERIAL_OFF
    void stdout_to_ttygs0(uint8_t * data, uint32_t count);
#endif /* USB_HID_ON_SHELL_SERIAL_OFF */
    static uint8_t buff[STDOUT_BUFF_SIZE];
    FILE *log_file = fopen("/data/shelllog", "a");
    if (!log_file)
        return NULL;
    int32_t count = 0;
    while (1)
    {
        count = read(stdout_fd, buff, STDOUT_BUFF_SIZE - 2);
        if (count > 0)
        {
            fwrite(buff, 1, count, log_file);
            fflush(log_file);
#ifndef USB_HID_ON_SHELL_SERIAL_OFF
            // stdout_to_ttygs0(buff, count);
#endif /* end #ifndef USB_HID_ON_SHELL_SERIAL_OFF */
        }
    }
}

static void finsh_system_function_init(const void *begin, const void *end)
{
    _syscall_table_begin = (finsh_syscall_p)begin;
    _syscall_table_end = (finsh_syscall_p)end;
}

/*
 * @ingroup finsh
 *
 * This function will initialize finsh shell
 */
int32_t finsh_system_init(void)
{
    extern const finsh_syscall_t __start_FSymTab;
    extern const finsh_syscall_t __stop_FSymTab;
    finsh_system_function_init(&__start_FSymTab, &__stop_FSymTab);

    static int32_t stdout_fd[2];

    if (pipe(stdout_fd) == -1)
    {
        printf("stdout_fd open err.\n");
        exit(1);
    }
#ifndef USB_HID_ON_SHELL_SERIAL_OFF
    dup2(stdout_fd[1], STDOUT_FILENO);
#endif /* end #ifndef USB_HID_ON_SHELL_SERIAL_OFF */
    setbuf(stdout, NULL);
    ym_show_version(stdout);

    ym_thread_run(ym_thread_create("stdout_thread",
                                   default_thread_cpuid,
                                   stdout_thread_prio,
                                   stdout_thread,
                                   &stdout_fd[0]));

    return 0;
}

static int32_t shell_version(int32_t argc, char **argv)
{
    (void)argc;
    (void)argv;

    ym_show_version(stdout);

    return 0;
}
MSH_CMD_EXPORT_ALIAS(shell_version, version, show version information);
