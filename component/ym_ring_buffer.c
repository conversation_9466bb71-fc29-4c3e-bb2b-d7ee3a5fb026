#include <stdlib.h>
#include <string.h>
#include <stdint.h>

#include "ym_ring_buffer.h"
#include "ym_log.h"

// 创建环形缓冲区
void ring_buffer_create(ring_buffer_p *rb, uint32_t size)
{
    *rb = (ring_buffer_p)malloc(sizeof(ring_buffer_t));
    if (*rb == NULL)
    {
        log_e("Failed to allocate memory for ring buffer");
        return;
    }

    ring_buffer_init(*rb, size);
}

// 初始化环形缓冲区
void ring_buffer_init(ring_buffer_p rb, uint32_t size)
{
    rb->buffer = (char *)calloc(size, sizeof(char));
    if (rb->buffer == NULL)
    {
        log_e("Failed to allocate memory for ring buffer");
        return;
    }

    rb->max = size;
    rb->head = 0;
    rb->tail = 0;
    rb->full = 0;
}

// 释放环形缓冲区
void ring_buffer_free(ring_buffer_p rb)
{
    free(rb->buffer);
}

// 清空环形缓冲区
void ring_buffer_clear(ring_buffer_p rb)
{
    rb->head = 0;
    rb->tail = 0;
    rb->full = 0;
}

// 写入数据到环形缓冲区
void ring_buffer_write(ring_buffer_p rb, const char *data, uint32_t size)
{
    for (uint32_t i = 0; i < size; i++)
    {
        rb->buffer[rb->head] = data[i];
        rb->head = (rb->head + 1) % rb->max;

        if (rb->full)
        {
            rb->tail = (rb->tail + 1) % rb->max;
        }

        rb->full = (rb->head == rb->tail);
    }
}

// 读取数据从环形缓冲区
uint32_t ring_buffer_read(ring_buffer_p rb, char *data, uint32_t size)
{
    if (rb->head == rb->tail && !rb->full)
    {
        return 0; // 缓冲区为空
    }

    uint32_t count = ring_buffer_available_data(rb);

    size = count > size ? size : count;
    count = 0;
    for (uint32_t i = 0; i < size; i++)
    {
        data[i] = rb->buffer[rb->tail];
        rb->tail = (rb->tail + 1) % rb->max;
        count++;
    }
    rb->full = false;

    return count;
}

// 检查环形缓冲区是否为空
bool ring_buffer_empty(ring_buffer_p rb)
{
    return (rb->head == rb->tail) && !rb->full;
}

// 检查环形缓冲区是否已满
bool ring_buffer_full(ring_buffer_p rb)
{
    return rb->full;
}

// 获取环形缓冲区可用数据量
uint32_t ring_buffer_available_data(ring_buffer_p rb)
{
    if (rb->head == rb->tail)
    {
        return rb->full ? rb->max : 0;
    }
    else if (rb->head > rb->tail)
    {
        return rb->head - rb->tail;
    }
    else
    {
        return rb->max - rb->tail + rb->head;
    }
}

void ring_buffer_destroy(ring_buffer_p rb)
{
    ring_buffer_free(rb);
    free(rb);
}
