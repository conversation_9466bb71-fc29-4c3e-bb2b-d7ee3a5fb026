/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-04-18 11:45:47
 * @LastEditTime: 2024-10-15 09:16:10
 * @LastEditors: Anzhi<PERSON>o
 * @Description: 封装pthread
 * @FilePath: /audio_mixer_rk3308/component/ym_thread.c
 * Copyright (c) 2006-2024, Yinkman Development Team
 */

#define __USE_GNU
#define _GNU_SOURCE /* See feature_test_macros(7) */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sched.h>
#include <pthread.h>
#include <errno.h>
#include <signal.h>
#include <time.h>
#include <unistd.h>
#include <pthread.h>
#include "ym_shell.h"
#include "utlist.h"
#include "ym_thread.h"
#include "ym_log.h"

#define YM_THREAD_NAME_LEN (64)

typedef struct _thread_def
{
    char name[YM_THREAD_NAME_LEN];
    pthread_t pid;
    int core_id;
    int priority;
    void *(*run)(void *args);
    void *args;
    int isrunning;
    pthread_mutex_t mutex;
    struct _thread_def *next, *prev;
} ym_thread_t, *ym_thread_p;

static ym_thread_p thread_head;
static pthread_mutex_t thread_list_mutex = PTHREAD_MUTEX_INITIALIZER;

static void *run(void *args)
{
    void *ret;
    ym_thread_p pt = args;
    pthread_setname_np(pt->pid, pt->name);
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    CPU_SET(pt->core_id, &cpuset);
    pthread_setaffinity_np(pt->pid, sizeof(cpu_set_t), &cpuset);
    pthread_setcancelstate(PTHREAD_CANCEL_ENABLE, NULL);
    pthread_setcanceltype(PTHREAD_CANCEL_DEFERRED, NULL);
    log_i("%s running.", pt->name);
    pt->isrunning = 1;
    pthread_mutex_unlock(&pt->mutex);
    ret = pt->run(pt->args);
    pthread_mutex_lock(&pt->mutex);
    pt->isrunning = 0;
    pthread_mutex_unlock(&pt->mutex);
    log_i("%s exit.", pt->name);
    return ret;
}

int ym_thread_is_running(void *handle)
{
    ym_thread_p pt = (ym_thread_p)handle;
    return pt->isrunning;
}

/**
 * @description: 注册线程，注意：只能在启动过程中调用
 * @param {char} *name
 * @param {int} core_id
 * @param {int} priority
 * @param {void} *
 * @param {void} *__arg
 * @return {*}
 */
void *ym_thread_create(const char *name, int core_id, int priority, void *(*__start_routine)(void *), void *__arg)
{
    ym_thread_p pt = (ym_thread_p)malloc(sizeof(ym_thread_t));
    if (!pt)
    {
        log_e("[%s]:malloc err.", __func__);
        return NULL;
    }
    memset(pt, 0, sizeof(ym_thread_t));
    snprintf(pt->name, YM_THREAD_NAME_LEN - 1, "%s", name);
    pt->core_id = core_id;
    pt->run = __start_routine;
    pt->args = __arg;
    pt->next = pt->prev = NULL;
    pt->isrunning = 0;
    pt->priority = priority;
    pt->mutex = (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER;
    pthread_mutex_lock(&thread_list_mutex);
    DL_APPEND(thread_head, pt);
    pthread_mutex_unlock(&thread_list_mutex);
    return (void *)pt;
}

/**
 * @description: 运行一个线程，注意：ym_thread_run，ym_thread_kill同一个线程，需要解决互斥问题，即运行、kill同一个线程的这个线程，必须为同一个线程。
 * @param {void} *handle
 * @return {*}
 * @use:
 */
void ym_thread_run(void *handle)
{
    ym_thread_p pt = (ym_thread_p)handle;
    pthread_mutex_trylock(&pt->mutex);
    pthread_attr_t thread_attr;
    struct sched_param thread_sched_param;
    pthread_attr_init(&thread_attr);
    pthread_attr_setschedpolicy(&thread_attr, SCHED_FIFO);
    pthread_attr_setstacksize(&thread_attr, 32 * 1024);
    pthread_attr_setschedparam(&thread_attr, &thread_sched_param);
    thread_sched_param.sched_priority = pt->priority;
    pthread_create(&pt->pid, &thread_attr, run, pt);
}

void ym_thread_kill(void *handle, void **thread_ret)
{
    ym_thread_p pt = (ym_thread_p)handle;
    if (pthread_self() == pt->pid)
    {
        pthread_exit(NULL);
    }
    pthread_mutex_lock(&pt->mutex);
    if (pt->pid)
    {
        if (pthread_kill(pt->pid, 0) == ESRCH)
        {
            log_e("%s thread not found.", pt->name);
        }
        else
        {
            log_i("Sending cancel request to %s thread.", pt->name);
            if (pthread_cancel(pt->pid) != 0)
            {
                log_e("Failed to cancel %s thread.", pt->name);
            }
            else
            {
                log_i("%s thread successfully canceled.", pt->name);
            }

            log_i("Waiting for %s thread to terminate.", pt->name);
            if (pthread_join(pt->pid, thread_ret) != 0)
            {
                log_e("Failed to join %s thread.", pt->name);
            }
            else
            {
                log_i("%s thread successfully terminated.", pt->name);
            }
        }
        pt->pid = 0;
    }
    pthread_mutex_unlock(&pt->mutex);
}

void ym_thread_destroy(void *handle)
{
    void *thread_ret;
    ym_thread_p pt = (ym_thread_p)handle;
    // 判断当前所处线程是否为目标线程
    if (pthread_self() == pt->pid)
    {
        pthread_exit(NULL);
    }
    pthread_mutex_lock(&pt->mutex);
    if (!pt->isrunning)
        goto __exit;
    if (pt->pid)
    {
        if (pthread_kill(pt->pid, 0) == ESRCH)
        {
            log_e("%s thread not found.", pt->name);
        }
        else
        {
            log_i("Sending cancel request to %s thread.", pt->name);
            if (pthread_cancel(pt->pid) != 0)
            {
                log_e("Failed to cancel %s thread.", pt->name);
            }
            else
            {
                log_i("%s thread successfully canceled.", pt->name);
            }

            log_i("Waiting for %s thread to terminate.", pt->name);
            if (pthread_join(pt->pid, &thread_ret) != 0)
            {
                log_e("Failed to join %s thread.", pt->name);
            }
            else
            {
                log_i("%s thread successfully terminated.", pt->name);
            }
        }
        pt->pid = 0;
    }
__exit:
    pthread_mutex_unlock(&pt->mutex);
    pthread_mutex_lock(&thread_list_mutex);
    DL_DELETE(thread_head, pt);
    pthread_mutex_unlock(&thread_list_mutex);
    log_i("destroy %s thread.", pt->name);
    free(pt);
}

/**
 * @description: 运行所有已注册的线程。注意：只在main函数中调用
 * @return {*} none
 */
void ym_thread_all_run(void)
{
    pthread_attr_t thread_attr;
    struct sched_param thread_sched_param;
    pthread_attr_init(&thread_attr);
    pthread_attr_setschedpolicy(&thread_attr, SCHED_FIFO);
    pthread_attr_setschedparam(&thread_attr, &thread_sched_param);
    ym_thread_p pt;
    pthread_mutex_lock(&thread_list_mutex);
    DL_FOREACH(thread_head, pt)
    {
        if (!pt->isrunning)
        {
            thread_sched_param.sched_priority = pt->priority;
            pthread_create(&pt->pid, &thread_attr, run, pt);
        }
    }
    pthread_mutex_unlock(&thread_list_mutex);
    pthread_attr_destroy(&thread_attr);
}

void ym_thread_list(void)
{
    ym_thread_p pt;
    printf("name\t\tisrunning\tcore\n");
    printf("------------------------------------\n");
    pthread_mutex_lock(&thread_list_mutex);
    DL_FOREACH(thread_head, pt)
    {
        printf("%-16s\t%-d\t%-d\n", pt->name, pt->isrunning, pt->core_id);
    }
    pthread_mutex_unlock(&thread_list_mutex);
}
MSH_CMD_EXPORT_ALIAS(ym_thread_list, ps, list threads in the system);
