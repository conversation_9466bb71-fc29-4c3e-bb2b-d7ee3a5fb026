#include <stdio.h>
#include <stdint.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <linux/watchdog.h>

#include "ym_watchdog.h"
#include "ym_log.h"

#ifndef WATCHDOG_DISABLE

static int32_t watchdog_fd;

void watchdog_init(int32_t timeout)
{
    watchdog_fd = open("/dev/watchdog0", O_WRONLY);
    if (watchdog_fd < 0)
    {
        log_e("Failed to open watchdog device.");
        return;
    }
    timeout = timeout / 1000;
    if (ioctl(watchdog_fd, WDIOC_SETTIMEOUT, &timeout) < 0)
    {
        log_e("Failed to set watchdog timeout.");
        close(watchdog_fd);
        return;
    }

    log_i("watchdog_init successful");
}

void watchdog_feed(void)
{
    if (write(watchdog_fd, "\0", 1) != 1)
    {
        log_e("Failed to feed watchdog.");
        close(watchdog_fd);
        return;
    }
}

#endif /* WATCHDOG_DISABLE */
