/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-11-30 10:16:37
 * @LastEditTime: 2024-11-18 14:14:55
 * @LastEditors: Anzhichao
 * @Description: 数据库中间件，观察者模式
 * @FilePath: /audio_mixer_rk3308/component/ym_database.c
 * Copyright (c) 2006-2024, Yinkman Development Team
 */

#include "ym_database.h"
#include <string.h>
#include "ym_shell.h"
#include "ym_startup.h"
#include "ym_utilities.h"
#include "utlist.h"
#include <time.h>
#include "version.h"

#include "ym_log.h"

typedef void (*value_write_callback)(int index, database_value old_data, database_value new_data);

typedef struct v_callback_def
{
    value_write_callback callback;
    struct v_callback_def *next, *prev;
} value_callback_def_t;

typedef database_value (*value_write_check_callback)(int index, database_value data);

database_value database_meta_value[DATABASE_INDEX_MAX];

database_value database_meta_value_shot[DATABASE_INDEX_MAX];

static value_callback_def_t *meta_value_callback[DATABASE_INDEX_MAX];

static value_write_check_callback meta_value_check_callback[DATABASE_INDEX_MAX];

void database_lowlevel_init(void)
{
    memset(database_meta_value, 0, sizeof(database_value) * DATABASE_INDEX_MAX);
    memset(database_meta_value_shot, 0, sizeof(database_value) * DATABASE_INDEX_MAX);
}

int database_write(int index, database_value d)
{
    value_callback_def_t *pos;
    database_value old_value;
    if (index >= DATABASE_INDEX_MAX)
    {
        log_e("index > %d", DATABASE_INDEX_MAX);
        return -1;
    }
    old_value = database_meta_value[index];
    // 执行参数合法检测函数
    if (meta_value_check_callback[index])
        database_meta_value[index] = meta_value_check_callback[index](index, d);
    else
        database_meta_value[index] = d;
    // 执行修改回调函数
    DL_FOREACH(meta_value_callback[index], pos)
    {
        if (pos->callback)
            pos->callback(index, old_value, database_meta_value[index]);
    }
    return 0;
}

int database_write2(int index, const database_value *pvalue, uint32_t count)
{
    while (count--)
    {
        if (database_write(index++, *pvalue++) < 0)
            return -1;
    }
    return 0;
}

int database_write_no_callback(int index, database_value d)
{
    if (index >= DATABASE_INDEX_MAX)
    {
        log_e("index > %d", DATABASE_INDEX_MAX);
        return -1;
    }
    // 执行参数合法检测函数
    if (meta_value_check_callback[index])
        database_meta_value[index] = meta_value_check_callback[index](index, d);
    else
        database_meta_value[index] = d;
    return 0;
}

int database_write2_no_callback(int index, const database_value *pvalue, uint32_t count)
{
    while (count--)
    {
        if (database_write_no_callback(index++, *pvalue++) < 0)
            return -1;
    }
    return 0;
}

int database_register(int index,
                      value_write_callback callback)
{
    value_callback_def_t *pos;
    if (index >= DATABASE_INDEX_MAX)
        return -1;

    DL_FOREACH(meta_value_callback[index], pos)
    {
        if (callback == pos->callback)
        {
            return 0;
        }
    }
    // new node
    pos = malloc(sizeof(value_callback_def_t));
    if (pos == NULL)
        return -2;
    pos->callback = callback;
    DL_APPEND(meta_value_callback[index], pos);
    return 0;
}

int database_register_check(int index,
                            value_write_check_callback callback)
{
    if (index >= DATABASE_INDEX_MAX)
        return -1;

    if (meta_value_check_callback[index])
    {
        log_e("database check already registed,index:%d", index);
        return -2;
    }
    meta_value_check_callback[index] = callback;
    return 0;
}

void database_take_shot(void)
{
    memcpy(database_meta_value_shot, database_meta_value, sizeof(database_value) * DATABASE_INDEX_MAX);
}

void database_restore_frome_shot(void)
{
    database_write2(0, database_meta_value_shot, DATABASE_INDEX_MAX);
}

static int database(uint8_t argc, char **argv)
{
    database_value tmp = {.u32 = 0};
    int idx;
    if (argc < 3)
    {
    __exit:
        printf("Usage:\n");
        printf("database read  [index]        - database read\n");
        printf("database write [index] [f|u]  - database write");
        printf("\n");
    }
    else
    {
        if (!strcmp(argv[1], "read"))
        {
            idx = atoi(argv[2]);
            if (idx < DATABASE_INDEX_MAX)
            {
                printf("database_meta_value:\n");
                tmp = database_read(idx);
                printf("f32:%f\n", tmp.f32);
                printf("u32:%u\n", tmp.u32);
            }
            else
            {
                printf("read database err");
            }
        }
        else if (!strcmp(argv[1], "write"))
        {
            if (argc < 5)
            {
                goto __exit;
            }
            if (!strcmp(argv[3], "f"))
            {
                tmp.f32 = atof(argv[4]);
            }
            else
            {
                tmp.u32 = atol(argv[4]);
            }

            if (database_write(atoi(argv[2]), tmp) == 0)
            {
                printf("database_meta_value:\n");
                printf("f32:%f\n", tmp.f32);
                printf("u32:%d\n", tmp.u32);
            }
            else
            {
                printf("read database err");
            }
        }
        else
        {

            goto __exit;
        }
    }
    return 0;
}

MSH_CMD_EXPORT(database, test database);
