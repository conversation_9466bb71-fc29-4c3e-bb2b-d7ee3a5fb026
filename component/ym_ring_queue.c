#include <stdlib.h>
#include <string.h>
#include <stdint.h>

#include "ym_ring_queue.h"

ring_queue_p ring_queue_create(uint32_t frames)
{
    ring_queue_p q = malloc(sizeof(ring_queue_t));
    if (q)
    {
        q->buffer = malloc(frames * sizeof(int32_t));
        if (q->buffer)
        {
            q->size = frames;
            q->front = 0;
            q->rear = 0;
        }
        else
        {
            free(q);
            q = NULL;
        }
    }
    return q;
}

uint32_t ring_queue_push(ring_queue_p q, int *d, uint32_t n)
{
    uint32_t count = 0;

    if((q->front + n) < q->size)
    {
        memmove(q->buffer + q->front, d, n * sizeof(int));
        q->front += n;
        count += n;
    }
    else
    {
        memmove(q->buffer + q->front, d, (q->size - q->front) * sizeof(int));
        memmove(q->buffer, d + (q->size - q->front), (n - q->size + q->front) * sizeof(int));
        q->front = (n - q->size + q->front);
        count += n;
    }

    return count;
}

int32_t ring_queue_pull(ring_queue_p q, int *d, uint32_t n)
{
    int32_t count = 0;

    if (q->front == q->rear)
    {
        q->rear = (q->rear + 1) % q->size;
    }

    if((q->rear + n) < q->size)
    {
        memmove(d, q->buffer + q->rear, n * sizeof(int));
        q->rear += n;
        count += n;
    }
    else
    {
        memmove(d, q->buffer + q->rear, (q->size - q->rear) * sizeof(int));
        memmove(d + (q->size - q->rear), q->buffer, (n - q->size + q->rear) * sizeof(int));
        q->rear = (n - q->size + q->rear);
        count += n;
    }

    return count;
}

uint32_t ring_queue_len(ring_queue_p q)
{
    if (q->front > q->rear)
        return q->front - q->rear;
    else if (q->front < q->rear)
        return q->front + q->size - q->rear;
    else
        return q->size;
}

void ring_queue_destroy(ring_queue_p q)
{
    if (q)
    {
        if (q->buffer)
        {
            free(q->buffer);
            q->buffer = NULL;
        }
        free(q);
        q = NULL;
    }
}
