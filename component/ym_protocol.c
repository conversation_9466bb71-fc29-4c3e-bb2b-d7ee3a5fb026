/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-11-09 14:47:04
 * @LastEditTime: 2024-11-25 15:20:00
 * @LastEditors: An<PERSON><PERSON><PERSON>
 * @Description: 音曼控制协议实现
 * @FilePath: /audio_mixer_rk3308/component/ym_protocol.c
 * Copyright (c) 2006-2024, Yinkman Development Team
 */

#include <stdio.h>
#include <errno.h>
#include <semaphore.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <netinet/tcp.h> // For TCP keep-alive options
#include <termios.h>
#include <arpa/inet.h>

#include "config.h"
#include "ym_protocol.h"
#include "ym_startup.h"
#include "ym_database.h"
#include "ym_shell.h"
#include "version.h"
#include "ym_thread.h"
#include "utlist.h"
#include "ym_mem_pool.h"
#include "ym_timer.h"
#include "ym_audio_tools.h"
#include "ym_mixer_beam.h"
#include "ym_mixer_platform.h"
// #include "usb_host.h"
// #include "ym_bt_pair.h"

#include "ym_log.h"
#include "ym_log_trans.h"
#ifdef BD_MIXER_LINE_MICS
#include "pca9536.h"
#endif /* BD_MIXER_LINE_MICS */
#ifdef BD_MIXER_JYD_8MIC
#include "misc.h"
#endif /* BD_MIXER_JYD_8MIC */
#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
#include "a2b_com.h"
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */
#ifdef PROTOCOL_SERIAL_PORT_BYPASS
#include "ym_serial.h"
#endif /* end PROTOCOL_SERIAL_PORT_BYPASS */

extern uint32_t hwid_adc_subversion;

#ifdef BEAM_ENABLE
extern mixer_def_p pbeam_handle[BEAM_INSTANCE_NUMBER];
#endif

struct ymlink_stack;

struct ymlink_desc ym;

static int ymlink_mem_init(void)
{
    ym.message_pool = ym_mem_pool_init(10, sizeof(ymlink_message_t));
    log_i("ymlink_mem_init successful.");
    return 0;
}
INIT_MEM_EXPORT(ymlink_mem_init);

/**
 * @description: 注册ymlink接收回调函数
 * @param {uint8_t} command
 * @param {callback} handle_callback
 * @return {*}
 */
void ymlink_register(int command, handle_callback_def callback)
{
    ymlink_callback_def_p elem;
    /*重复性检查，当把两个相同key值的结构体添加到哈希表中时会报错*/
    HASH_FIND_INT(ym.handle_head, &command, elem); /* id already in the hash? */
    /*只有在哈希中不存在ID的情况下，我们才创建该项目并将其添加。否则，我们只修改已经存在的结构。*/
    if (elem == NULL)
    {
        elem = (ymlink_callback_def_p)malloc(sizeof(ymlink_callback_def_t));
        if (elem)
        {
            memset(elem, 0, sizeof(ymlink_callback_def_t));
            elem->command = command;
            HASH_ADD_INT(ym.handle_head, command, elem); /* id: name of key field */
        }
        else
        {
            log_e("ymlink_register malloc err.");
            return;
        }
    }
    handle_callback_list_t *callback_elem = (handle_callback_list_p)malloc(sizeof(handle_callback_list_t));
    memset(callback_elem, 0, sizeof(handle_callback_list_t));
    callback_elem->callback = callback;
    DL_APPEND(elem->callback_list, callback_elem);
}

/**
 * @description: 注册ymlink特殊切换状态机函数，主要解决固件数据中含有帧头帧尾的问题
 * @param {uint8_t} command
 * @param {callback} special_status_change_handle
 * @return {*}
 */
static void ymlink_special_status_register(int command, special_status_change_handle callback)
{
    ymlink_status_change_def_p elem;
    /*重复性检查，当把两个相同key值的结构体添加到哈希表中时会报错*/
    HASH_FIND_INT(ym.special_status_change, &command, elem); /* id already in the hash? */
    /*只有在哈希中不存在ID的情况下，我们才创建该项目并将其添加。否则，我们只修改已经存在的结构。*/
    if (elem == NULL)
    {
        elem = (ymlink_status_change_def_p)malloc(sizeof(ymlink_status_change_def_t));
        if (elem)
        {
            elem->command = command;
            HASH_ADD_INT(ym.special_status_change, command, elem); /* id: name of key field */
            elem->callback = callback;
        }
        else
        {
            log_e("ymlink_special_status_register malloc err.");
        }
    }
    else
    {
        log_e("command:%d is exist.", command);
    }
}

static ymlink_callback_def_p find_callback(int command)
{
    ymlink_callback_def_p s;
    HASH_FIND_INT(ym.handle_head, &command, s); /* s: output pointer */
    return s;
}

static ymlink_status_change_def_p find_special_status_check(int command)
{
    ymlink_status_change_def_p s;
    HASH_FIND_INT(ym.special_status_change, &command, s); /* s: output pointer */
    return s;
}

static void *set_chn_mute(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t chn;
        uint8_t mute;
    } __attribute__((packed)) set_chn_mute_def;
    set_chn_mute_def *args = (set_chn_mute_def *)data;
    database_value tmp = {.i32 = args->mute};

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xA2;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 2, NULL, 0);
        return NULL;
    }
    else // sync MIC_GLOBAL_MUTE & AEC_OUT_MUTE to slaves
    {
        if ((args->chn == MIC_GLOBAL_MUTE) ||
            (args->chn == AEC_OUT_MUTE))
        {
            int i = database_get(a2b_slave_nodes_index)->i32;
            while (i > 0)
            {
                a2b_comm.node = i--;
                bsp_ad2428_s_comm(&a2b_comm, data, 2, NULL, 0);
            }
        }
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    if (args->chn <= IN_CHN_MAX)
    {
        if (args->mute == 0xff)
        {
            tmp.i32 = !database_get(input_channel1_mute_index + args->chn)->i32;
        }
        database_write(input_channel1_mute_index + args->chn, tmp);
    }
    else if (args->chn <= OUT_CHN_MAX)
    {
        args->chn -= OUT_CHN_MIN;
        if (args->mute == 0xff)
        {
            tmp.i32 = !database_get(output_channel1_mute_ms_index + MIXER1_BASE_INDEX + args->chn)->i32;
        }
        database_write(output_channel1_mute_ms_index + MIXER1_BASE_INDEX + args->chn, tmp);
        if (args->mute == 0xff)
        {
            tmp.i32 = !database_get(output_channel1_mute_ms_index + MIXER2_BASE_INDEX + args->chn)->i32;
        }
        database_write(output_channel1_mute_ms_index + MIXER2_BASE_INDEX + args->chn, tmp);
    }
    else if (args->chn == MIC_GLOBAL_MUTE)
    {
        if (args->mute == 0xff)
        {
            tmp.i32 = !database_get(global_mic_mute_index)->i32;
        }
        database_write(global_mic_mute_index, tmp);
#ifdef BD_MIXER_LINE_MICS
        if (args->mute == 1u)
        {
            pca9536_reg_set(0, PCA9536_REG_OUTPUT_PORT, 0x1); /* red led on, blue led off */
            pca9536_reg_set(1, PCA9536_REG_OUTPUT_PORT, 0x1);
        }
        else
        {
            pca9536_reg_set(0, PCA9536_REG_OUTPUT_PORT, 0x2); /* red led off, blue led on */
            pca9536_reg_set(1, PCA9536_REG_OUTPUT_PORT, 0x2);
        }
#endif /* BD_MIXER_LINE_MICS */
    }
    else if (args->chn == OUT_GLOBAL_MUTE)
    {
        if (args->mute == 0xff)
        {
            tmp.i32 = !database_get(global_out_mute_index)->i32;
        }
        database_write(global_out_mute_index, tmp);
    }
    else if (args->chn >= IN_MUTE_CHN17 && args->chn <= IN_MUTE_CHN24)
    {
        args->chn -= IN_MUTE_CHN17;
        if (args->mute == 0xff)
        {
            tmp.i32 = !database_get(input_channel17_mute_index + args->chn)->i32;
        }
        database_write(input_channel17_mute_index + args->chn, tmp);
    }
    else if (args->chn == ALL_SPEAKER_MUTE)
    {
        if (args->mute == 0xff)
        {
            tmp.i32 = !database_get(global_skp_mute_index)->i32;
        }
        database_write(global_skp_mute_index, tmp);
    }
    else if (args->chn == AEC_OUT_MUTE)
    {
        if (args->mute == 0xff)
        {
            tmp.i32 = !database_get(global_aec_out_mute_index)->i32;
        }
        database_write(global_aec_out_mute_index, tmp);
    }
    else if (args->chn == AFC_OUT_MUTE)
    {
        if (args->mute == 0xff)
        {
            tmp.i32 = !database_get(global_afc_out_mute_index)->i32;
        }
        database_write(global_afc_out_mute_index, tmp);
    }
    else if (args->chn == HW_PA_MUTE)
    {
        if (args->mute == 0xff)
        {
            tmp.i32 = !database_get(global_hw_pa_mute_index)->i32;
        }
        database_write(global_hw_pa_mute_index, tmp);
    }
    return NULL;
}

static void *get_chn_mute(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t chn;
    } __attribute__((packed)) get_chn_mute_def;
    get_chn_mute_def *args = (get_chn_mute_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xA3;
    a2b_comm.ackl = 5;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 1, &msg->payload[msg->len], 1);
        msg->len += 1;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    if (args->chn <= IN_CHN_MAX)
    {
        msg->payload[msg->len++] = database_get(input_channel1_mute_index + args->chn)->u32 & 0x01;
    }
    else if (args->chn <= OUT_CHN_MAX)
    {
        args->chn -= OUT_CHN_MIN;
        msg->payload[msg->len++] = database_get(output_channel1_mute_ms_index + base + args->chn)->u32 & 0x01;
    }
    else if (args->chn == MIC_GLOBAL_MUTE)
    {
        msg->payload[msg->len++] = database_get(global_mic_mute_index)->i32;
    }
    else if (args->chn == OUT_GLOBAL_MUTE)
    {
        msg->payload[msg->len++] = database_get(global_out_mute_index)->i32;
    }
    else if (args->chn >= IN_MUTE_CHN17 && args->chn <= IN_MUTE_CHN24)
    {
        args->chn -= IN_MUTE_CHN17;
        msg->payload[msg->len++] = database_get(input_channel17_mute_index + args->chn)->i32;
    }
    else if (args->chn == ALL_SPEAKER_MUTE)
    {
        msg->payload[msg->len++] = database_get(global_skp_mute_index)->i32;
    }
    else if (args->chn == AEC_OUT_MUTE)
    {
        msg->payload[msg->len++] = database_get(global_aec_out_mute_index)->i32;
    }
    else if (args->chn == AFC_OUT_MUTE)
    {
        msg->payload[msg->len++] = database_get(global_afc_out_mute_index)->i32;
    }
    else if (args->chn == HW_PA_MUTE)
    {
        msg->payload[msg->len++] = database_get(global_hw_pa_mute_index)->i32;
    }
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_matrix_vol(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t ichn;
        uint8_t ochn;
        uint8_t vol;
    } __attribute__((packed)) set_matrix_vol_def;
    set_matrix_vol_def *args = (set_matrix_vol_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xA4;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 3, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    // 为了兼容之前的协议
    if (args->ichn == 0x10)
        args->ichn = YM_MATRIX_INPUT_AEC_OUTPUT_CHANNEL;
    else if (args->ichn == 0x11)
        args->ichn = YM_MATRIX_INPUT_AFC_OUTPUT_CHANNEL;
    else if (args->ichn > 0x11)
        args->ichn -= 2;
    database_value tmp = {.i32 = args->vol};

    if ((args->ichn != YM_MATRIX_INPUT_AEC_OUTPUT_CHANNEL) && (args->ichn != YM_MATRIX_INPUT_AFC_OUTPUT_CHANNEL))
    {
        database_write(args->ochn * (ochn1_ichn1_vol_unit_count) + ochn1_ichn1_vol_ms_index + MIXER1_BASE_INDEX + args->ichn, tmp);
        database_write(args->ochn * (ochn1_ichn1_vol_unit_count) + ochn1_ichn1_vol_ms_index + MIXER2_BASE_INDEX + args->ichn, tmp);
    }
    else
    {
        int index;
        index = args->ochn * (ochn1_ichn1_vol_unit_count) + ochn1_ichn1_vol_ms_index + base + args->ichn;
        database_write(index, tmp);
    }

    return NULL;
}

static void *get_matrix_vol(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t ichn;
        uint8_t ochn;
    } __attribute__((packed)) get_matrix_vol_def;
    get_matrix_vol_def *args = (get_matrix_vol_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xA5;
    a2b_comm.ackl = 5;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 2, &msg->payload[msg->len], 1);
        msg->len += 1;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    // 为了兼容之前的协议
    if (args->ichn == 0x10)
        args->ichn = YM_MATRIX_INPUT_AEC_OUTPUT_CHANNEL;
    else if (args->ichn == 0x11)
        args->ichn = YM_MATRIX_INPUT_AFC_OUTPUT_CHANNEL;
    else if (args->ichn > 0x11)
        args->ichn -= 2;
    int index = args->ochn * (ochn1_ichn1_vol_unit_count) + ochn1_ichn1_vol_ms_index + base + args->ichn;
    msg->payload[msg->len++] = database_get(index)->i32;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_matrix_mute(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t ichn;
        uint8_t ochn;
        uint8_t mute;
    } __attribute__((packed)) set_matrix_mute_def;
    set_matrix_mute_def *args = (set_matrix_mute_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xA6;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 3, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    /**
     * 同步普通通道参数，AEC AFC通道特殊处理且不同步
     */
    if (args->ichn == 0x10)
        args->ichn = YM_MATRIX_INPUT_AEC_OUTPUT_CHANNEL;
    else if (args->ichn == 0x11)
        args->ichn = YM_MATRIX_INPUT_AFC_OUTPUT_CHANNEL;
    else if (args->ichn > 0x11)
        args->ichn -= 2;

    if ((args->ichn == YM_MATRIX_INPUT_AEC_OUTPUT_CHANNEL) || (args->ichn == YM_MATRIX_INPUT_AFC_OUTPUT_CHANNEL))
    {
        database_value tmp = *database_get(ochn1_ichn_mute_ms_index + base + args->ochn);
        if (args->mute == 0)
        {
            if (args->ichn == YM_MATRIX_INPUT_AEC_OUTPUT_CHANNEL)
            {
                // 判断afc通道是否未取消静音，如果是，则将afc通道静音
                if (!(database_get(ochn1_ichn_mute_ms_index + base + args->ochn)->u32 & (1 << YM_MATRIX_INPUT_AFC_OUTPUT_CHANNEL)))
                {
                    tmp.u32 |= (1 << YM_MATRIX_INPUT_AFC_OUTPUT_CHANNEL);
                }
            }
            else
            {
                // 判断aec通道是否未取消静音，如果是，则将aec通道静音
                if (!(database_get(ochn1_ichn_mute_ms_index + base + args->ochn)->u32 & (1 << YM_MATRIX_INPUT_AEC_OUTPUT_CHANNEL)))
                {
                    tmp.u32 |= (1 << YM_MATRIX_INPUT_AEC_OUTPUT_CHANNEL);
                }
            }
        }
        tmp.u32 &= ~(1 << args->ichn);
        tmp.u32 |= ((args->mute ? 1 : 0) << args->ichn);
        database_write(ochn1_ichn_mute_ms_index + base + args->ochn, tmp);
    }
    else
    {
        // 需要同步的通道
        database_value master_matrix = *database_get(ochn1_ichn_mute_ms_index + MIXER1_BASE_INDEX + args->ochn);
        master_matrix.u32 &= ~(1 << args->ichn);
        master_matrix.u32 |= ((args->mute ? 1 : 0) << args->ichn);
        database_write(ochn1_ichn_mute_ms_index + MIXER1_BASE_INDEX + args->ochn, master_matrix);
        database_value slave_matrix = *database_get(ochn1_ichn_mute_ms_index + MIXER2_BASE_INDEX + args->ochn);
        slave_matrix.u32 &= ~(1 << args->ichn);
        slave_matrix.u32 |= ((args->mute ? 1 : 0) << args->ichn);
        database_write(ochn1_ichn_mute_ms_index + MIXER2_BASE_INDEX + args->ochn, slave_matrix);
    }
    return NULL;
}

static void *get_matrix_mute(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t ichn;
        uint8_t ochn;
    } __attribute__((packed)) get_matrix_mute_def;
    get_matrix_mute_def *args = (get_matrix_mute_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;
    database_value tmp = *database_get(ochn1_ichn_mute_ms_index + base + args->ochn);

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xA7;
    a2b_comm.ackl = 5;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 2, &msg->payload[msg->len], 1);
        msg->len += 1;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    // 为了兼容之前的协议
    if (args->ichn == 0x10)
        args->ichn = YM_MATRIX_INPUT_AEC_OUTPUT_CHANNEL;
    else if (args->ichn == 0x11)
        args->ichn = YM_MATRIX_INPUT_AFC_OUTPUT_CHANNEL;
    else if (args->ichn > 0x11)
        args->ichn -= 2;
    msg->payload[msg->len++] = (tmp.u32 >> args->ichn) & 0x01;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_mic_gain(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t node;
        uint8_t chn;
        uint8_t gain;
    } __attribute__((packed)) set_mic_gain_def;
    set_mic_gain_def *args = (set_mic_gain_def *)data;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xA8;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 3, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    if (args->node == 0)
    {
        database_value tmp;
        tmp.i32 = (uint8_t)(args->gain);
        database_write(input1_gain_index + args->chn, tmp);
    }
    return NULL;
}

static void *get_mic_gain(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t node;
        uint8_t chn;
    } __attribute__((packed)) get_mic_gain_def;
    get_mic_gain_def *args = (get_mic_gain_def *)data;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xA9;
    a2b_comm.ackl = 5;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 2, &msg->payload[msg->len], 1);
        msg->len += 1;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    if (args->node == 0)
    {
        database_value tmp = *database_get(input1_gain_index + args->chn);
        msg->payload[msg->len++] = (uint8_t)(tmp.i32);
    }
    else
    {
        msg->payload[msg->len++] = 0;
    }
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_outchn_type(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t chn;
        uint8_t type;
    } __attribute__((packed)) set_outchn_type_def;
    set_outchn_type_def *args = (set_outchn_type_def *)data;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xAA;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 2, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    if (args->type >= YM_MATRIX_MAX_OUTPUT_CHANNEL_TYPES)
    {
        args->type = YM_MATRIX_MAX_OUTPUT_CHANNEL_TYPES - 1;
    }
    database_value tmp = {.i32 = args->type};
    database_write(output_channel1_type_index + args->chn, tmp);
    return NULL;
}

static void *get_outchn_type(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t chn;
    } __attribute__((packed)) get_outchn_type_def;
    get_outchn_type_def *args = (get_outchn_type_def *)data;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xAB;
    a2b_comm.ackl = 5;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 1, &msg->payload[msg->len], 1);
        msg->len += 1;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    msg->payload[msg->len++] = database_get(output_channel1_type_index + args->chn)->u32;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_aec_mode(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t chn;
        uint8_t mode;
    } __attribute__((packed)) set_aec_mode_def;
    set_aec_mode_def *args = (set_aec_mode_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xAC;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 2, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    if (args->chn <= AEC_CHN_MAX)
    {
        database_value tmp = {.i32 = args->mode};
        database_write(args->chn + input_channel1_aec_mode_ms_index + base, tmp);
    }
    else if (args->chn <= ATMP_CHN_MAX)
    {
        args->chn -= ATMP_CHN_MIN;
        args->mode += YM_MIXER_ATM_IN_CHAN_DEF_PRIORITY;
        if (args->mode > YM_MIXER_ATM_IN_CHAN_MAX_PRIORITY)
        {
            args->mode = YM_MIXER_ATM_IN_CHAN_MAX_PRIORITY;
        }
        database_value tmp = {.i32 = args->mode};
        database_write(args->chn + input_channel1_atm_mode_ms_index + base, tmp);
    }
    else if (args->chn <= ATMF_CHN_MAX)
    {
        args->chn -= ATMF_CHN_MIN;
        if (args->mode > YM_MIXER_ATM_IN_CHAN_MAX_DECAYRATIO)
        {
            args->mode = YM_MIXER_ATM_IN_CHAN_MAX_DECAYRATIO;
        }
        database_value tmp = {.i32 = args->mode};
        database_write(args->chn + input_channel1_atm_decay_ms_index + base, tmp);
    }
    else if (args->chn >= INPUT_CHN17 && args->chn <= INPUT_CHN24)
    {
        database_value tmp = {.i32 = args->mode};
        args->chn -= INPUT_CHN17;
        database_write(input_channel17_aec_mode_ms_index + base + args->chn, tmp);
    }
    else if (args->chn >= ATMP_CHN17_MIN && args->chn <= ATMP_CHN24_MAX)
    {
        args->mode += YM_MIXER_ATM_IN_CHAN_DEF_PRIORITY;
        if (args->mode > YM_MIXER_ATM_IN_CHAN_MAX_PRIORITY)
        {
            args->mode = YM_MIXER_ATM_IN_CHAN_MAX_PRIORITY;
        }
        args->chn -= ATMP_CHN17_MIN;
        database_value tmp = {.i32 = args->mode};
        database_write(input_channel17_atm_mode_ms_index + base + args->chn, tmp);
    }
    else if (args->chn >= ATMF_CHN17_MIN && args->chn <= ATMF_CHN24_MAX)
    {
        if (args->mode > YM_MIXER_ATM_IN_CHAN_MAX_DECAYRATIO)
        {
            args->mode = YM_MIXER_ATM_IN_CHAN_MAX_DECAYRATIO;
        }
        args->chn -= ATMF_CHN17_MIN;
        database_value tmp = {.i32 = args->mode};
        database_write(input_channel17_atm_decay_ms_index + base + args->chn, tmp);
    }
    return NULL;
}

static void *get_aec_mode(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t chn;
    } __attribute__((packed)) get_aec_mode_def;
    get_aec_mode_def *args = (get_aec_mode_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xAD;
    a2b_comm.ackl = 5;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 1, &msg->payload[msg->len], 1);
        msg->len += 1;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    if (args->chn <= AEC_CHN_MAX)
    {
        msg->payload[msg->len++] = database_get(input_channel1_aec_mode_ms_index + base + args->chn)->u32;
    }
    else if (args->chn <= ATMP_CHN_MAX)
    {
        args->chn -= ATMP_CHN_MIN;
        msg->payload[msg->len++] = database_get(input_channel1_atm_mode_ms_index + base + args->chn)->i32 - YM_MIXER_ATM_IN_CHAN_DEF_PRIORITY;
    }
    else if (args->chn <= ATMF_CHN_MAX)
    {
        args->chn -= ATMF_CHN_MIN;
        msg->payload[msg->len++] = database_get(input_channel1_atm_decay_ms_index + base + args->chn)->i32;
    }
    else if (args->chn >= INPUT_CHN17 && args->chn <= INPUT_CHN24)
    {
        args->chn -= INPUT_CHN17;
        msg->payload[msg->len++] = database_get(input_channel17_aec_mode_ms_index + base + args->chn)->u32;
    }
    else if (args->chn >= ATMP_CHN17_MIN && args->chn <= ATMP_CHN24_MAX)
    {
        args->chn -= ATMP_CHN17_MIN;
        msg->payload[msg->len++] = database_get(input_channel17_atm_mode_ms_index + base + args->chn)->i32 - YM_MIXER_ATM_IN_CHAN_DEF_PRIORITY;
    }
    else if (args->chn >= ATMF_CHN17_MIN && args->chn <= ATMF_CHN24_MAX)
    {
        args->chn -= ATMF_CHN17_MIN;
        msg->payload[msg->len++] = database_get(input_channel17_atm_decay_ms_index + base)->u32;
    }
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_inchn_ducker(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t chn;
        uint8_t duc;
    } __attribute__((packed)) set_inchn_ducker_def;
    set_inchn_ducker_def *args = (set_inchn_ducker_def *)data;
    if (args->chn > 23)
        return NULL;

    if (args->duc >= YM_MATRIX_MAX_INPUT_CHANNEL_DUCKER_MODES)
    {
        args->duc = YM_MATRIX_MAX_INPUT_CHANNEL_DUCKER_MODES - 1;
    }

    database_value tmp = {.i32 = args->duc};
    database_write(args->chn + input_channel1_ducker_index, tmp);
    return NULL;
}

static void *get_inchn_ducker(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t chn;
    } __attribute__((packed)) get_inchn_ducker_def;
    get_inchn_ducker_def *args = (get_inchn_ducker_def *)data;

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    if (args->chn > 23)
    {
        msg->payload[msg->len++] = 0;
    }
    else
    {
        msg->payload[msg->len++] = database_get(input_channel1_ducker_index + args->chn)->i32;
    }
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_test_signal(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t sig;
        uint16_t val;
    } __attribute__((packed)) set_test_signal_def;
    set_test_signal_def *args = (set_test_signal_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;
    args->val = htobe16(args->val);
    switch (args->sig)
    {
    case TEST_SIG_TYPE:
        if (args->val < YM_MATRIX_TEST_MAX_NOISE_TYPES)
        {
            database_value tmp;
            tmp.i32 = args->val;
            database_write(test_sig_type_ms_index + base, tmp);
        }
        break;
    case TEST_SIG_FREQ:
    {
        int16_t freq = (int16_t)args->val;
        freq = freq < 20 ? 20 : freq;
        freq = freq > 20000 ? 20000 : freq;
        database_value tmp;
        tmp.i32 = freq;
        database_write(test_sig_freq_ms_index + base, tmp);
    }
    break;
    case TEST_SIG_AMP:
    {
        int16_t amp = (int16_t)args->val;
        amp = amp < 10 * YM_MATRIX_TEST_SIGNAL_MIN_LEVEL_DB ? 10 * YM_MATRIX_TEST_SIGNAL_MIN_LEVEL_DB : amp;
        amp = amp > 10 * YM_MATRIX_TEST_SIGNAL_MAX_LEVEL_DB ? 10 * YM_MATRIX_TEST_SIGNAL_MAX_LEVEL_DB : amp;
        database_value tmp;
        tmp.i32 = amp;
        database_write(test_sig_amp_ms_index + base, tmp);
    }
    break;
    default:
        break;
    }
    return NULL;
}

static void *get_test_signal(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t sig;
    } __attribute__((packed)) get_test_signal_def;
    get_test_signal_def *args = (get_test_signal_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;
    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    int16_t *tmp = (int16_t *)&msg->payload[msg->len];
    switch (args->sig)
    {
    case TEST_SIG_TYPE:
        *tmp = database_get(test_sig_type_ms_index + base)->i32;
        *tmp = htobe16(*tmp);
        break;
    case TEST_SIG_FREQ:
        *tmp = database_get(test_sig_freq_ms_index + base)->i32;
        *tmp = htobe16(*tmp);
        break;
    case TEST_SIG_AMP:
        *tmp = database_get(test_sig_amp_ms_index + base)->i32;
        *tmp = htobe16(*tmp);
        break;
    default:
        break;
    }
    msg->len += 2;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

extern int otg_upgrade(int argc, char **argv);
static void *restore_settings(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t cata;
        uint8_t node;
    } __attribute__((packed)) restore_settings_def;
    restore_settings_def *args = (restore_settings_def *)data;
    int ret = 0;
    // TODO:
#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xB2;
    a2b_comm.ackl = 5;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, &args->cata, 1, &msg->payload[msg->len], 1);

        msg->len++;
        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    (void)args;
    switch (args->cata)
    {
    case 0x00:
        // 恢复固件
        break;
    case 0x01:
        // 重置当前参数：该命令会重置 Mixer当前参数，但不会更改 Flash中保存的参数，重置之后系统会自动重启， 并使用 当前固件版本默认参数 。
        pause_mixer();
        usleep(1000 * 10);
        ret |= system("rm -rf /appcfg/mode-index");
        ret |= system("rm -rf /appcfg/curr-mode");
        ret |= system("reboot -f");
        break;
    case 0x02:
        // 重置当前参数：该命令会重置，并保存
        pause_mixer();
        usleep(1000 * 10);
        ret |= system("rm -rf /appcfg/*");
        ret |= system("sync;sync");
        ret |= system("reboot -f");
        break;
    case 0xff: // upgrade image
        // otg_upgrade(0, NULL);
        break;
    default:
        break;
    }
    if (ret)
    {
        // log_e("restore_settings failed.");
    }

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    msg->payload[msg->len++] = 0;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *reset_self(uint8_t device, uint8_t *data)
{
    // TODO:
#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xB3;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, NULL, 0, NULL, 0);
        return NULL;
    }
#if 1 // cancel rese salves in case of 3308 can not discovery slave 2~4 after reboot, @2025/7/2
    else // reset all slaves
    {
        a2b_comm.core = 0x01;
        for (int i = 1; database_get(a2b_slave_nodes_index)->i32 >= i; i++)
        {
            a2b_comm.node = i;
            bsp_ad2428_s_comm(&a2b_comm, NULL, 0, NULL, 0);
            usleep(10000);
        }
    }
#endif
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    database_value tmp;
    tmp.i32 = 1;
    database_write_no_callback(global_out_mute_index, tmp);
    usleep(100000);
    // reboot self
    printf("reboot...");
    pause_mixer();
    usleep(100000);
    if(system("reboot -f"))
        printf("run reboot command failed!!!\n");

    return NULL;
}

static void *get_sys_state(uint8_t device, uint8_t *data)
{
    enum
    {
        STAT_SIG = (0x00), /* get signal */
        STAT_CPU = (0x01), /* get cpu load */
        STAT_THD = (0x02), /* get input thd */
        STAT_SNR = (0x03), /* get noise, reverb, SNR */
        STAT_SIG2 = (0x04),
        STAT_THD2 = (0x05),
        STAT_MICL = (0x06), /* get all mic signal level */
        GET_SOC_TEMP = (0x09),
    };

    typedef struct
    {
        uint8_t cata;
        uint8_t aec;
    } __attribute__((packed)) get_sys_state_def;
    get_sys_state_def *args = (get_sys_state_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;
    extern void *ym_mixer_master_handle;
    extern void *ym_mixer_slave_handle;
    void *ym_mixer_handle = device == 0x01 ? ym_mixer_master_handle : ym_mixer_slave_handle;
    latency_e latency = (device == 0x01) ? LATENCY_5MS : LATENCY_10MS;
    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);

    switch (args->cata)
    {
    case STAT_SIG:
    {
        {
            const database_value *pmixer_sig = database_get(mixer_input_channel1_sig_db_ms_index + base);
            for (size_t i = 0; i < 16; i++)
            {
                if (i < MIXER_INPUT_CHN_MAX)
                {
                    msg->payload[msg->len++] = (uint8_t)((pmixer_sig[i].i32) / YM_MATRIX_FRAME_DB_QUANT);
                }
                else
                {
                    msg->payload[msg->len++] = 96;
                }
            }
        }
        {
            const database_value *pmixer_sig = database_get(mixer_output_channel1_sig_db_ms_index + base);
            for (size_t i = 0; i < 16; i++)
            {
                if (i < MIXER_OUTPUT_CHN_MAX)
                {
                    msg->payload[msg->len++] = (uint8_t)((pmixer_sig[i].i32) / YM_MATRIX_FRAME_DB_QUANT);
                }
                else
                {
                    msg->payload[msg->len++] = 96;
                }
            }
        }
    }
    break;
    case STAT_CPU:
    {
        msg->payload[msg->len++] = (uint8_t)(database_get(cpu_load_ms_index + base)->i32);
    }
    break;
    case STAT_THD:
    {
        mixer_lib_lock(base == MIXER1_BASE_INDEX ? 1 : 2);
        unsigned char *thd = mixer_get_input_thdDB[latency](ym_mixer_handle);
        mixer_lib_unlock(base == MIXER1_BASE_INDEX ? 1 : 2);
        for (size_t i = 0; i < 16; i++)
        {
            if (i < MIXER_INPUT_CHN_MAX)
            {
                msg->payload[msg->len++] = (uint8_t)thd[i];
            }
            else
            {
                msg->payload[msg->len++] = 0;
            }
        }
    }
    break;
    case STAT_SNR:
    {
        size_t index = msg->len;
        mixer_lib_lock(base == MIXER1_BASE_INDEX ? 1 : 2);
        msg->payload[index] = mixer_getNoiseLevel_SNR_rt60[latency](ym_mixer_handle,
                                                           args->aec,
                                                           (char *)&(msg->payload[index + 1]),
                                                           (char *)&(msg->payload[index + 2]));
        mixer_lib_unlock(base == MIXER1_BASE_INDEX ? 1 : 2);
        log_i("STAT_SNR:%d\t%d\t%d", msg->payload[index], msg->payload[index + 1], msg->payload[index + 2]);
        msg->len += 3;
    }
    break;
    case STAT_SIG2:
    {
        const database_value *pmixer_sig = database_get(mixer_input_channel17_sig_db_ms_index + base);
        for (size_t i = 0; i < 8; i++)
        {
            msg->payload[msg->len++] = (uint8_t)((pmixer_sig[i].i32) / YM_MATRIX_FRAME_DB_QUANT);
        }
    }
    break;
    case STAT_THD2:
    {
        if (MIXER_INPUT_CHN_MAX < 17)
        {
            for (size_t i = 0; i < 8; i++)
                msg->payload[msg->len++] = 0;
        }
        else
        {
            mixer_lib_lock(base == MIXER1_BASE_INDEX ? 1 : 2);
            unsigned char *thd = mixer_get_input_thdDB[latency](ym_mixer_handle);
            mixer_lib_unlock(base == MIXER1_BASE_INDEX ? 1 : 2);
            for (size_t i = 16; i < 24; i++)
            {
                if (i < MIXER_INPUT_CHN_MAX)
                {
                    msg->payload[msg->len++] = (uint8_t)thd[i];
                }
                else
                {
                    msg->payload[msg->len++] = 0;
                }
            }
        }
    }
    break;
    case STAT_MICL:
    {
#ifdef BEAM_ENABLE
    int beam_id = 0; /* default: BD_MIXER_6_1MIC_ARRAY || BD_MIXER_WX_128 || BD_MIXER_YOU_YAN_6_1MIC */
#if defined(BD_MIXER_YO_8MIC)
    beam_id = YM_PROTO_CORE_ID(device) - 1;
#elif defined(BD_MIXER_LINE_MICS)
    beam_id = YM_PROTO_SLAVE_ID(device) + ((YM_PROTO_CORE_ID(device) == 2) ? BEAM_INSTANCE_NUMBER : (uint8_t)(0u));
#endif /* BD_MIXER_YO_8MIC */

    int32_t beam_in_signal[BEAM_IN_CHANNELS];
    beam_get_ch_vol(pbeam_handle[beam_id]->handle, beam_in_signal);
    for (uint32_t i = 0; i < BEAM_IN_CHANNELS; i++)
    {
        msg->payload[msg->len++] = (uint8_t)(-beam_in_signal[i]);
    }
#endif /* BEAM_ENABLE */
    }
    break;
    case GET_SOC_TEMP:
    {
        short *val = (short *)&msg->payload[msg->len];
        *val = htobe16((short)(database_get(soc_temp_index)->i32));
        msg->len += 2;
    }
    default:
        break;
    }
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
    // __err:
    //     FREE_YMLINK_MESSAGE();
}

static void *save_tune_mode(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t mode;
    } __attribute__((packed)) save_tune_mode_def;
    save_tune_mode_def *args = (save_tune_mode_def *)data;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xB5;
    a2b_comm.ackl = 5;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 1, &msg->payload[msg->len++], 1);

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    uint32_t tmp = gconfig_ctrl.mode;
    gconfig_ctrl.mode = args->mode;
    if (store_mode() != 0)
    {
        log_e("store mode err.");
        msg->payload[msg->len++] = 1;
    }
    else
    {
        msg->payload[msg->len++] = 0;
    }
    gconfig_ctrl.mode = tmp;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_tune_mode(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t mode;
        uint8_t loop;
    } __attribute__((packed)) set_tune_mode_def;
    extern void *ym_mixer_master_handle;
    set_tune_mode_def *args = (set_tune_mode_def *)data;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xB6;
    a2b_comm.ackl = 5;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 2, &msg->payload[msg->len++], 1);

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    latency_e latency = (device == 0x01) ? LATENCY_5MS : LATENCY_10MS;

    if (args->mode == 0xfd)
    {
        gconfig_ctrl.tune_mode_loop_max = args->loop;
        msg->payload[msg->len++] = (uint8_t)mode_index_set(gconfig_ctrl.mode);
    }
    else if (args->mode == 0xff)
    {
        if (gconfig_ctrl.tune_mode_loop_max)
        {
            gconfig_ctrl.mode = (gconfig_ctrl.mode + 1) % gconfig_ctrl.tune_mode_loop_max;
            mode_index_set(gconfig_ctrl.mode);

            // 打开模式配置文件
            // 如果模式配置文件存在，则初始化mixer参数
            pause_mixer();
            usleep(1000 * 10);
            database_value tmp;
            tmp.i32 = database_get(global_tune_mode_vol_index)->i32;
            mixer_set_trailer_mode[latency](ym_mixer_master_handle, gconfig_ctrl.mode);
            msg->payload[msg->len++] = set_mixer_mode(gconfig_ctrl.mode);
#if defined(WEN_XIANG_BOARD)
            if (wm520_dev_plugin_count_get() > 1)
            {
                double_wm520_params_config();
            }
            else if (database_get(wen_xiang_bt_count_index)->i32 > 1)
            {
                wm520_sd20pro_params_restore(1);
            }
#elif defined(BD_MIXER_YO_8MIC) && defined(COMM_WITH_MIC_MATRIX_BY_A2B)
            sys_params_adjust_according_to_a2b_slaves(database_get(a2b_slave_nodes_index)->i32);
#endif /* end WEN_XIANG_BOARD */
            database_write(global_tune_mode_vol_index, tmp);
            resume_mixer();
        }
        else
        {
            msg->payload[msg->len++] = 0x01;
        }
    }
    else if (YM_MIXER_MAX_MODE > args->mode)
    {
        // 打开模式配置文件
        // 如果模式配置文件存在，则初始化mixer参数
        pause_mixer();
        usleep(1000 * 10);
        database_value tmp;
        tmp.i32 = database_get(global_tune_mode_vol_index)->i32;
        mixer_set_trailer_mode[latency](ym_mixer_master_handle, args->mode);
        msg->payload[msg->len++] = set_mixer_mode(args->mode);
#if defined(WEN_XIANG_BOARD)
        if (wm520_dev_plugin_count_get() > 1)
        {
            double_wm520_params_config();
        }
        else if (database_get(wen_xiang_bt_count_index)->i32 > 1)
        {
            wm520_sd20pro_params_restore(1);
        }
#elif defined(BD_MIXER_YO_8MIC) && defined(COMM_WITH_MIC_MATRIX_BY_A2B)
        sys_params_adjust_according_to_a2b_slaves(database_get(a2b_slave_nodes_index)->i32);
#endif /* end WEN_XIANG_BOARD */
        database_write(global_tune_mode_vol_index, tmp);
        resume_mixer();
    }
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *get_tune_mode(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t mode;
    } __attribute__((packed)) get_tune_mode_def;
    get_tune_mode_def *args = (get_tune_mode_def *)data;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xB7;
    a2b_comm.ackl = 5;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 1, &msg->payload[msg->len++], 1);

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    if (args->mode == 0xfd)
    {
        msg->payload[msg->len++] = (uint8_t)gconfig_ctrl.tune_mode_loop_max;
    }
    else
    {
        msg->payload[msg->len++] = gconfig_ctrl.mode;
    }
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_module_en(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t type;
        uint8_t endis;
    } __attribute__((packed)) set_module_en_def;
    set_module_en_def *args = (set_module_en_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;
    int index = 0;
    database_value tmp = {.i32 = args->endis};

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xB8;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 2, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    if (args->type <= 0x0D)
    {
        index = args->type * YM_MATRIX_MAX_GAIN_CONTROL_PARAMS +
                YM_MATRIX_GAIN_CONTROL_ON_OFF +
                mic_noise_gate_gain_control_on_off_index;
    }
    else if (args->type <= 0x12) // GEQ
    {
        args->type -= 0x0E;
        index = args->type * 66 + geq_mic_onoff_index;
    }
    else if (args->type < 0x18) // PEQ
    {
        args->type -= 0x13;
        index = args->type * 34 + peq_mic_onoff_index;
    }
    else if (args->type == 0x18)
    {
        // TODO:
        database_value tmp = {.i32 = args->endis};
        database_write(peq_anf_0_onoff_ms_index + base, tmp);
        database_write(peq_anf_1_onoff_ms_index + base, tmp);
        database_write(peq_anf_2_onoff_ms_index + base, tmp);
        for (size_t i = 0; i < 8; i++)
        {
            if (database_get(peq_anf_2_gain1_ms_index + base + i)->i32 < 0)
            {
                tmp.i32 = 1;
            }
        }
        if (args->endis > 1)
            tmp.i32 = args->endis - 2;
        database_write(peq_anf_2_onoff_ms_index + base, tmp);

        return NULL;
    }
    else if (args->type == 0x1A)
    {
        index = mic_aec1_auto_mixer_gain_control_on_off_index;
    }
    else if (args->type == 0x1B)
    {
        index = mic_aec0_auto_mixer_gain_control_on_off_index;
    }
#ifdef BEAM_ENABLE
    else if (args->type == 0x1C)
    {
        int32_t offset = 0;
#if defined(BD_MIXER_LINE_MICS) || defined(BD_MIXER_YO_8MIC)
        offset = (YM_PROTO_CORE_ID(device) == 1) ? 0u : (BEAM_INSTANCE_NUMBER * BEAM_PARAM_MAX);
#endif
        index = peq_beamf_onoff_index + BEAM_BASE_INDEX + offset;
    }
#endif /* BEAM_ENABLE */
    else if (args->type == 0x1D)
    {
        index = peq_aec_1_onoff_ms_index + base;
    }
    else if (args->type == 0x1E)
    {
        index = peq_aec_2_onoff_ms_index + base;
    }
    database_write(index, tmp);
    return NULL;
}

static void *get_module_en(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t type;
    } __attribute__((packed)) get_module_en_def;
    get_module_en_def *args = (get_module_en_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xB9;
    a2b_comm.ackl = 5;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 1, &msg->payload[msg->len++], 1);

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    if (args->type <= 0x0D)
    {
        int index = args->type * YM_MATRIX_MAX_GAIN_CONTROL_PARAMS +
                    YM_MATRIX_GAIN_CONTROL_ON_OFF +
                    mic_noise_gate_gain_control_on_off_index;
        msg->payload[msg->len++] = database_get(index)->i32;
    }
    else if (args->type <= 0x12)
    {
        args->type -= 0x0E;
        msg->payload[msg->len++] = database_get(args->type * 66 + geq_mic_onoff_index)->i32;
    }
    else if (args->type < 0x18)
    {
        args->type -= 0x13;
        msg->payload[msg->len++] = database_get(args->type * 34 + peq_mic_onoff_index)->i32;
    }
    else if (args->type == 0x18)
    {
        msg->payload[msg->len++] = database_get(peq_anf_0_onoff_ms_index + base)->i32;
    }
    else if (args->type == 0x1A)
    {
        msg->payload[msg->len++] = database_get(mic_aec1_auto_mixer_gain_control_on_off_index)->i32;
    }
    else if (args->type == 0x1B)
    {
        msg->payload[msg->len++] = database_get(mic_aec0_auto_mixer_gain_control_on_off_index)->i32;
    }
#ifdef BEAM_ENABLE
    else if (args->type == 0x1C)
    {
        int32_t offset = 0;
#if defined(BD_MIXER_LINE_MICS) || defined(BD_MIXER_YO_8MIC)
        offset = (YM_PROTO_CORE_ID(device) == 1) ? 0u : (BEAM_INSTANCE_NUMBER * BEAM_PARAM_MAX);
#endif
        msg->payload[msg->len++] = database_get(peq_beamf_onoff_index + BEAM_BASE_INDEX + offset)->i32;
    }
#endif /* BEAM_ENABLE */
    else if (args->type == 0x1D)
    {
        msg->payload[msg->len++] = database_get(peq_aec_1_onoff_ms_index + base)->i32;
    }
    else if (args->type == 0x1E)
    {
        msg->payload[msg->len++] = database_get(peq_aec_2_onoff_ms_index + base)->i32;
    }
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

// #if defined(PROTOCOL_SERIAL_PORT_BYPASS)
// extern void serial_port_bypass_flag_set(int flag);
// extern int serial_port_bypass_flag_get(void);
// extern void serial_port_protocol_fd_set(int fd);
// extern int serial_port_protocol_fd_get(void);
// extern void serial_port_protocol_232_fd_set(int fd);
// extern int serial_port_protocol_232_fd_get(void);
// extern void serial_port_bypass_if_fd_set(int fd);
// extern int serial_port_bypass_if_fd_get(void);
// #endif /* end PROTOCOL_SERIAL_PORT_BYPASS */
static void *set_sync_state(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t type;
        uint8_t val;
    } __attribute__((packed)) set_sync_state_def;
    set_sync_state_def *args = (set_sync_state_def *)data;
    enum
    {
        MIXER_PARAM = (0x00), /* save current parameters */
        SAVE_ENABLE = (0x01), /* save current parameters */
        SAVE_PARAM = (0x02),  /* save current parameters */
        MIC_DETECT = (0x03),  /* save current parameters */
        DSP_STATE = (0x04),   /* save current parameters */
        USB_FLASH = (0x05),   // U盘录音指令
        LOG_EXPORT = (0x06),  // 日志导出
        DATA_BYPASS = 0xff
    };
    switch (args->type)
    {
    case MIXER_PARAM:
        break;
    case SAVE_ENABLE:
        break;
    case SAVE_PARAM:
        store_param();
        break;
    case MIC_DETECT:
        break;
    case DSP_STATE:
        break;
    case USB_FLASH:
        database_write(usb_flash_record_status_index, (database_value){.i32 = args->val});
        break;
    case LOG_EXPORT:
        if (args->val == 0)
            ym_log_trans_stop();
        else
            ym_log_trans_start();
        break;
    case DATA_BYPASS:
#ifdef PROTOCOL_SERIAL_PORT_BYPASS
        log_i("protocol serial is bypass mode");
        serial_port_bypass_flag_set(1); /* bypass and power off wmic */
#ifdef BD_MIXER_YO_8MIC
        void gpio_wmic_power_onoff_set(int level);
        gpio_wmic_power_onoff_set(0);
#endif /* BD_MIXER_YO_8MIC */
#endif /* PROTOCOL_SERIAL_PORT_BYPASS */
        break;
    default:
        break;
    }
    return NULL;
}

static void *get_sync_state(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t type;
        uint8_t val;
    } __attribute__((packed)) get_sync_state_def;
    get_sync_state_def *args = (get_sync_state_def *)data;
    enum
    {
        MIXER_PARAM = (0x00), /* save current parameters */
        SAVE_ENABLE = (0x01), /* save current parameters */
        SAVE_PARAM = (0x02),  /* save current parameters */
        MIC_DETECT = (0x03),  /* save current parameters */
        DSP_STATE = (0x04),   /* save current parameters */
        USB_FLASH = (0x05),   // U盘录音指令
        LOG_EXPORT = (0x06),  // 日志导出
    };
    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);

    switch (args->type)
    {
    case MIXER_PARAM:
        msg->payload[msg->len++] = 0;
        break;
    case SAVE_ENABLE:
        msg->payload[msg->len++] = 0;
        break;
    case SAVE_PARAM:
        msg->payload[msg->len++] = 0;
        break;
    case MIC_DETECT:
        msg->payload[msg->len++] = 0;
        break;
    case DSP_STATE:
        msg->payload[msg->len++] = 0;
        break;
    case USB_FLASH:
        msg->payload[msg->len++] = database_get(usb_flash_record_status_index)->i32;
        break;
    case LOG_EXPORT:
    {
        int len = ym_log_trans_get(&msg->payload[msg->len], 255);
        msg->len += len;
    }
    break;
    default:
        break;
    }
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

enum
{
    IO_DELAY_MIC = (0x00),
    IO_DELAY_AUX = (0x01),
    IO_DELAY_OUT_MIN = (0x02),
    IO_DELAY_OUT_MAX = (0x11),
    IO_DELAY_BGM = (0x12), /* backgroud music */
    IO_DELAY_IN_MIN = (0x13),
    IO_DELAY_IN_MAX = (0x2A),
};

static void *set_io_delay(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t chn;
        uint16_t nms;
    } __attribute__((packed)) set_io_delay_def;
    set_io_delay_def *args = (set_io_delay_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;
    args->nms = htobe16(args->nms);
    if (args->chn == IO_DELAY_MIC)
    {
        database_value tmp = {.i32 = args->nms};
        database_write(global_mic_delay_index, tmp);
    }
    else if (args->chn == IO_DELAY_AUX)
    {
        database_value tmp = {.i32 = args->nms};
        database_write(global_aux_delay_index, tmp);
    }
    else if (args->chn <= IO_DELAY_OUT_MAX)
    {
        args->chn -= IO_DELAY_OUT_MIN;
        database_value tmp = {.i32 = args->nms};
        database_write(output_channel1_delay_ms_index + base + args->chn, tmp);
    }
    else if (args->chn == IO_DELAY_BGM)
    {
        database_value tmp = {.i32 = args->nms};
        database_write(global_bgm_delay_index, tmp);
    }
    else if (args->chn <= IO_DELAY_IN_MAX)
    {
        args->chn -= IO_DELAY_IN_MIN;
        database_value tmp = {.i32 = args->nms};
        database_write(input_channel1_delay_ms_index + base + args->chn, tmp);
    }
    return NULL;
}

static void *get_io_delay(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t chn;
    } __attribute__((packed)) get_io_delay_def;
    get_io_delay_def *args = (get_io_delay_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;
    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    short *val = (short *)&msg->payload[msg->len];
    if (args->chn == IO_DELAY_MIC)
    {
        *val = database_get(global_mic_delay_index)->i32;
    }
    else if (args->chn == IO_DELAY_AUX)
    {
        *val = database_get(global_aux_delay_index)->i32;
    }
    else if (args->chn <= IO_DELAY_OUT_MAX)
    {
        args->chn -= IO_DELAY_OUT_MIN;
        *val = database_get(output_channel1_delay_ms_index + base + args->chn)->u32;
    }
    else if (args->chn == IO_DELAY_BGM)
    {
        *val = database_get(global_bgm_delay_index)->i32;
    }
    else if (args->chn <= IO_DELAY_IN_MAX)
    {
        args->chn -= IO_DELAY_IN_MIN;
        *val = database_get(input_channel1_delay_ms_index + base + args->chn)->u32;
    }
    *val = htobe16(*val);
    msg->len += 2;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_out_invt(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t chn;
        uint8_t inv;
    } __attribute__((packed)) set_out_invt_def;
    set_out_invt_def *args = (set_out_invt_def *)data;
    database_value tmp = {.i32 = args->inv};
    database_write(output_channel1_phase_invert_index + args->chn, tmp);
    return NULL;
}

static void *get_out_invt(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t chn;
    } __attribute__((packed)) get_out_invt_def;
    get_out_invt_def *args = (get_out_invt_def *)data;

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    msg->payload[msg->len++] = database_get(output_channel1_phase_invert_index + args->chn)->i32;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *get_version(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t type;
        uint8_t node;
    } __attribute__((packed)) get_version_def;
    enum
    {
        FIRMWARE = (0x00),
        HARDWARE = (0x01),
        BUILD_TIME = (0x02),
        RUN_TIME = (0x03),
        HW_ADC = (0x04),
        MIXER_ID = (0x05),
#ifdef BEAM_ENABLE
        BEAM_ID = (0x06),
#endif
    };
    get_version_def *args = (get_version_def *)data;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xC0;
    a2b_comm.ackl = 8;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, &args->type, 1, &msg->payload[msg->len], 4);
        msg->len += 4;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    uint32_t *val = (uint32_t *)&msg->payload[msg->len];
    char *version;
    uint32_t len;
    switch (args->type)
    {
    case FIRMWARE:
        msg->payload[msg->len++] = 0xCC;
        msg->payload[msg->len++] = YM_VERSION;
        msg->payload[msg->len++] = YM_SUBVERSION;
        msg->payload[msg->len++] = YM_REVISION;
        break;
    case HARDWARE:
#ifdef BD_MIXER_JYD_8MIC
        int32_t value = interface_board_version_get();
        *val = htobe32(YM_HARDWARE_VERSION | ((value == 0) ? INTERFACE_BOARD_LITE : INTERFACE_BOARD_FULL));
#else
        *val = htobe32(YM_HARDWARE_VERSION);
#endif
        msg->len += 4;
        break;
    case BUILD_TIME:
        struct tm *tm = convertDateTime(__DATE__, __TIME__);
        msg->payload[msg->len++] = tm->tm_year - 100;
        msg->payload[msg->len++] = tm->tm_mon + 1;
        msg->payload[msg->len++] = tm->tm_mday;
        msg->payload[msg->len++] = tm->tm_hour;
        break;
    case RUN_TIME:
        extern uint32_t start_time;
        *val = time(NULL) - start_time;
        *val = htobe32(*val);
        msg->len += 4;
        break;
    case HW_ADC:
        *val = htobe32(hwid_adc_subversion);
        msg->len += 4;
        break;
    case MIXER_ID:
        version = mixer_query_version[0]();
        len = strlen(version);
        msg->payload[msg->len++] = 0xCC;
        msg->payload[msg->len++] = version[len - 5] - '0';
        msg->payload[msg->len++] = version[len - 3] - '0';
        msg->payload[msg->len++] = version[len - 1] - '0';
        break;
#ifdef BEAM_ENABLE
    case BEAM_ID:
        version = beam_query_version();
        len = strlen(version);
        msg->payload[msg->len++] = 0xCC;
        msg->payload[msg->len++] = version[len - 5] - '0';
        msg->payload[msg->len++] = version[len - 3] - '0';
        msg->payload[msg->len++] = version[len - 1] - '0';
        break;
#endif
    default:
        break;
    }
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

FILE *upgrade_file = NULL; // 用于接收升级固件
static uint8_t bank_num;

static void *firmware_start(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t mver;
        uint8_t sver;
    } __attribute__((packed)) firmware_start_def;
    POSSIBLY_UNUSED firmware_start_def *args = (firmware_start_def *)data;

    uint8_t ret = 0;

    log_i("start download firmware,mver: %d,sver: %d", args->mver, args->sver);

    bank_num = 1;

    /**
     * 创建接收文件，如果已经创建则fseek初始位置重新写入。
     */
    if (upgrade_file)
    {
        rewind(upgrade_file);
        log_i("rewind /data/upgarde file.");
    }
    else
    {
        upgrade_file = fopen("/data/upgrade", "wb+");
        if (!upgrade_file)
        {
            ret = 1;
            log_e("can't open /data/upgarde file.");
        }
    }

    if (upgrade_file)
        database_write(firmware_upgrading_index, (database_value)1);

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    msg->payload[msg->len++] = ret;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *firmware_bank(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t bank;
        uint8_t sum;
        uint8_t len;
        // uint8_t dat[];
    } __attribute__((packed)) firmware_bank_def;
    const firmware_bank_def *args = (firmware_bank_def *)data;
    const uint8_t *dat = (uint8_t *)(data + sizeof(firmware_bank_def));

    int ret = 0;
    uint32_t sum = args->len;

    // 写入升级文件
    if (upgrade_file)
    {
        // bank num check
        if (bank_num != args->bank)
        {
            log_e("bank num cheak err,bank:%d\t%d", bank_num, args->bank);
            ret = 1;
        }
        else
        {
            bank_num++;
            if (bank_num == 0)
                bank_num++;
            // 校验:SUM = (Data Length + DATA[0] + DATA[1] + … + DATA[N-1]) % 0xFF
            for (size_t i = 0; i < args->len; i++)
                sum = (sum + dat[i]) % 0xff;
            if ((sum & 0xff) != args->sum)
            {
                log_e("firmware bank:%d sum check err. sum:%d \t recv:%d", args->bank, sum, args->sum);
                ret = 1;
            }
            else
            {
                ret = fwrite(dat, 1, args->len, upgrade_file);
                if (ret != args->len)
                {
                    log_e("firmware bank write err:%d", ret);
                    ret = 1;
                }
                else
                {
                    ret = 0;
                }
                // log_i("bank:%d succeed.", args->bank);
            }
        }
    }
    else
    {
        ret = 1;
        log_e("upgrade_file is null");
    }

    if (ret)
        database_write(firmware_upgrading_index, (database_value)0);
    else
        database_write(firmware_upgrading_index, (database_value)1);

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    msg->payload[msg->len++] = args->bank;
    msg->payload[msg->len++] = ret;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_module_threshold(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t mod;
        uint16_t targ;
    } __attribute__((packed)) set_module_threshold_def;
    set_module_threshold_def *args = (set_module_threshold_def *)data;
    int16_t val = htobe16(args->targ);

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xC4;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 3, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    val = val < 10 * YM_MATRIX_GAIN_THRESHOLD_TARGET_MIN_DB ? 10 * YM_MATRIX_GAIN_THRESHOLD_TARGET_MIN_DB : val;
    val = val > 10 * YM_MATRIX_GAIN_THRESHOLD_TARGET_MAX_DB ? 10 * YM_MATRIX_GAIN_THRESHOLD_TARGET_MAX_DB : val;
    int index = 0;
    if (args->mod <= 0x0D)
    {
        index = args->mod * YM_MATRIX_MAX_GAIN_CONTROL_PARAMS +
                YM_MATRIX_GAIN_CONTROL_THESHOLD_TARGET +
                mic_noise_gate_gain_control_on_off_index;
    }
    else if (args->mod == 0x0E)
    {
        index = mic_aec1_auto_mixer_gain_control_theshold_target_index;
    }
    else if (args->mod == 0x0F)
    {
        index = mic_aec0_auto_mixer_gain_control_theshold_target_index;
    }
    else
    {
        return NULL;
    }
    database_value tmp = {.i32 = val};
    database_write(index, tmp);
    return NULL;
}

static void *get_module_threshold(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t mod;
    } __attribute__((packed)) get_module_threshold_def;
    get_module_threshold_def *args = (get_module_threshold_def *)data;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xC5;
    a2b_comm.ackl = 6;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 1, &msg->payload[msg->len], 2);
        msg->len += 2;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    int16_t *val = (int16_t *)&msg->payload[msg->len];
    int index = 0;
    if (args->mod <= 0x0D)
    {
        index = args->mod * YM_MATRIX_MAX_GAIN_CONTROL_PARAMS +
                YM_MATRIX_GAIN_CONTROL_THESHOLD_TARGET +
                mic_noise_gate_gain_control_on_off_index;
    }
    else if (args->mod == 0x0E)
    {
        index = mic_aec1_auto_mixer_gain_control_theshold_target_index;
    }
    else if (args->mod == 0x0F)
    {
        index = mic_aec0_auto_mixer_gain_control_theshold_target_index;
    }
    *val = database_get(index)->i32;
    *val = htobe16(*val);
    msg->len += 2;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_module_compensation(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t mod;
        int16_t gain;
    } __attribute__((packed)) set_module_compensation_def;
    set_module_compensation_def *args = (set_module_compensation_def *)data;
    int16_t cmps = htobe16(args->gain);

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xC6;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 3, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    if ((YM_MATRIX_AUX_NOISE_GATE >= args->mod) || (YM_MATRIX_AUTO_DUCKER == args->mod))
    {
        cmps = cmps < 10 * YM_MATRIX_GAIN_THRESHOLD_TARGET_MIN_DB ? 10 * YM_MATRIX_GAIN_THRESHOLD_TARGET_MIN_DB : cmps;
        cmps = cmps > 10 * YM_MATRIX_GAIN_THRESHOLD_TARGET_MAX_DB ? 10 * YM_MATRIX_GAIN_THRESHOLD_TARGET_MAX_DB : cmps;
    }
    else if (YM_MATRIX_AUX_DRC >= args->mod)
    {
        cmps = cmps < 10 * YM_MATRIX_GAIN_COMPR_BOOST_MIN_DB ? 10 * YM_MATRIX_GAIN_COMPR_BOOST_MIN_DB : cmps;
        cmps = cmps > 10 * YM_MATRIX_GAIN_COMPR_BOOST_MAX_DB ? 10 * YM_MATRIX_GAIN_COMPR_BOOST_MAX_DB : cmps;
    }
    else if (YM_MATRIX_AUX_EXPANDER >= args->mod)
    {
        cmps = cmps < 10 * YM_MIXER_EXPANDER_MIN_BOOST_DB ? 10 * YM_MIXER_EXPANDER_MIN_BOOST_DB : cmps;
        cmps = cmps > 10 * YM_MIXER_EXPANDER_MAX_BOOST_DB ? 10 * YM_MIXER_EXPANDER_MAX_BOOST_DB : cmps;
    }
    else
    {
        cmps = cmps < 10 * YM_MATRIX_GAIN_COMPR_BOOST_MIN_DB ? 10 * YM_MATRIX_GAIN_COMPR_BOOST_MIN_DB : cmps;
        cmps = cmps > 10 * YM_MATRIX_GAIN_COMPR_BOOST_MAX_DB ? 10 * YM_MATRIX_GAIN_COMPR_BOOST_MAX_DB : cmps;
    }
    int index = 0;
    if (args->mod <= 0x0D)
    {
        index = args->mod * YM_MATRIX_MAX_GAIN_CONTROL_PARAMS +
                YM_MATRIX_GAIN_CONTROL_MAKEUP_BOOST_SUPPRESS +
                mic_noise_gate_gain_control_on_off_index;
    }
    else if (args->mod == 0x0E)
    {
        index = mic_aec1_auto_mixer_gain_control_makeup_boost_suppress_index;
    }
    else if (args->mod == 0x0F)
    {
        index = mic_aec0_auto_mixer_gain_control_makeup_boost_suppress_index;
    }
    else
    {
        return NULL;
    }
    database_value tmp = {.i32 = cmps};
    database_write(index, tmp);
    return NULL;
}

static void *get_module_compensation(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t mod;
    } __attribute__((packed)) get_module_compensation_def;
    get_module_compensation_def *args = (get_module_compensation_def *)data;
    int16_t *val;
    int index = 0;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xC7;
    a2b_comm.ackl = 6;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 1, &msg->payload[msg->len], 2);
        msg->len += 2;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    val = (int16_t *)&msg->payload[msg->len];
    if (args->mod <= 0x0D)
    {
        index = args->mod * YM_MATRIX_MAX_GAIN_CONTROL_PARAMS +
                YM_MATRIX_GAIN_CONTROL_MAKEUP_BOOST_SUPPRESS +
                mic_noise_gate_gain_control_on_off_index;
    }
    else if (args->mod == 0x0E)
    {
        index = mic_aec1_auto_mixer_gain_control_makeup_boost_suppress_index;
    }
    else if (args->mod == 0x0F)
    {
        index = mic_aec0_auto_mixer_gain_control_makeup_boost_suppress_index;
    }
    *val = database_get(index)->i32;
    *val = htobe16(*val);
    msg->len += 2;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_module_attack_time(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t mod;
        uint16_t time;
    } __attribute__((packed)) set_module_attack_time_def;
    set_module_attack_time_def *args = (set_module_attack_time_def *)data;
    int16_t val = htobe16(args->time);
    val = val < YM_MATRIX_GAIN_ATTACK_RELEASE_MIN_MS ? YM_MATRIX_GAIN_ATTACK_RELEASE_MIN_MS : val;
    val = val > YM_MATRIX_GAIN_ATTACK_RELEASE_MAX_MS ? YM_MATRIX_GAIN_ATTACK_RELEASE_MAX_MS : val;
    int index = 0;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xC8;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 3, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    if (args->mod <= 0x0D)
    {
        index = args->mod * YM_MATRIX_MAX_GAIN_CONTROL_PARAMS +
                YM_MATRIX_GAIN_CONTROL_ATTACKTIME_MS +
                mic_noise_gate_gain_control_on_off_index;
    }
    else if (args->mod == 0x0E)
    {
        index = mic_aec1_auto_mixer_gain_control_attacktime_index;
    }
    else if (args->mod == 0x0F)
    {
        index = mic_aec0_auto_mixer_gain_control_attacktime_index;
    }
    else
    {
        return NULL;
    }
    database_value tmp = {.i32 = val};
    database_write(index, tmp);
    return NULL;
}

static void *get_module_attack_time(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t mod;
    } __attribute__((packed)) get_module_attack_time_def;
    get_module_attack_time_def *args = (get_module_attack_time_def *)data;
    int16_t *val;
    int index = 0;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xC9;
    a2b_comm.ackl = 6;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 1, &msg->payload[msg->len], 2);
        msg->len += 2;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    val = (int16_t *)&msg->payload[msg->len];
    if (args->mod <= 0x0D)
    {
        index = args->mod * YM_MATRIX_MAX_GAIN_CONTROL_PARAMS +
                YM_MATRIX_GAIN_CONTROL_ATTACKTIME_MS +
                mic_noise_gate_gain_control_on_off_index;
    }
    else if (args->mod == 0x0E)
    {
        index = mic_aec1_auto_mixer_gain_control_attacktime_index;
    }
    else if (args->mod == 0x0F)
    {
        index = mic_aec0_auto_mixer_gain_control_attacktime_index;
    }
    *val = database_get(index)->i32;
    *val = htobe16(*val);
    msg->len += 2;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_module_release_time(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t mod;
        uint16_t time;
    } __attribute__((packed)) set_module_release_time_def;
    set_module_release_time_def *args = (set_module_release_time_def *)data;
    int16_t val = htobe16(args->time);
    val = val < YM_MATRIX_GAIN_ATTACK_RELEASE_MIN_MS ? YM_MATRIX_GAIN_ATTACK_RELEASE_MIN_MS : val;
    val = val > YM_MATRIX_GAIN_ATTACK_RELEASE_MAX_MS ? YM_MATRIX_GAIN_ATTACK_RELEASE_MAX_MS : val;
    int index = 0;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xCA;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 3, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    if (args->mod <= 0x0D)
    {
        index = args->mod * YM_MATRIX_MAX_GAIN_CONTROL_PARAMS +
                YM_MATRIX_GAIN_CONTROL_RELEASETIME_MS +
                mic_noise_gate_gain_control_on_off_index;
    }
    else if (args->mod == 0x0E)
    {
        index = mic_aec1_auto_mixer_gain_control_releasetime_index;
    }
    else if (args->mod == 0x0F)
    {
        index = mic_aec0_auto_mixer_gain_control_releasetime_index;
    }
    else
    {
        return NULL;
    }
    database_value tmp = {.i32 = val};
    database_write(index, tmp);
    return NULL;
}

static void *get_module_release_time(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t mod;
    } __attribute__((packed)) get_module_release_time_def;
    get_module_release_time_def *args = (get_module_release_time_def *)data;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xCB;
    a2b_comm.ackl = 6;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 1, &msg->payload[msg->len], 2);
        msg->len += 2;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    int16_t *val = (int16_t *)&msg->payload[msg->len];
    int index = 0;
    if (args->mod <= 0x0D)
    {
        index = args->mod * YM_MATRIX_MAX_GAIN_CONTROL_PARAMS +
                YM_MATRIX_GAIN_CONTROL_RELEASETIME_MS +
                mic_noise_gate_gain_control_on_off_index;
    }
    else if (args->mod == 0x0E)
    {
        index = mic_aec1_auto_mixer_gain_control_releasetime_index;
    }
    else if (args->mod == 0x0F)
    {
        index = mic_aec0_auto_mixer_gain_control_releasetime_index;
    }
    *val = database_get(index)->i32;
    *val = htobe16(*val);
    msg->len += 2;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_module_others_param(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t mod;
        int16_t param;
    } __attribute__((packed)) set_module_others_param_def;
    set_module_others_param_def *args = (set_module_others_param_def *)data;
    int16_t val = htobe16(args->param);

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xCC;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 3, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    if (YM_MATRIX_AUX_EXPANDER >= args->mod)
    {
        if (val < 10 * YM_MIXER_DRC_EXPAND_MIN_COMPR_RATIO)
        {
            val = 10 * YM_MIXER_DRC_EXPAND_MIN_COMPR_RATIO;
        }
        else if (val > 10 * YM_MIXER_DRC_EXPAND_MAX_COMPR_RATIO)
        {
            val = 10 * YM_MIXER_DRC_EXPAND_MAX_COMPR_RATIO;
        }
    }
    else if (YM_MATRIX_AUTO_DUCKER == args->mod)
    {
        if (val < 1)
        {
            val = 1;
        }
        else if (val > YM_DUCK_MAX_KEEP_TIME_MS)
        {
            val = YM_DUCK_MAX_KEEP_TIME_MS;
        }
    }
    else if ((args->mod >= YM_MATRIX_MIC_AGC) && (YM_MATRIX_AUX_AGC >= args->mod))
    {
        if (val < 10 * YM_AGC_NOISE_LEVEL_DEF_DB)
        {
            val = 10 * YM_AGC_NOISE_LEVEL_DEF_DB;
        }
        else if (val > 0)
        {
            val = 0;
        }
    }
    else if ((args->mod >= YM_MATRIX_MIC_AEC1_AUTO_MIXER) && (YM_MATRIX_MIC_AEC0_AUTO_MIXER >= args->mod))
    {
        if (val < 10)
        {
            val = 10;
        }
        else if (val > 500)
        {
            val = 500;
        }
    }
    int index = 0;
    if (args->mod <= 0x0D)
    {
        index = args->mod * YM_MATRIX_MAX_GAIN_CONTROL_PARAMS +
                YM_MATRIX_GAIN_CONTROL_COMPR_RATIO +
                mic_noise_gate_gain_control_on_off_index;
    }
    else if (args->mod == 0x0E)
    {
        index = mic_aec1_auto_mixer_gain_control_compr_ratio_index;
    }
    else if (args->mod == 0x0F)
    {
        index = mic_aec0_auto_mixer_gain_control_compr_ratio_index;
    }
    else
    {
        return NULL;
    }
    database_value tmp = {.i32 = val};
    database_write(index, tmp);
    return NULL;
}

static void *get_module_others_param(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t mod;
    } __attribute__((packed)) get_module_others_param_def;
    get_module_others_param_def *args = (get_module_others_param_def *)data;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xCD;
    a2b_comm.ackl = 6;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 1, &msg->payload[msg->len], 2);
        msg->len += 2;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    int16_t *val = (int16_t *)&msg->payload[msg->len];
    int index = 0;
    if (args->mod <= 0x0D)
    {
        index = args->mod * YM_MATRIX_MAX_GAIN_CONTROL_PARAMS +
                YM_MATRIX_GAIN_CONTROL_COMPR_RATIO +
                mic_noise_gate_gain_control_on_off_index;
    }
    else if (args->mod == 0x0E)
    {
        index = mic_aec1_auto_mixer_gain_control_compr_ratio_index;
    }
    else if (args->mod == 0x0F)
    {
        index = mic_aec0_auto_mixer_gain_control_compr_ratio_index;
    }
    *val = database_get(index)->i32;
    *val = htobe16(*val);
    msg->len += 2;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

enum
{
    GEQ_BAND_MIC = (0x00),
    GEQ_BAND_AUX = (0x01),
    GEQ_BAND_SPK = (0x02),
    GEQ_BAND_HP = (0x03),
    GEQ_BAND_REC = (0x04),
    PEQ_BAND_MIC = (0x05),
    PEQ_BAND_AUX = (0x06),
    PEQ_BAND_SPK = (0x07),
    PEQ_BAND_HP = (0x08),
    PEQ_BAND_REC = (0x09),
    PEQ_BAND_ANF_0 = (0x0A),
    PEQ_BAND_ANF_1 = (0x0B),
    PEQ_BAND_AEC = (0x0C),
    PEQ_BAND_ANF_2 = (0x0D),
#ifdef BEAM_ENABLE
    PEQ_BAND_BEAMF = (0x0E),
#endif
    PEQ_BAND_AEC_1 = (0x0F),
    PEQ_BAND_AEC_2 = (0x10),
#ifdef BEAM_ENABLE
#if (BEAM_INSTANCE_NUMBER > 1)
    PEQ_BAND_BEAMF2 = (0x11),
    PEQ_BAND_BEAMF3 = (0x12),
    PEQ_BAND_BEAMF4 = (0x13),
#endif
#endif
};

static void *set_geq_peq_band(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t type;
        uint8_t band;
    } __attribute__((packed)) set_geq_peq_band_def;

    set_geq_peq_band_def *args = (set_geq_peq_band_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;
    short val = args->band;
    int index;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xCE;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 2, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    switch (args->type)
    {
    case GEQ_BAND_MIC:
    case GEQ_BAND_AUX:
    case GEQ_BAND_SPK:
    case GEQ_BAND_HP:
    case GEQ_BAND_REC:
    {
        index = geq_mic_cnt_index + args->type * 66;
        val = val > MIXER_PARAM_GEQ_MAX ? MIXER_PARAM_GEQ_MAX : val;
    }
    break;
    case PEQ_BAND_MIC:
    case PEQ_BAND_AUX:
    case PEQ_BAND_SPK:
    case PEQ_BAND_HP:
    case PEQ_BAND_REC:
    {
        val = val > MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX : val;
        args->type -= PEQ_BAND_MIC;
        index = peq_mic_cnt_index + args->type * 34;
    }
    break;
    case PEQ_BAND_AEC:
        return NULL;
        break;
    case PEQ_BAND_ANF_0:
    {
        val = val > MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX : val;
        index = peq_anf_0_cnt_ms_index + base;
    }
    break;
    case PEQ_BAND_ANF_1:
    {
        val = val > MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX : val;
        index = peq_anf_1_cnt_ms_index + base;
    }
    break;
    case PEQ_BAND_ANF_2:
    {
        val = val > MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX : val;
        index = peq_anf_2_cnt_ms_index + base;
    }
    break;
#ifdef BEAM_ENABLE
    case PEQ_BAND_BEAMF:
    {
        val = val > MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX : val;
        int32_t offset = 0;
#if defined(BD_MIXER_LINE_MICS) || defined(BD_MIXER_YO_8MIC)
        offset = (YM_PROTO_CORE_ID(device) == 1) ? 0u : BEAM_INSTANCE_NUMBER;
#endif
        index = peq_beamf_cnt_index + BEAM_BASE_INDEX + offset * BEAM_PARAM_MAX;
        break;
    }
#endif /* BEAM_ENABLE */
    case PEQ_BAND_AEC_1:
    {
        val = val > MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX : val;
        index = peq_aec_1_cnt_ms_index + base;
    }
    break;
    case PEQ_BAND_AEC_2:
    {
        val = val > MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX : val;
        index = peq_aec_2_cnt_ms_index + base;
    }
    break;
#ifdef BEAM_ENABLE
#if (BEAM_INSTANCE_NUMBER > 1)
    case PEQ_BAND_BEAMF2:
    case PEQ_BAND_BEAMF3:
    case PEQ_BAND_BEAMF4:
    {
        val = val > MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX : val;
        int32_t offset = args->type - PEQ_BAND_BEAMF2 + 1;
        if (YM_PROTO_CORE_ID(device) == 2)
            offset += BEAM_INSTANCE_NUMBER;
        index = peq_beamf_cnt_index + BEAM_BASE_INDEX + offset * BEAM_PARAM_MAX;
        break;
    }
#endif
#endif /* BEAM_ENABLE */
    default:
        return NULL;
        break;
    }
    database_value tmp = {.i32 = val};
    database_write(index, tmp);
    return NULL;
}

static void *get_geq_peq_band(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t type;
    } __attribute__((packed)) get_geq_peq_band_def;
    get_geq_peq_band_def *args = (get_geq_peq_band_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xCF;
    a2b_comm.ackl = 5;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 1, &msg->payload[msg->len++], 1);

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    int index;
    switch (args->type)
    {
    case GEQ_BAND_MIC:
    case GEQ_BAND_AUX:
    case GEQ_BAND_SPK:
    case GEQ_BAND_HP:
    case GEQ_BAND_REC:
    {
        index = geq_mic_cnt_index + args->type * 66;
    }
    break;
    case PEQ_BAND_MIC:
    case PEQ_BAND_AUX:
    case PEQ_BAND_SPK:
    case PEQ_BAND_HP:
    case PEQ_BAND_REC:
    {
        args->type -= PEQ_BAND_MIC;
        index = peq_mic_cnt_index + args->type * 34;
    }
    break;
    case PEQ_BAND_AEC:
        index = 0;
        break;
    case PEQ_BAND_ANF_0:
    {
        index = peq_anf_0_cnt_ms_index + base;
    }
    break;
    case PEQ_BAND_ANF_1:
    {
        index = peq_anf_1_cnt_ms_index + base;
    }
    break;
    case PEQ_BAND_ANF_2:
    {
        index = peq_anf_2_cnt_ms_index + base;
    }
    break;
#ifdef BEAM_ENABLE
    case PEQ_BAND_BEAMF:
    {
        int32_t offset = 0;
#if defined(BD_MIXER_LINE_MICS) || defined(BD_MIXER_YO_8MIC)
        offset = (YM_PROTO_CORE_ID(device) == 1) ? 0u : BEAM_INSTANCE_NUMBER;
#endif
        index = peq_beamf_cnt_index + BEAM_BASE_INDEX + offset * BEAM_PARAM_MAX;
        break;
    }
#endif /* BEAM_ENABLE */
    case PEQ_BAND_AEC_1:
    {
        index = peq_aec_1_cnt_ms_index + base;
    }
    break;
    case PEQ_BAND_AEC_2:
    {
        index = peq_aec_2_cnt_ms_index + base;
    }
    break;
#ifdef BEAM_ENABLE
#if (BEAM_INSTANCE_NUMBER > 1)
    case PEQ_BAND_BEAMF2:
    case PEQ_BAND_BEAMF3:
    case PEQ_BAND_BEAMF4:
    {
        int32_t offset = args->type - PEQ_BAND_BEAMF2 + 1;
        if (YM_PROTO_CORE_ID(device) == 2)
            offset += BEAM_INSTANCE_NUMBER;
        index = peq_beamf_cnt_index + BEAM_BASE_INDEX + offset * BEAM_PARAM_MAX;
        break;
    }
#endif
#endif /* BEAM_ENABLE */
    default:
        index = 0;
        break;
    }
    msg->payload[msg->len++] = database_get(index)->i32;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_geq_peq_freq(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t type;
        uint8_t band;
        uint16_t freq;
    } __attribute__((packed)) set_geq_peq_freq_def;

    set_geq_peq_freq_def *args = (set_geq_peq_freq_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;
    short val = htobe16(args->freq);
    val = val < YM_MIXER_MIN_EQ_CENTER_FREQ ? YM_MIXER_MIN_EQ_CENTER_FREQ : val;
    val = val > YM_MIXER_MAX_EQ_CENTER_FREQ ? YM_MIXER_MAX_EQ_CENTER_FREQ : val;
    int index;
    database_value tmp = {.i32 = val};

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xD0;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 4, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    switch (args->type)
    {
    case GEQ_BAND_MIC:
    case GEQ_BAND_AUX:
    case GEQ_BAND_SPK:
    case GEQ_BAND_HP:
    case GEQ_BAND_REC:
    {
        args->band = args->band >= MIXER_PARAM_GEQ_MAX ? MIXER_PARAM_GEQ_MAX - 1 : args->band;
        index = geq_mic_freq1_index + args->type * 66 + args->band;
    }
    break;
    case PEQ_BAND_MIC:
    case PEQ_BAND_AUX:
    case PEQ_BAND_SPK:
    case PEQ_BAND_HP:
    case PEQ_BAND_REC:
    {
        args->type -= PEQ_BAND_MIC;
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_mic_freq1_index + args->type * 34 + args->band;
    }
    break;
    case PEQ_BAND_AEC:
    {
        index = low_shelf_freq_ms_index + base;
    }
    break;
    case PEQ_BAND_ANF_0:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_anf_0_freq1_ms_index + base + args->band;
    }
    break;
    case PEQ_BAND_ANF_1:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_anf_1_freq1_ms_index + base + args->band;
    }
    break;
    case PEQ_BAND_ANF_2:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_anf_2_freq1_ms_index + args->band + base;
    }
    break;
#ifdef BEAM_ENABLE
    case PEQ_BAND_BEAMF:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        int32_t offset = 0;
#if defined(BD_MIXER_LINE_MICS) || defined(BD_MIXER_YO_8MIC)
        offset = (YM_PROTO_CORE_ID(device) == 1) ? 0u : BEAM_INSTANCE_NUMBER;
#endif
        index = peq_beamf_freq1_index + args->band + BEAM_BASE_INDEX + offset * BEAM_PARAM_MAX;
        break;
    }
#endif /* BEAM_ENABLE */
    case PEQ_BAND_AEC_1:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_aec_1_freq1_ms_index + base + args->band;
    }
    break;
    case PEQ_BAND_AEC_2:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_aec_2_freq1_ms_index + base + args->band;
    }
    break;
#ifdef BEAM_ENABLE
#if (BEAM_INSTANCE_NUMBER > 1)
    case PEQ_BAND_BEAMF2:
    case PEQ_BAND_BEAMF3:
    case PEQ_BAND_BEAMF4:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        int32_t offset = args->type - PEQ_BAND_BEAMF2 + 1;
        if (YM_PROTO_CORE_ID(device) == 2)
            offset += BEAM_INSTANCE_NUMBER;
        index = peq_beamf_freq1_index + args->band + BEAM_BASE_INDEX + offset * BEAM_PARAM_MAX;
        break;
    }
#endif
#endif /* BEAM_ENABLE */
    default:
        return NULL;
        break;
    }
    database_write(index, tmp);
    return NULL;
}

static void *get_geq_peq_freq(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t type;
        uint8_t band;
    } __attribute__((packed)) get_geq_peq_freq_def;
    get_geq_peq_freq_def *args = (get_geq_peq_freq_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xD1;
    a2b_comm.ackl = 6;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 2, &msg->payload[msg->len], 2);
        msg->len += 2;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    int index;
    short *val = (short *)&msg->payload[msg->len];
    database_value tmp;
    switch (args->type)
    {
    case GEQ_BAND_MIC:
    case GEQ_BAND_AUX:
    case GEQ_BAND_SPK:
    case GEQ_BAND_HP:
    case GEQ_BAND_REC:
    {
        args->band = args->band >= MIXER_PARAM_GEQ_MAX ? MIXER_PARAM_GEQ_MAX - 1 : args->band;
        index = geq_mic_freq1_index + args->type * 66 + args->band;
    }
    break;
    case PEQ_BAND_MIC:
    case PEQ_BAND_AUX:
    case PEQ_BAND_SPK:
    case PEQ_BAND_HP:
    case PEQ_BAND_REC:
    {
        args->type -= PEQ_BAND_MIC;
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_mic_freq1_index + args->type * 34 + args->band;
    }
    break;
    case PEQ_BAND_AEC:
    {
        index = low_shelf_freq_ms_index + base;
    }
    break;
    case PEQ_BAND_ANF_0:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_anf_0_freq1_ms_index + base + args->band;
    }
    break;
    case PEQ_BAND_ANF_1:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_anf_1_freq1_ms_index + base + args->band;
    }
    break;
    case PEQ_BAND_ANF_2:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_anf_2_freq1_ms_index + args->band + base;
    }
    break;
#ifdef BEAM_ENABLE
    case PEQ_BAND_BEAMF:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        int32_t offset = 0;
#if defined(BD_MIXER_LINE_MICS) || defined(BD_MIXER_YO_8MIC)
        offset = (YM_PROTO_CORE_ID(device) == 1) ? 0u : BEAM_INSTANCE_NUMBER;
#endif
        index = peq_beamf_freq1_index + args->band + BEAM_BASE_INDEX + offset * BEAM_PARAM_MAX;
        break;
    }
#endif /* BEAM_ENABLE */
    case PEQ_BAND_AEC_1:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_aec_1_freq1_ms_index + base + args->band;
    }
    break;
    case PEQ_BAND_AEC_2:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_aec_2_freq1_ms_index + base + args->band;
    }
    break;
#ifdef BEAM_ENABLE
#if (BEAM_INSTANCE_NUMBER > 1)
    case PEQ_BAND_BEAMF2:
    case PEQ_BAND_BEAMF3:
    case PEQ_BAND_BEAMF4:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        int32_t offset = args->type - PEQ_BAND_BEAMF2 + 1;
        if (YM_PROTO_CORE_ID(device) == 2)
            offset += BEAM_INSTANCE_NUMBER;
        index = peq_beamf_freq1_index + args->band + BEAM_BASE_INDEX + offset * BEAM_PARAM_MAX;
        break;
    }
#endif
#endif /* BEAM_ENABLE */
    default:
        index = 0;
        break;
    }
    tmp = database_read(index);
    *val = tmp.i32;
    *val = htobe16(*val);
    msg->len += 2;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_geq_peq_gain(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t type;
        uint8_t band;
        int16_t gain;
    } __attribute__((packed)) set_geq_peq_gain_def;
    set_geq_peq_gain_def *args = (set_geq_peq_gain_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;
    short val = htobe16(args->gain);
    val = val < YM_MIXER_MIN_EQ_GAIN ? YM_MIXER_MIN_EQ_GAIN : val;
    val = val > YM_MIXER_MAX_EQ_GAIN ? YM_MIXER_MAX_EQ_GAIN : val;
    int index;
    database_value tmp = {.i32 = val};

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xD2;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 4, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    switch (args->type)
    {
    case GEQ_BAND_MIC:
    case GEQ_BAND_AUX:
    case GEQ_BAND_SPK:
    case GEQ_BAND_HP:
    case GEQ_BAND_REC:
    {
        args->band = args->band >= MIXER_PARAM_GEQ_MAX ? MIXER_PARAM_GEQ_MAX - 1 : args->band;
        index = geq_mic_gain1_index + args->type * 66 + args->band;
    }
    break;
    case PEQ_BAND_MIC:
    case PEQ_BAND_AUX:
    case PEQ_BAND_SPK:
    case PEQ_BAND_HP:
    case PEQ_BAND_REC:
    {
        args->type -= PEQ_BAND_MIC;
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_mic_gain1_index + args->type * 34 + args->band;
    }
    break;
    case PEQ_BAND_AEC:
    {
        index = low_shelf_gain_ms_index + base;
    }
    break;
    case PEQ_BAND_ANF_0:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_anf_0_gain1_ms_index + args->band + base;
    }
    break;
    case PEQ_BAND_ANF_1:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_anf_1_gain1_ms_index + args->band + base;
    }
    break;
    case PEQ_BAND_ANF_2:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_anf_2_gain1_ms_index + args->band + base;
    }
    break;
#ifdef BEAM_ENABLE
    case PEQ_BAND_BEAMF:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        int32_t offset = 0;
#if defined(BD_MIXER_LINE_MICS) || defined(BD_MIXER_YO_8MIC)
        offset = (YM_PROTO_CORE_ID(device) == 1) ? 0u : BEAM_INSTANCE_NUMBER;
#endif
        index = peq_beamf_gain1_index + args->band + BEAM_BASE_INDEX + offset * BEAM_PARAM_MAX;
        break;
    }
#endif /* BEAM_ENABLE */
    case PEQ_BAND_AEC_1:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_aec_1_gain1_ms_index + args->band + base;
    }
    break;
    case PEQ_BAND_AEC_2:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_aec_2_gain1_ms_index + args->band + base;
    }
    break;
#ifdef BEAM_ENABLE
#if (BEAM_INSTANCE_NUMBER > 1)
    case PEQ_BAND_BEAMF2:
    case PEQ_BAND_BEAMF3:
    case PEQ_BAND_BEAMF4:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        int32_t offset = args->type - PEQ_BAND_BEAMF2 + 1;
        if (YM_PROTO_CORE_ID(device) == 2)
            offset += BEAM_INSTANCE_NUMBER;
        index = peq_beamf_gain1_index + args->band + BEAM_BASE_INDEX + offset * BEAM_PARAM_MAX;
        break;
    }
#endif
#endif /* BEAM_ENABLE */
    default:
        return NULL;
        break;
    }
    database_write(index, tmp);
    return NULL;
}

static void *get_geq_peq_gain(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t type;
        uint8_t band;
    } __attribute__((packed)) get_geq_peq_gain_def;
    get_geq_peq_gain_def *args = (get_geq_peq_gain_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;
    int index;
    short *val;
    database_value tmp;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xD3;
    a2b_comm.ackl = 6;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 2, &msg->payload[msg->len], 2);
        msg->len += 2;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    val = (short *)&msg->payload[msg->len];
    switch (args->type)
    {
    case GEQ_BAND_MIC:
    case GEQ_BAND_AUX:
    case GEQ_BAND_SPK:
    case GEQ_BAND_HP:
    case GEQ_BAND_REC:
    {
        args->band = args->band >= MIXER_PARAM_GEQ_MAX ? MIXER_PARAM_GEQ_MAX - 1 : args->band;
        index = geq_mic_gain1_index + args->type * 66 + args->band;
    }
    break;
    case PEQ_BAND_MIC:
    case PEQ_BAND_AUX:
    case PEQ_BAND_SPK:
    case PEQ_BAND_HP:
    case PEQ_BAND_REC:
    {
        args->type -= PEQ_BAND_MIC;
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_mic_gain1_index + args->type * 34 + args->band;
    }
    break;
    case PEQ_BAND_AEC:
    {
        index = low_shelf_gain_ms_index + base;
    }
    break;
    case PEQ_BAND_ANF_0:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_anf_0_gain1_ms_index + args->band + base;
    }
    break;
    case PEQ_BAND_ANF_1:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_anf_1_gain1_ms_index + args->band + base;
    }
    break;
    case PEQ_BAND_ANF_2:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_anf_2_gain1_ms_index + args->band + base;
    }
    break;
#ifdef BEAM_ENABLE
    case PEQ_BAND_BEAMF:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        int32_t offset = 0;
#if defined(BD_MIXER_LINE_MICS) || defined(BD_MIXER_YO_8MIC)
        offset = (YM_PROTO_CORE_ID(device) == 1) ? 0u : BEAM_INSTANCE_NUMBER;
#endif
        index = peq_beamf_gain1_index + args->band + BEAM_BASE_INDEX + offset * BEAM_PARAM_MAX;
        break;
    }
#endif /* BEAM_ENABLE */
    case PEQ_BAND_AEC_1:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_aec_1_gain1_ms_index + args->band + base;
    }
    break;
    case PEQ_BAND_AEC_2:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        index = peq_aec_2_gain1_ms_index + args->band + base;
    }
    break;
#ifdef BEAM_ENABLE
#if (BEAM_INSTANCE_NUMBER > 1)
    case PEQ_BAND_BEAMF2:
    case PEQ_BAND_BEAMF3:
    case PEQ_BAND_BEAMF4:
    {
        args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
        int32_t offset = args->type - PEQ_BAND_BEAMF2 + 1;
        if (YM_PROTO_CORE_ID(device) == 2)
            offset += BEAM_INSTANCE_NUMBER;
        index = peq_beamf_gain1_index + args->band + BEAM_BASE_INDEX + offset * BEAM_PARAM_MAX;
        break;
    }
#endif
#endif /* BEAM_ENABLE */
    default:
        index = 0;
        break;
    }
    tmp = database_read(index);
    *val = tmp.i32;
    *val = htobe16(*val);
    msg->len += 2;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_peq_filter(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t type;
        uint8_t band;
        uint8_t filter;
    } __attribute__((packed)) set_peq_filter_def;
    set_peq_filter_def *args = (set_peq_filter_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;
    int index;

    args->filter = args->filter > YM_PEQ_NOTCH_FILTER ? YM_PEQ_NOTCH_FILTER : args->filter;
    args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
    database_value tmp = {.i32 = args->filter};

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xD4;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 3, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    switch (args->type)
    {
    case 0x00:
        index = peq_mic_filter1_index + args->band;
        break;
    case 0x01:
        index = peq_aux_filter1_index + args->band;
        break;
    case 0x02:
        index = peq_spk_filter1_index + args->band;
        break;
    case 0x03:
        index = peq_hp_filter1_index + args->band;
        break;
    case 0x04:
        index = peq_rec_filter1_index + args->band;
        break;
    case 0x05:
        index = peq_anf_0_filter1_ms_index + base + args->band;
        break;
    case 0x06:
        index = peq_anf_1_filter1_ms_index + base + args->band;
        break;
    case 0x07:
        return NULL;
        break;
    case 0x08:
        index = peq_anf_2_filter1_ms_index + base + args->band;
        break;
#ifdef BEAM_ENABLE
    case 0x09:
    {
        int32_t offset = 0;
#if defined(BD_MIXER_LINE_MICS) || defined(BD_MIXER_YO_8MIC)
        offset = (YM_PROTO_CORE_ID(device) == 1) ? 0u : BEAM_INSTANCE_NUMBER;
#endif
        index = peq_beamf_filter1_index + args->band + BEAM_BASE_INDEX + offset * BEAM_PARAM_MAX;
        break;
    }
#endif /* BEAM_ENABLE */
    case 0x0a:
        index = peq_aec_1_filter1_ms_index + base + args->band;
        break;
    case 0x0b:
        index = peq_aec_2_filter1_ms_index + base + args->band;
        break;
#ifdef BEAM_ENABLE
#if (BEAM_INSTANCE_NUMBER > 1)
    case 0x0c:
    case 0x0d:
    case 0x0e:
    {
        int32_t offset = args->type - 0xb;
        if (YM_PROTO_CORE_ID(device) == 2)
            offset += BEAM_INSTANCE_NUMBER;
        index = peq_beamf_filter1_index + args->band + BEAM_BASE_INDEX + offset * BEAM_PARAM_MAX;
        break;
    }
#endif
#endif /* BEAM_ENABLE */
    default:
        return NULL;
        break;
    }
    database_write(index, tmp);
    return NULL;
}

static void *get_peq_filter(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t type;
        uint8_t band;
    } __attribute__((packed)) get_peq_filter_def;
    get_peq_filter_def *args = (get_peq_filter_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;
    int index = 0;
    database_value tmp;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xD5;
    a2b_comm.ackl = 5;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 2, &msg->payload[msg->len], 1);
        msg->len += 1;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
    switch (args->type)
    {
    case 0x00:
        index = peq_mic_filter1_index + args->band;
        break;
    case 0x01:
        index = peq_aux_filter1_index + args->band;
        break;
    case 0x02:
        index = peq_spk_filter1_index + args->band;
        break;
    case 0x03:
        index = peq_hp_filter1_index + args->band;
        break;
    case 0x04:
        index = peq_rec_filter1_index + args->band;
        break;
    case 0x05:
        index = peq_anf_0_filter1_ms_index + base + args->band;
        break;
    case 0x06:
        index = peq_anf_1_filter1_ms_index + base + args->band;
        break;
    case 0x07:
        break;
    case 0x08:
        index = peq_anf_2_filter1_ms_index + base + args->band;
        break;
#ifdef BEAM_ENABLE
    case 0x09:
    {
        int32_t offset = 0;
#if defined(BD_MIXER_LINE_MICS) || defined(BD_MIXER_YO_8MIC)
        offset = (YM_PROTO_CORE_ID(device) == 1) ? 0u : BEAM_INSTANCE_NUMBER;
#endif
        index = peq_beamf_filter1_index + args->band + BEAM_BASE_INDEX + offset * BEAM_PARAM_MAX;
        break;
    }
#endif /* BEAM_ENABLE */
    case 0x0a:
        index = peq_aec_1_filter1_ms_index + base + args->band;
        break;
    case 0x0b:
        index = peq_aec_2_filter1_ms_index + base + args->band;
        break;
#ifdef BEAM_ENABLE
#if (BEAM_INSTANCE_NUMBER > 1)
    case 0x0c:
    case 0x0d:
    case 0x0e:
    {
        int32_t offset = args->type - 0xb;
        if (YM_PROTO_CORE_ID(device) == 2)
            offset += BEAM_INSTANCE_NUMBER;
        index = peq_beamf_filter1_index + args->band + BEAM_BASE_INDEX + offset * BEAM_PARAM_MAX;
        break;
    }
#endif
#endif /* BEAM_ENABLE */
    default:
        break;
    }
    tmp = database_read(index);
    msg->payload[msg->len++] = tmp.i32;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_peq_qval(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t type;
        uint8_t band;
        uint16_t qval;
    } __attribute__((packed)) set_peq_qval_def;
    set_peq_qval_def *args = (set_peq_qval_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;
    int index;

    args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
    short val = htobe16(args->qval);

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xD6;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 4, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    val = val < YM_PEQ_MIN_Q_VALUE ? YM_PEQ_MIN_Q_VALUE : val;
    val = val > YM_PEQ_MAX_Q_VALUE ? YM_PEQ_MAX_Q_VALUE : val;
    if (args->type + PEQ_BAND_MIC == PEQ_BAND_AEC)
    {
        index = low_shelf_qval_ms_index + base;
    }
    else
    {
        if (args->type + PEQ_BAND_MIC == PEQ_BAND_ANF_2)
            index = peq_anf_2_qval1_ms_index + base + args->band;
        else if (args->type + PEQ_BAND_MIC == PEQ_BAND_ANF_0)
            index = peq_anf_0_qval1_ms_index + base + args->band;
        else if (args->type + PEQ_BAND_MIC == PEQ_BAND_ANF_1)
            index = peq_anf_1_qval1_ms_index + base + args->band;
#ifdef BEAM_ENABLE
        else if (args->type + PEQ_BAND_MIC == PEQ_BAND_BEAMF)
        {
            int32_t offset = 0;
#if defined(BD_MIXER_LINE_MICS) || defined(BD_MIXER_YO_8MIC)
            offset = (YM_PROTO_CORE_ID(device) == 1) ? 0u : BEAM_INSTANCE_NUMBER;
#endif
            index = peq_beamf_qval1_index + args->band + BEAM_BASE_INDEX + offset * BEAM_PARAM_MAX;
        }
#endif /* BEAM_ENABLE */
        else if (args->type + PEQ_BAND_MIC == PEQ_BAND_AEC_1)
            index = peq_aec_1_qval1_ms_index + base + args->band;
        else if (args->type + PEQ_BAND_MIC == PEQ_BAND_AEC_2)
            index = peq_aec_2_qval1_ms_index + base + args->band;
#ifdef BEAM_ENABLE
#if (BEAM_INSTANCE_NUMBER > 1)
        else if ((args->type + PEQ_BAND_MIC == PEQ_BAND_BEAMF2) ||
                 (args->type + PEQ_BAND_MIC == PEQ_BAND_BEAMF3) ||
                 (args->type + PEQ_BAND_MIC == PEQ_BAND_BEAMF4))
        {
            int32_t offset = args->type + PEQ_BAND_MIC - PEQ_BAND_BEAMF2 + 1;
            if (YM_PROTO_CORE_ID(device) == 2)
                offset += BEAM_INSTANCE_NUMBER;
            index = peq_beamf_qval1_index + args->band + BEAM_BASE_INDEX + offset * BEAM_PARAM_MAX;
        }
#endif
#endif /* BEAM_ENABLE */
        else
            index = peq_mic_qval1_index + args->type * 34 + args->band;
    }
    database_value tmp = {.i32 = val};
    database_write(index, tmp);
    return NULL;
}

static void *get_peq_qval(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t type;
        uint8_t band;
    } __attribute__((packed)) get_peq_qval_def;
    get_peq_qval_def *args = (get_peq_qval_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;
    int index;
    args->band = args->band >= MIXER_PARAM_PEQ_MAX ? MIXER_PARAM_PEQ_MAX - 1 : args->band;
    short *val;
    database_value tmp;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xD7;
    a2b_comm.ackl = 6;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 2, &msg->payload[msg->len], 2);
        msg->len += 2;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    val = (short *)&msg->payload[msg->len];
    if (args->type + PEQ_BAND_MIC == PEQ_BAND_AEC)
    {
        index = low_shelf_qval_ms_index + base;
    }
    else
    {
        if (args->type + PEQ_BAND_MIC == PEQ_BAND_ANF_2)
            index = peq_anf_2_qval1_ms_index + base + args->band;
        else if (args->type + PEQ_BAND_MIC == PEQ_BAND_ANF_0)
            index = peq_anf_0_qval1_ms_index + base + args->band;
        else if (args->type + PEQ_BAND_MIC == PEQ_BAND_ANF_1)
            index = peq_anf_1_qval1_ms_index + base + args->band;
#ifdef BEAM_ENABLE
        else if (args->type + PEQ_BAND_MIC == PEQ_BAND_BEAMF)
        {
            int32_t offset = 0;
#if defined(BD_MIXER_LINE_MICS) || defined(BD_MIXER_YO_8MIC)
            offset = (YM_PROTO_CORE_ID(device) == 1) ? 0u : BEAM_INSTANCE_NUMBER;
#endif
            index = peq_beamf_qval1_index + args->band + BEAM_BASE_INDEX + offset * BEAM_PARAM_MAX;
        }
#endif /* BEAM_ENABLE */
        else if (args->type + PEQ_BAND_MIC == PEQ_BAND_AEC_1)
            index = peq_aec_1_qval1_ms_index + base + args->band;
        else if (args->type + PEQ_BAND_MIC == PEQ_BAND_AEC_2)
            index = peq_aec_2_qval1_ms_index + base + args->band;
#ifdef BEAM_ENABLE
#if (BEAM_INSTANCE_NUMBER > 1)
        else if ((args->type + PEQ_BAND_MIC == PEQ_BAND_BEAMF2) ||
                 (args->type + PEQ_BAND_MIC == PEQ_BAND_BEAMF3) ||
                 (args->type + PEQ_BAND_MIC == PEQ_BAND_BEAMF4))
        {
            int32_t offset = args->type + PEQ_BAND_MIC - PEQ_BAND_BEAMF2 + 1;
            if (YM_PROTO_CORE_ID(device) == 2)
                offset += BEAM_INSTANCE_NUMBER;
            index = peq_beamf_qval1_index + args->band + BEAM_BASE_INDEX + offset * BEAM_PARAM_MAX;
        }
#endif
#endif /* BEAM_ENABLE */
        else
            index = peq_mic_qval1_index + args->type * 34 + args->band;
    }
    tmp = database_read(index);
    *val = tmp.i32;
    *val = htobe16(*val);
    msg->len += 2;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

#ifdef BEAM_ENABLE

enum
{
    BEAM_SHADOW = (0x01),      /* beamforming shadow area */
    BEAM_PRIOR = (0x02),       /* beamforming prior area */
    BEAM_REFNG = (0x03),       /* beamforming remote end noise gate */
    BEAM_MICNG = (0x04),       /* beamforming mic noise gate */
    BEAM_MIC_CALIB = (0x05),   /* beamforming mic calibration */
    BEAM_MIC_COMPE = (0x06),   /* beamforming mic gain compensation */
    BEAM_MIC_NOISE = (0x07),   /* beamforming mic noise */
    BEAM_VAD_FLAG = (0x08),    /* beamforming vad flag */
    BEAM_HOLD_TIME = (0x09),   /* beamforming far-end voice holding time */
    BEAM_SMOOTH_TIME = (0x0A), /* beamforming far-end voice smooth time */
    BEAM_OUT_CHN = (0x0B),     /* beamforming output channel */
    BEAM_POLARITY = (0x0C),    /* beamforming polarity */
    BEAM_CLEAR_GAIN = (0x0D),  /* beamforming gain clearing */
    BEAM_REF_STEP = (0x0E),    /* beamforming far-end step */
    BEAM_INSTALL = (0x0F),     /* beamforming installation mode */
    BEAM_TUNE_MODE = (0x10),   /* beamforming tune mode */
    BEAM_OUT_GAIN = (0x11),    /* beamforming output gain */
    BEAM_WIDTH_LVL = (0x12),   /* beamforming width level */
    BEAM_INST_HEIGHT = (0x13), /* beamforming installation height */
    BEAM_ATM_DECAY = (0x14),   /* beamforming automixer decay factor */
    BEAM_ATM_PRIOR = (0x15),   /* beamforming automixer priority */
    BEAM_ATM_ATTA = (0x16),    /* beamforming automixer attack time */
    BEAM_ATM_RELS = (0x17),    /* beamforming automixer release time */
    BEAM_WIDTH = (0x18),       /* beamforming width of one beamformer channel */
    BEAM_COORDNT = (0x19),     /* beamforming coordinate of one beamformer channel */
    BEAM_ATM_GAIN = (0x1A),    /* beamforming automixer gain */
    BEAM_MAN_CALIB = (0xFF),   /* beamforming mic gain modify */
};

static void *set_beamforming(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t type;
        uint8_t dat[];
    } __attribute__((packed)) set_beamforming_def;
    set_beamforming_def *args = (set_beamforming_def *)data;
    database_value tmp;
    uint8_t beam_id;
    int32_t database;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    int forward_len = 0;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xD8;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        switch (args->type)
        {
        case BEAM_SHADOW:
        case BEAM_PRIOR:
            forward_len = 1 + 9;
            break;
        case BEAM_REFNG:
        case BEAM_MICNG:
            forward_len = 1 + 2;
            break;
        case BEAM_MIC_CALIB:
            forward_len = 1 + 1; // extra 0 is useless
            break;
        case BEAM_HOLD_TIME:
        case BEAM_SMOOTH_TIME:
            forward_len = 1 + 2;
            break;
        case BEAM_OUT_CHN:
            forward_len = 1 + 2;
            break;
        case BEAM_POLARITY:
            forward_len = 1 + 5;
            break;
        case BEAM_CLEAR_GAIN:
            forward_len = 1 + 1; // extra 0 is useless
            break;
        case BEAM_REF_STEP:
            forward_len = 1 + 4;
            break;
        case BEAM_INSTALL:
            forward_len = 1 + 1;
            break;
        case BEAM_TUNE_MODE:
            forward_len = 1 + 1;
            break;
        case BEAM_OUT_GAIN:
            forward_len = 1 + 2;
            break;
        case BEAM_WIDTH_LVL:
            forward_len = 1 + 1;
            break;
        case BEAM_INST_HEIGHT:
            forward_len = 1 + 2;
            break;
        case BEAM_ATM_DECAY:
            forward_len = 1 + 2;
            break;
        case BEAM_ATM_PRIOR:
            forward_len = 1 + 2;
            break;
        case BEAM_ATM_ATTA:
        case BEAM_ATM_RELS:
            forward_len = 1 + 2;
            break;
        case BEAM_WIDTH:
            forward_len = 1 + 2;
            break;
        case BEAM_COORDNT:
            forward_len = 1 + 10;
            break;
        case BEAM_ATM_GAIN:
            forward_len = 1 + 2;
            break;

        case BEAM_MAN_CALIB:
            forward_len = 1 + 3;
            break;
        default:
            break;
        }
        if (forward_len)
            bsp_ad2428_s_comm(&a2b_comm, data, forward_len, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    if (YM_PROTO_SLAVE_ID(device) >= BEAM_INSTANCE_NUMBER)
    {
        log_w("beam id set err. device = %hhu", device);
        return NULL;
    }
#if defined(BD_MIXER_6_1MIC_ARRAY) || defined(BD_MIXER_WX_128) || defined(BD_MIXER_YOU_YAN_6_1MIC)
    beam_id = 0;
#else
    beam_id = YM_PROTO_SLAVE_ID(device) + ((YM_PROTO_CORE_ID(device) == 2) ? BEAM_INSTANCE_NUMBER : (uint8_t)(0u));
#endif /* BD_MIXER_6_1MIC_ARRAY || BD_MIXER_WX_128 || BD_MIXER_YOU_YAN_6_1MIC */
    database = BEAM_BASE_INDEX + beam_id * BEAM_PARAM_MAX;

    switch (args->type)
    {
    case BEAM_SHADOW:
    {
    }
    break;
    case BEAM_PRIOR:
    {
    }
    break;
    case BEAM_REFNG:
    {
    }
    break;
    case BEAM_MICNG:
    {
    }
    break;
    case BEAM_MIC_CALIB:
    {
    }
    break;
    case BEAM_MIC_COMPE:
    {
    }
    break;
    case BEAM_MIC_NOISE:
    {
    }
    break;
    case BEAM_VAD_FLAG:
    {
    }
    break;
    case BEAM_HOLD_TIME:
    {
    }
    break;
    case BEAM_SMOOTH_TIME:
    {
    }
    break;
    case BEAM_OUT_CHN:
    {
        /* 波束通道 */
        database_write_no_callback(database + beam_channel0_out_id_index, (database_value){.u32 = (uint32_t)(args->dat[0])});
        database_write(database + beam_channel1_out_id_index, (database_value){.u32 = (uint32_t)(args->dat[1])});
        break;
    }
    case BEAM_POLARITY:
    {
        /* 波束模式 */
        typedef struct
        {
            uint8_t track_mode;
            int16_t theta;
            int16_t phi;
        } __attribute__((packed)) set_beamforming_polarity_def;
        set_beamforming_polarity_def *_args = (set_beamforming_polarity_def *)args->dat;
        if (_args->track_mode >= BEAM_TRACK_MAXI_TYPES)
            log_w("beam track mode set err.index:%d", _args->track_mode);
        else
        {
            _args->theta = (int16_t)be16toh(_args->theta);
            _args->phi = (int16_t)be16toh(_args->phi);
            database_write_no_callback(database + beam_track_z_axis_index, (database_value){.i32 = (int32_t)_args->theta});
            database_write_no_callback(database + beam_track_x_axis_index, (database_value){.i32 = (int32_t)_args->phi});
            database_write(database + beam_track_mode_index, (database_value){.u32 = (uint32_t)(_args->track_mode)});
        }
        break;
    }
    case BEAM_CLEAR_GAIN:
    {
    }
    break;
    case BEAM_REF_STEP:
    {
    }
    break;
    case BEAM_INSTALL:
    {
        tmp.i32 = args->dat[0];
        // database_write(beam_install_mode_index, tmp);
    }
    break;
    case BEAM_TUNE_MODE:
    {
        database_write(database + beam_mode_index, (database_value){.u32 = (uint32_t)(args->dat[0])});
        break;
    }
    case BEAM_OUT_GAIN:
    {
    }
    break;
    case BEAM_WIDTH_LVL:
    {
        /* 波束宽度 */
        database_write(database + beam_width_level_index, (database_value){.u32 = (uint32_t)(args->dat[0])});
        break;
    }
    case BEAM_INST_HEIGHT:
    {
    }
    break;
    case BEAM_ATM_DECAY:
    {
    }
    break;
    case BEAM_ATM_PRIOR:
    {
    }
    break;
    case BEAM_ATM_ATTA:
    {
        typedef struct
        {
            uint16_t attack_time;
        } __attribute__((packed)) set_beamforming_attack_time_def;
        set_beamforming_attack_time_def *_args = (set_beamforming_attack_time_def *)args->dat;
        tmp.u32 = (uint32_t)be16toh(_args->attack_time);
        database_write(database + beam_attack_time_index, tmp);
        break;
    }
    case BEAM_ATM_RELS:
    {
        typedef struct
        {
            uint16_t release_time;
        } __attribute__((packed)) set_beamforming_release_time_def;
        set_beamforming_release_time_def *_args = (set_beamforming_release_time_def *)args->dat;
        tmp.u32 = (uint32_t)be16toh(_args->release_time);
        database_write(database + beam_release_time_index, tmp);
        break;
    }
    case BEAM_WIDTH:
    {
        database_write(database + beam_area1_width_index + args->dat[0], (database_value){.u32 = (uint32_t)(args->dat[1])});
        break;
    }
    case BEAM_COORDNT:
    {
        typedef struct
        {
            uint8_t index;
            uint8_t enable;
            int16_t x1;
            int16_t y1;
            int16_t x2;
            int16_t y2;
        } __attribute__((packed)) set_beamforming_coordnt_def;
        set_beamforming_coordnt_def *_args = (set_beamforming_coordnt_def *)args->dat;
        tmp.i32 = (int16_t)be16toh(_args->x1);
        database_write_no_callback(database + beam_area1_x1_axis_index + _args->index, tmp);
        tmp.i32 = (int16_t)be16toh(_args->y1);
        database_write_no_callback(database + beam_area1_y1_axis_index + _args->index, tmp);
        tmp.i32 = (int16_t)be16toh(_args->x2);
        database_write_no_callback(database + beam_area1_x2_axis_index + _args->index, tmp);
        tmp.i32 = (int16_t)be16toh(_args->y2);
        database_write_no_callback(database + beam_area1_y2_axis_index + _args->index, tmp);
        tmp.u32 = (uint32_t)(_args->enable);
        database_write(database + beam_area1_enabled_index + _args->index, tmp);
        break;
    }
    case BEAM_ATM_GAIN:
    {
        database_write(database + beam_area1_gain_index + args->dat[0], (database_value){.i32 = (int32_t)(args->dat[1])});
        break;
    }
    case BEAM_MAN_CALIB:
    {
    }
    break;
    default:
        break;
    }
    return NULL;
}

static void *get_beamforming(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t type;
        uint8_t dat[];
    } __attribute__((packed)) get_beamforming_def;
    get_beamforming_def *args = (get_beamforming_def *)data;
    uint8_t beam_id;
    int32_t database;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    int forward_len = 0;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xD9;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        switch (args->type)
        {
        case BEAM_SHADOW:
        case BEAM_PRIOR:
            forward_len = 1 + 1;
            a2b_comm.ackl = 14;
            break;
        case BEAM_REFNG:
        case BEAM_MICNG:
            forward_len = 1 + 0;
            a2b_comm.ackl = 6;
            break;
        case BEAM_MIC_COMPE:
            forward_len = 1 + 1;
            a2b_comm.ackl = 6;
            break;
        case BEAM_MIC_NOISE:
            forward_len = 1 + 1;
            a2b_comm.ackl = 6;
            break;
        case BEAM_VAD_FLAG:
            forward_len = 1 + 0;
            a2b_comm.ackl = 5;
            break;
        case BEAM_HOLD_TIME:
        case BEAM_SMOOTH_TIME:
            forward_len = 1 + 0;
            a2b_comm.ackl = 6;
            break;
        case BEAM_OUT_CHN:
            forward_len = 1 + 0;
            a2b_comm.ackl = 6;
            break;
        case BEAM_POLARITY:
            forward_len = 1 + 0;
            a2b_comm.ackl = 9;
            break;
        case BEAM_REF_STEP:
            forward_len = 1 + 0;
            a2b_comm.ackl = 8;
            break;
        case BEAM_INSTALL:
            forward_len = 1 + 0;
            a2b_comm.ackl = 5;
            break;
        case BEAM_OUT_GAIN:
            forward_len = 1 + 0;
            a2b_comm.ackl = 6;
            break;
        case BEAM_WIDTH_LVL:
            forward_len = 1 + 0;
            a2b_comm.ackl = 5;
            break;
        case BEAM_INST_HEIGHT:
            forward_len = 1 + 0;
            a2b_comm.ackl = 6;
            break;
        case BEAM_ATM_DECAY:
            forward_len = 1 + 1;
            a2b_comm.ackl = 5;
            break;
        case BEAM_ATM_PRIOR:
            forward_len = 1 + 1;
            a2b_comm.ackl = 5;
            break;
        case BEAM_ATM_ATTA:
        case BEAM_ATM_RELS:
            forward_len = 1 + 0;
            a2b_comm.ackl = 6;
            break;
        case BEAM_WIDTH:
            forward_len = 1 + 1;
            a2b_comm.ackl = 5;
            break;
        case BEAM_COORDNT:
            forward_len = 1 + 1;
            a2b_comm.ackl = 13;
            break;
        case BEAM_ATM_GAIN:
            forward_len = 1 + 1;
            a2b_comm.ackl = 5;
            break;
        default:
            break;
        }
        if (forward_len)
        {
            MALLOC_YMLINK_MESSAGE();
            YMLINK_MESSAGE_HEAD(msg);

            bsp_ad2428_s_comm(&a2b_comm, data, forward_len, &msg->payload[msg->len], a2b_comm.ackl - 4);
            msg->len += (a2b_comm.ackl - 4);

            YMLINK_MESSAGE_TAIL(msg);
            YMLINK_MESSAGE_APPEND(msg);
        }
        else
        {
            return NULL;
        }
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    if (YM_PROTO_SLAVE_ID(device) >= BEAM_INSTANCE_NUMBER)
    {
        log_w("beam id set err. device = %hhu", device);
        return NULL;
    }
#if defined(BD_MIXER_6_1MIC_ARRAY) || defined(BD_MIXER_WX_128) || defined(BD_MIXER_YOU_YAN_6_1MIC)
    beam_id = 0;
#else
    beam_id = YM_PROTO_SLAVE_ID(device) + ((YM_PROTO_CORE_ID(device) == 2) ? BEAM_INSTANCE_NUMBER : (uint8_t)(0u));
#endif /* BD_MIXER_6_1MIC_ARRAY || BD_MIXER_WX_128 || BD_MIXER_YOU_YAN_6_1MIC */
    database = BEAM_BASE_INDEX + beam_id * BEAM_PARAM_MAX;

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    // TODO:
    switch (args->type)
    {
    case BEAM_SHADOW: /* dat[0] is node phase */
        msg->len += 14 - 4;
        break;
    case BEAM_PRIOR: /* dat[0] is node phase */
        msg->len += 14 - 4;
        break;
    case BEAM_REFNG: /* dat[0] is node phase */
        msg->len += 6 - 4;
        break;
    case BEAM_MICNG: /* dat[0] is node phase */
        msg->len += 6 - 4;
        break;
    case BEAM_MIC_COMPE: /* dat[0] is node phase */
        msg->len += 6 - 4;
        break;
    case BEAM_MIC_NOISE: /* dat[0] is node phase */
        msg->len += 6 - 4;
        break;
    case BEAM_VAD_FLAG: /* dat[0] is node phase */
        msg->len += 5 - 4;
        break;
    case BEAM_HOLD_TIME: /* dat[0] is node phase */
        msg->len += 6 - 4;
        break;
    case BEAM_SMOOTH_TIME: /* dat[0] is node phase */
        msg->len += 6 - 4;
        break;
    case BEAM_POLARITY: /* dat[0] is node phase */
    {
        typedef struct
        {
            uint8_t track_mode;
            int16_t theta;
            int16_t phi;
        } __attribute__((packed)) set_beamforming_polarity_def;
        int32_t theta, phi;
        set_beamforming_polarity_def *_args = (set_beamforming_polarity_def *)(&msg->payload[msg->len]);
        _args->track_mode = (uint8_t)(database_get(database + beam_track_mode_index)->u32);
        theta = database_get(database + beam_track_z_axis_index)->i32;
        phi = database_get(database + beam_track_x_axis_index)->i32;
        _args->theta = (int16_t)htobe16((int16_t)theta);
        _args->phi = (int16_t)htobe16((int16_t)phi);
        msg->len += 5;
        break;
    }
    case BEAM_REF_STEP: /* dat[0] is node phase */
        msg->len += 8 - 4;
        break;
    case BEAM_INSTALL: /* dat[0] is node phase */
        // msg->payload[msg->len] = (uint8_t)database_get(beam_install_mode_index)->i32;
        msg->len += 5 - 4;
        break;
    case BEAM_TUNE_MODE:
    {
        msg->payload[msg->len++] = (uint8_t)(database_get(database + beam_mode_index)->u32);
        break;
    }
    case BEAM_OUT_GAIN: /* dat[0] is node phase */
        msg->len += 6 - 4;
        break;
    case BEAM_WIDTH_LVL: /* dat[0] is node phase */
    {
        /* 波束宽度 */
        msg->payload[msg->len++] = (uint8_t)(database_get(database + beam_width_level_index)->u32);
        break;
    }
    case BEAM_INST_HEIGHT:
        msg->len += 6 - 4;
        break;
    case BEAM_ATM_DECAY:
        msg->len += 5 - 4;
        break;
    case BEAM_ATM_PRIOR:
        msg->len += 5 - 4;
        break;
    case BEAM_ATM_ATTA:
    {
        typedef struct
        {
            uint16_t attack_time;
        } __attribute__((packed)) set_beamforming_attack_time_def;
        set_beamforming_attack_time_def *_args = (set_beamforming_attack_time_def *)(&msg->payload[msg->len]);
        _args->attack_time = (uint16_t)htobe16((uint16_t)(database_get(database + beam_attack_time_index)->u32));
        msg->len += 2;
        break;
    }
    case BEAM_ATM_RELS:
    {
        typedef struct
        {
            uint16_t release_time;
        } __attribute__((packed)) set_beamforming_release_time_def;
        set_beamforming_release_time_def *_args = (set_beamforming_release_time_def *)(&msg->payload[msg->len]);
        _args->release_time = (uint16_t)htobe16((uint16_t)(database_get(database + beam_release_time_index)->u32));
        msg->len += 2;
        break;
    }
    case BEAM_WIDTH:
    {
        int32_t area_id = (int32_t)(args->dat[0]);
        msg->payload[msg->len++] = (uint8_t)(database_get(database + beam_area1_width_index + area_id)->u32);
        break;
    }
    case BEAM_COORDNT:
    {
        typedef struct
        {
            uint8_t index;
            uint8_t enable;
            int16_t x1;
            int16_t y1;
            int16_t x2;
            int16_t y2;
        } __attribute__((packed)) get_beamforming_coordnt_def;
        get_beamforming_coordnt_def *_args = (get_beamforming_coordnt_def *)(&msg->payload[msg->len]);
        _args->index = (int32_t)(args->dat[0]);
        _args->enable = (uint8_t)(database_get(database + beam_area1_enabled_index + _args->index)->u32);
        _args->x1 = (int16_t)htobe16((int16_t)(database_get(database + beam_area1_x1_axis_index + _args->index)->i32));
        _args->y1 = (int16_t)htobe16((int16_t)(database_get(database + beam_area1_y1_axis_index + _args->index)->i32));
        _args->x2 = (int16_t)htobe16((int16_t)(database_get(database + beam_area1_x2_axis_index + _args->index)->i32));
        _args->y2 = (int16_t)htobe16((int16_t)(database_get(database + beam_area1_y2_axis_index + _args->index)->i32));
        msg->len += 10;
        break;
    }
    case BEAM_OUT_CHN:
    {
        msg->payload[msg->len++] = (uint8_t)(database_get(database + beam_channel0_out_id_index)->u32);
        msg->payload[msg->len++] = (uint8_t)(database_get(database + beam_channel1_out_id_index)->u32);
        break;
    }
    case BEAM_ATM_GAIN:
    {
        int32_t area_id = (int32_t)(args->dat[0]);
        msg->payload[msg->len++] = (uint8_t)(database_get(database + beam_area1_gain_index + area_id)->u32);
        break;
    }
    default:
    break;
    }
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

#endif

static void *set_led_func(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t fun;
        uint8_t dat[];
    } __attribute__((packed)) set_led_func_def;
    set_led_func_def *args = (set_led_func_def *)data;
    if (args->fun == 0xff)
    {
        // 迪文屏测试
        // dwin_test();
    }
    else
    {
#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
        a2b_comm_misc_t a2b_comm;
        a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
        a2b_comm.node = YM_PROTO_SLAVE_ID(device);
        a2b_comm.core = YM_PROTO_CORE_ID(device);
        a2b_comm.cmd  = 0xDA;
        a2b_comm.ackl = 0;
        a2b_comm.ackt = 0;
        if (YM_PROTO_SLAVE_ID(device))
        {
            bsp_ad2428_s_comm(&a2b_comm, data, 4, NULL, 0);
        }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */
    }
    return NULL;
}

static void *get_led_func(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t fun;
        uint8_t node;
    } __attribute__((packed)) get_led_func_def;
    get_led_func_def *args = (get_led_func_def *)data;
    // TODO:
    (void)args;
    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    // msg->payload[msg->len++] = !get_dwin_test_flag();
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *slave_ota_ctrl(uint8_t device, uint8_t *data)
{
#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xDC;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 2, NULL, 0);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */
    return NULL;
}

static void *slave_ota_prog(uint8_t device, uint8_t *data)
{
#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xDD;
    a2b_comm.ackl = 7;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, NULL, 0, &msg->payload[msg->len], 3);
        msg->len += 3;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */
    return NULL;
}

enum
{
    MIC_AEC_DIS = (0x00),    /* disable mic aec */
    MIC_AEC_MMIN = (0x01),   /* mode 1 */
    MIC_AEC_MMAX = (0x10),   /* mode 16 */
    MIC_AEC_ENER = (0xF9),   /* update aec energy */
    MIC_AEC_STUNE = (0xFA),  /* start aec tune */
    MIC_AEC_PTUNE = (0xFB),  /* stop aec tune */
    MIC_AFC_STUNE = (0xFC),  /* start afc tune */
    MIC_AFC_PTUNE = (0xFD),  /* stop afc tune */
    MIC_AEFC_STUNE = (0xFE), /* start aec+afc tune */
    MIC_AEFC_PTUNE = (0xFF), /* stop aec+afc tune */
};

static void *set_mic_aec(uint8_t device, uint8_t *data)
{
    void aec_afc_set_tune_mode(AEC_AFC_TUNE_MODE mode);
    typedef struct
    {
        uint8_t type;
    } __attribute__((packed)) set_mic_aec_def;
    set_mic_aec_def *args = (set_mic_aec_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;
    short val = args->type;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xEC;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 1, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    switch (args->type)
    {
    case MIC_AEC_ENER:
        aec_afc_set_tune_mode(AEC_ENERGY);
        break;
    case MIC_AEC_STUNE:
        aec_afc_set_tune_mode(AEC_TUNE);
        break;
    case MIC_AEC_PTUNE:
        aec_afc_set_tune_mode(STOP_TUNE);
        break;
    case MIC_AFC_STUNE:
        aec_afc_set_tune_mode(AFC_TUNE);
        break;
    case MIC_AFC_PTUNE:
        aec_afc_set_tune_mode(STOP_TUNE);
        break;
    case MIC_AEFC_STUNE:
        aec_afc_set_tune_mode(AEC_AFC_TUNE);
        break;
    case MIC_AEFC_PTUNE:
        aec_afc_set_tune_mode(STOP_TUNE);
        break;
    default:
        if (MIC_AEC_MMAX >= args->type)
        {

            database_value tmp = {.i32 = val};
            database_write(mic_aec_mode_ms_index + base, tmp);
        }
        break;
    }
    return NULL;
}

static void *get_mic_aec(uint8_t device, uint8_t *data)
{
#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xED;
    a2b_comm.ackl = 5;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, NULL, 0, &msg->payload[msg->len], 1);
        msg->len += 1;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;
    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    msg->payload[msg->len++] = database_get(mic_aec_mode_ms_index + base)->i32;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}
enum
{
    AEC_CTRL_MIC = (0x00),   /* mic number */
    AEC_CTRL_SPK = (0x01),   /* speaker number */
    AEC_CTRL_ANL = (0x02),   /* aec noise level */
    AEC_CTRL_ANFN = (0x03),  /* anf filter number */
    AEC_CTRL_ANFD = (0x04),  /* anf filter depth */
    AEC_CTRL_AEC0D = (0x05), /* aec0 delay */
    AEC_CTRL_AEC0L = (0x06), /* aec0 length */
    AEC_CTRL_AEC1D = (0x07), /* aec1 delay */
    AEC_CTRL_AEC1L = (0x08), /* aec1 length */
    AEC_CTRL_AEC0E = (0x09), /* aec0 energy */
    AEC_CTRL_AEC1E = (0x0A), /* aec1 energy */
    AEC_CTRL_CMFN = (0x0B),  /* aec comfortable noise */
    AEC_CTRL_AECDM = (0x0C), /* aec downmix mode */
    AEC_CTRL_AFCP = (0x0D),  /* afc path */
    AEC_CTRL_HSHL = (0x0E),  /* aec high-shelf level */
    AFC_FREQ_SHIFT = (0x0F), /* afc frequency shift */
    AFC_DET_MODE = (0x10),   /* afc detection mode */
    AEC_BKG_DECAY = (0x11),  /* AEC1 background decay level */
    AEC_BYPASS = (0x12),     /* aec1 bypass mic with linear filtering */
    AFC_FILTER = (0xFD),     /* afc filter */
    TUNE_BLOCK = (0xFE),     /* aec tune block sequence */
    TUNE_RESULT = (0xFF),    /* aec tune result */
};

static void *set_aec_ctrl(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t type;
        uint16_t val;
    } __attribute__((packed)) set_aec_ctrl_def;
    set_aec_ctrl_def *args = (set_aec_ctrl_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;
    short val = htobe16(args->val);

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xEE;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 3, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    switch (args->type)
    {
    case AEC_CTRL_MIC:
        break;
    case AEC_CTRL_SPK:
        break;
    case AEC_CTRL_ANL:
    {
        database_value tmp = {.i32 = val};
        database_write(aec_noise_level_ms_index + base, tmp);
    }
    break;
    case AEC_CTRL_ANFN:
    {
        database_value tmp = {.i32 = val};
        database_write(anf_filter_num_ms_index + base, tmp);
    }
    break;
    case AEC_CTRL_ANFD:
    {
        database_value tmp = {.i32 = val};
        database_write(anf_filter_dep_ms_index + base, tmp);
    }
    break;
    case AEC_CTRL_AEC0D:
    {
        val = val < 0 ? 0 : val;
        val = val > 255 ? 255 : val;
        database_value tmp = {.i32 = val};
        database_write(aec0_delay_ms_index + base, tmp);
    }
    break;
    case AEC_CTRL_AEC0L:
    {
        database_value tmp = {.i32 = val};
        database_write(aec0_length_ms_index + base, tmp);
    }
    break;
    case AEC_CTRL_AEC1D:
    {
        val = val < 0 ? 0 : val;
        val = val > 255 ? 255 : val;
        database_value tmp = {.i32 = val};
        database_write(aec1_delay_ms_index + base, tmp);
    }
    break;
    case AEC_CTRL_AEC1L:
    {
        database_value tmp1 = {.i32 = val};
        database_write(aec1_length_ms_index + base, tmp1);
    }
    break;
    case AEC_CTRL_AEC0E:
    {
        database_value tmp1 = {.i32 = val};
        database_write(aec_0_energy_ms_index + base, tmp1);
    }
    break;
    case AEC_CTRL_AEC1E:
    {
        database_value tmp1 = {.i32 = val};
        database_write(aec_1_energy_ms_index + base, tmp1);
    }
    break;
    case AEC_CTRL_CMFN:
    {
        database_value tmp = {.i32 = val};
        database_write(aec_comfor_noise_ms_index + base, tmp);
    }
    break;
    case AEC_CTRL_AECDM:
    {
        database_value tmp = {.i32 = val};
        database_write(aec_dwmx_mode_ms_index + base, tmp);
    }
    break;
    case AEC_CTRL_AFCP:
    {
        database_value tmp = {.i32 = val};
        database_write(afc_path_mode_ms_index + base, tmp);
    }
    break;
    case AEC_CTRL_HSHL:
    {
        database_value tmp = {.i32 = val};
        database_write(aec_highshelf_level_ms_index + base, tmp);
    }
    break;
    case AFC_FREQ_SHIFT:
        val = val < -120 ? -120 : val;
        val = val > 120 ? 120 : val;
        {
            database_value tmp = {.i32 = val};
            database_write(afc_freq_shift_ms_index + base, tmp);
        }
        break;
    case AFC_DET_MODE:
        // TODO:近场麦克风检测
        val = val < 0 ? 0 : val;
        val = val > 3 ? 3 : val;
        {
            database_value tmp = {.i32 = val};
            database_write(howling_enable_ms_index + base, tmp);
        }
        break;
    case AEC_BKG_DECAY:
        {
            database_value tmp = {.i32 = val};
            database_write(aec_bkg_decay_ms_index + base, tmp);
        }
        break;
    case AEC_BYPASS:
        database_value tmp = {.i32 = val};
        database_write(aec_bypass_ms_index + MIXER1_BASE_INDEX, tmp);
        database_write(aec_bypass_ms_index + MIXER2_BASE_INDEX, tmp);
        break;
    default:
        break;
    }
    return NULL;
}

static void *get_aec_ctrl(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t type;
        uint8_t high;
        uint8_t low;
    } __attribute__((packed)) get_aec_ctrl_def;
    get_aec_ctrl_def *args = (get_aec_ctrl_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;
    extern void *ym_mixer_master_handle;
    extern void *ym_mixer_slave_handle;
    void *ym_mixer_handle = device == 0x01 ? ym_mixer_master_handle : ym_mixer_slave_handle;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xEF;
    a2b_comm.ackl = 6;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 3, &msg->payload[msg->len], 2);
        msg->len += 2;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    short *val = (short *)&msg->payload[msg->len];
    latency_e latency = (device == 0x01) ? LATENCY_5MS : LATENCY_10MS;
    switch (args->type)
    {
    case AEC_CTRL_MIC:
        // get aec mic number
        *val = htobe16((short)database_get(aec_mic_num_ms_index + base)->i32);
        break;
    case AEC_CTRL_SPK:
        // get aec spk number
        *val = htobe16((short)database_get(aec_spk_num_ms_index + base)->i32);
        break;
    case AEC_CTRL_ANL:
        *val = htobe16((short)database_get(aec_noise_level_ms_index + base)->i32);
        break;
    case AEC_CTRL_ANFN:
        *val = htobe16((short)database_get(anf_filter_num_ms_index + base)->i32);
        break;
    case AEC_CTRL_ANFD:
        *val = htobe16((short)database_get(anf_filter_dep_ms_index + base)->i32);
        break;
    case AEC_CTRL_AEC0D:
        *val = htobe16((short)database_get(aec0_delay_ms_index + base)->i32);
        break;
    case AEC_CTRL_AEC0L:
        *val = htobe16((short)database_get(aec0_length_ms_index + base)->i32);
        break;
    case AEC_CTRL_AEC1D:
        *val = htobe16((short)database_get(aec1_delay_ms_index + base)->i32);
        break;
    case AEC_CTRL_AEC1L:
        *val = htobe16((short)database_get(aec1_length_ms_index + base)->i32);
        break;
    case AEC_CTRL_AEC0E:
        *val = htobe16((short)database_get(aec_0_energy_ms_index + base)->i32);
        break;
    case AEC_CTRL_AEC1E:
        *val = htobe16((short)database_get(aec_1_energy_ms_index + base)->i32);
        break;
    case AEC_CTRL_CMFN:
        *val = htobe16((short)database_get(aec_comfor_noise_ms_index + base)->i32);
        break;
    case AEC_CTRL_AECDM:
        *val = htobe16((short)database_get(aec_dwmx_mode_ms_index + base)->i32);
        break;
    case AEC_CTRL_AFCP:
        *val = htobe16((short)database_get(afc_path_mode_ms_index + base)->i32);
        break;
    case AEC_CTRL_HSHL:
        *val = htobe16((short)database_get(aec_highshelf_level_ms_index + base)->i32);
        break;
    case AFC_FREQ_SHIFT:
        *val = htobe16((short)database_get(afc_freq_shift_ms_index + base)->i32);
        break;
    case AFC_DET_MODE:
        *val = htobe16((short)database_get(howling_enable_ms_index + base)->i32);
        break;
    case AEC_BKG_DECAY:
        *val = htobe16((short)database_get(aec_bkg_decay_ms_index + base)->i32);
        break;
    case AEC_BYPASS:
        *val = htobe16((short)database_get(aec_bypass_ms_index + base)->i32);
        break;
    case AFC_FILTER:
    {
        unsigned short howling_freq[YM_MIXER_ANF_MAX_PEQ_FILTERS_NUM];
        short howling_gain[YM_MIXER_ANF_MAX_PEQ_FILTERS_NUM];
        short howling_Q[YM_MIXER_ANF_MAX_PEQ_FILTERS_NUM];
        int num_howling_freq;
        int drcMakeupGain;
        // TODO:
        //  void set_howling_suppression_filter(unsigned short *restrict howling_freq,
        //                                      short *restrict howling_gain,
        //                                      short *restrict howling_Q,
        //                                      int num_howling_freq);
        mixer_read_howling_info[latency](ym_mixer_handle, howling_freq, howling_gain,
                                howling_Q, &num_howling_freq, &drcMakeupGain);
        // if (num_howling_freq > 0)
        // {
        //     set_howling_suppression_filter(howling_freq, howling_gain, howling_Q, num_howling_freq);
        // }

        database_value tmp = {.i32 = num_howling_freq};
        database_write(anf_filter_num_ms_index + base, tmp);
        *val = num_howling_freq;
    }
    break;
    case TUNE_BLOCK:
    {
#define AEC_1MS_SAMPLES (16)
        uint32_t iMax = 0;
        uint32_t blk = 0;
        uint32_t startSample = 0;
        float sumEng = 0.0;
        mixer_lib_lock(base == MIXER1_BASE_INDEX ? 1 : 2);
        mixerGetAECFilterBlkEng[latency](ym_mixer_handle, 0, &iMax, &sumEng, &blk, &startSample);
        mixer_lib_unlock(base == MIXER1_BASE_INDEX ? 1 : 2);
        if (startSample >= AEC_1MS_SAMPLES)
        {
            startSample -= AEC_1MS_SAMPLES;
        }
        if (iMax == 0)
        {
            if (startSample >= AEC_1MS_SAMPLES)
            {
                startSample -= AEC_1MS_SAMPLES;
            }
        }
        msg->payload[2] = (uint8_t)(iMax & 0xff);
        msg->payload[3] = (uint8_t)(startSample / AEC_1MS_SAMPLES);
    }
    break;
    case TUNE_RESULT:
        *val = htobe16((short)database_get(tuning_result_index)->i32);
        break;
    default:
        break;
    }
    msg->len += 2;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_mic_ans(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t mod;
        uint8_t lvl;
    } __attribute__((packed)) set_mic_ans_def;
    set_mic_ans_def *args = (set_mic_ans_def *)data;
    database_value tmp = {.i32 = args->lvl};
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xF0;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 2, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    switch (args->mod)
    {
    case 0xfb: /* aec reference denoise mode */
    {
        database_write(aec_ref_denoise_index, tmp);
    }
    break;
    case 0xfc: /* aec2 denoise mode */
    {
        database_write(aec2_denoise_ms_index + base, tmp);
    }
    break;
    case 0xfd: /* dereverb time */
    {
        database_write(speech_enhance_level_index, tmp);
    }
    break;
    case 0xfe: /* ref noise suppression */
    {
        database_write(speech_restore_level_index, tmp);
    }
    break;
    case 0xff: /* dereverb */
    {
        args->lvl = args->lvl > YM_MATRIX_DEREVERB_MODE5 ? YM_MATRIX_DEREVERB_MODE5 : args->lvl;
        tmp.i32 = args->lvl;
        database_write(dereverb_level_ms_index + base, tmp);
    }
    break;
    default:
        if (0x12 >= args->mod) /* mic noise suppression */
        {
            database_value tmp = {.i32 = args->mod};
            database_write(mic_ans_mode_ms_index + MIXER1_BASE_INDEX, tmp);
            database_write(mic_ans_mode_ms_index + MIXER2_BASE_INDEX, tmp);
        }
        break;
    }
    return NULL;
}

static void *get_mic_ans(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t mod;
        uint8_t lvl;
    } __attribute__((packed)) get_mic_ans_def;
    get_mic_ans_def *args = (get_mic_ans_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xF1;
    a2b_comm.ackl = 5;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 2, &msg->payload[msg->len], 1);
        msg->len += 1;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    switch (args->mod)
    {
    case 0x00: /* mic ans mode */
    {
        // database_value tmp = *database_get(mic_ans_mode_ms_index + base);
        // msg->payload[msg->len++] = tmp.i32 > 12 ? tmp.i32 - 12 : tmp.i32;
        msg->payload[msg->len++] = database_get(mic_ans_mode_ms_index + base)->i32;
    }
    break;
    case 0x01: /* dereverb mode */
        msg->payload[msg->len++] = database_get(dereverb_level_ms_index + base)->i32;
        break;
    case 0x02: /* ref ans mode */
        msg->payload[msg->len++] = 0;
        break;
    case 0x03: /* dereverb rt60 time */
        msg->payload[msg->len++] = 0;
        break;
    case 0xfb: /* aec reference denoise */
        msg->payload[msg->len++] = database_get(aec_ref_denoise_index)->i32;
        break;
    case 0xfc: /* aec2 denoise */
        msg->payload[msg->len++] = database_get(aec2_denoise_ms_index + base)->i32;
        break;
    case 0xfd: /* dereverb time */
        msg->payload[msg->len++] = database_get(speech_enhance_level_index)->i32;
        break;
    case 0xfe: /* ref noise suppression */
        msg->payload[msg->len++] = database_get(speech_restore_level_index)->i32;
        break;
    default:
        msg->payload[msg->len++] = 0;
        break;
    }
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_barcode(uint8_t device, uint8_t *data)
{
    const char *path = "/userdata/barcode";
    uint8_t id[5][5] = {0};
    // 判断path是否存在，不存在则创建文件
    if (access(path, F_OK) != 0)
    {
        FILE *fp = fopen(path, "wb");
        if (fp == NULL)
        {
            log_e("create barcode file err:%s", path);
            return NULL;
        }
        fclose(fp);
    }

    FILE *fp = fopen(path, "rb+");
    int ret = 0;
    if (fp == NULL)
    {
        log_e("open barcode file err:%s", path);
        return NULL;
    }
    ret = fread(id, 1, 25, fp);
    if (ret != 25)
    {
        log_e("read barcode file err:%d", ret);
    }
    int index = 0;
    switch (data[0])
    {
    case 0x00: // 核心板
    case 0x01: // 接口板
    case 0x02: // 功放板1
    case 0x03: // 功放板2
        index = data[0];
        break;
    case 0xff: // 整机
        index = 4;
        break;
    default:
        log_e("set barcode err:%d", data[0]);
        goto __exit;
        break;
    }
    log_i("set barcode:%d", index);
    memcpy(id[index], &data[1], 5);
    ret = fseek(fp, 0, SEEK_SET);
    if (ret != 0)
    {
        log_e("seek barcode file err:%d", ret);
    }
    ret = fwrite(id, 1, 25, fp);
    if (ret != 25)
    {
        log_e("write barcode file err:%d", ret);
    }
__exit:
    ret = fflush(fp);
    if (ret != 0)
    {
        log_e("flush barcode file err:%d", ret);
    }
    ret = fsync(fileno(fp));
    if (ret != 0)
    {
        log_e("sync barcode file err:%d", ret);
    }
    ret = fclose(fp);
    if (ret != 0)
    {
        log_e("close barcode file err:%d", ret);
    }
    return NULL;
}

static void *get_barcode(uint8_t device, uint8_t *data)
{
    const char *path = "/userdata/barcode";
    uint8_t id[5][5] = {0};
    // 判断path是否存在，不存在则创建文件
    if (access(path, F_OK) != 0)
    {
        FILE *fp = fopen(path, "wb");
        if (fp == NULL)
        {
            log_e("create barcode file err:%s", path);
            return NULL;
        }
        fclose(fp);
    }
    FILE *fp = fopen(path, "rb");
    int ret = 0;
    if (fp == NULL)
    {
        log_e("open barcode file err:%s", path);
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);
        msg->len += 5;
        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
    ret |= fread(id, 5, 5, fp);
    ret |= fclose(fp);

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    int index = 0;
    switch (data[0])
    {
    case 0x00: // 核心板
    case 0x01: // 接口板
    case 0x02: // 功放板1
    case 0x03: // 功放板2
        index = data[0];
        break;
    case 0xff: // 整机
        index = 4;
        break;
    default:
        log_e("set barcode err:%d", data[0]);
        break;
    }
    memcpy(&msg->payload[msg->len], id[index], 5);
    msg->len += 5;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

enum
{
    NET_CONFIG = 0x00,
    MAC_CONFIG = 0x00, // MAC address,retrun 6 bytes MAC address
    IP_CONFIG = 0x01,  // mode(DHCP/STATIC)and static IP,mask,gateway,return 13 bytes
    TUNE_KEY_TEST = 0x81,
    USB_HOST_TEST = 0x82,
};

static uint32_t udisk_test_status;

static void *reset_netconfig(uint8_t device, uint8_t *data)
{
    switch (data[0])
    {
    case NET_CONFIG:
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);
        int ret = system("rm /data/net_config;sync;sync;");
        (void)ret;
        msg->payload[msg->len++] = 0;
        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
    break;
    case TUNE_KEY_TEST:
        disable_key_tune = !data[1];
        tune_key_status = 0;
        break;
    case USB_HOST_TEST:
        udisk_test_status = system("/usr/bin/udisk_test.sh");
        break;
    default:
        log_i("reset netconfig err:%d", *data);
        break;
    }
    return NULL;
}

static void *get_netconfig(uint8_t device, uint8_t *data)
{
    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    switch (*data)
    {
    case MAC_CONFIG:
        get_mac_addr(&msg->payload[msg->len]);
        msg->len += 6;
        break;
    case IP_CONFIG:
        get_net_config(&msg->payload[msg->len]);
        msg->len += 13;
        break;
    case TUNE_KEY_TEST:
        msg->payload[msg->len++] = tune_key_status;
        break;
    case USB_HOST_TEST:
        msg->payload[msg->len++] = !udisk_test_status;
        break;

    default:
        break;
    }

    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_48v_pwr(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t chn;
        uint8_t onoff;
    } __attribute__((packed)) set_48v_pwr_def;
    set_48v_pwr_def *args = (set_48v_pwr_def *)data;
    database_value tmp = {.i32 = args->onoff};
    if (args->chn >= 0x08 && args->chn <= 0x0f)
    {
        database_write(mic_48v_gate1_index + args->chn - 0x08, tmp);
        // log_i("set[%d] 48v pwr:%d", args->chn - 0x08, args->onoff);
    }
    else if (args->chn == 0x10)
    {
        // TODO:callback
        database_write(power_232_index, tmp);
    }
    else if (args->chn == 0x11)
    {
        // TODO:callback
#if defined(WEN_XIANG_BOARD)
        bt_pair_callback(0, tmp.i32);
#endif /* end #if defined(WEN_XIANG_BOARD) */
    }
    else if (args->chn == 0x12)
    {
#if defined(WEN_XIANG_BOARD)
        bt_pair_callback(1, tmp.i32);
#endif /* end #if defined(WEN_XIANG_BOARD) */
    }
    else if (args->chn >= 0x20 && args->chn <= 0x2f)
    {
        if (args->chn > 0x27)
            return NULL;
        database_write(mic_boost_gain1_index + args->chn - 0x20, tmp);
    }
    return NULL;
}

static void *get_48v_pwr(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t chn;
    } __attribute__((packed)) get_48v_pwr_def;
    get_48v_pwr_def *args = (get_48v_pwr_def *)data;

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    if (args->chn >= 0x08 && args->chn <= 0x0f)
    {
        msg->payload[msg->len++] = database_get(mic_48v_gate1_index + args->chn - 0x08)->i32;
        // log_i("get[%d] 48v pwr:%d", args->chn - 0x08, msg->payload[msg->len - 1]);
    }
    else if (args->chn == 0x10)
        msg->payload[msg->len++] = database_get(power_232_index)->i32;
    else if (args->chn == 0x11)
        msg->payload[msg->len++] = database_get(wen_xiang_bt_state_index)->i32;
    else if (args->chn == 0x12)
        msg->payload[msg->len++] = database_get(wen_xiang_bt2_state_index)->i32;
#if defined(WEN_XIANG_BOARD)
    else if (args->chn == 0x13)
        msg->payload[msg->len++] = database_get(wen_xiang_bt_count_index)->i32;
#endif /* end WEN_XIANG_BOARD */
    else if (args->chn >= 0x20 && args->chn <= 0x2f)
    {
        if (args->chn > 0x27)
        {
            msg->payload[msg->len++] = 0;
        }
        else
        {
            msg->payload[msg->len++] = database_get(mic_boost_gain1_index + args->chn - 0x20)->i32;
        }
    }
    else
        msg->payload[msg->len++] = 0;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_mode_name(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t mode;
        uint8_t nlen;
        char name[];
    } __attribute__((packed)) set_mode_name_def;
    set_mode_name_def *args = (set_mode_name_def *)data;
    int name_len = args->nlen;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xF8;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 2 + name_len, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    if (args->mode < YM_MIXER_MAX_MODE)
    {
        if (name_len > MIXER_PARAM_MODE_NAME_LEN)
            name_len = MIXER_PARAM_MODE_NAME_LEN;
        memset(nonvolatile_global_cfg.mode_name[args->mode], '\0', MIXER_PARAM_MODE_NAME_LEN);
        memcpy(nonvolatile_global_cfg.mode_name[args->mode], args->name, name_len);
        nonvolatile_global_cfg_save();
    }
    else if (args->mode == 0x22) // uac name
    {
        if (name_len > MIXER_PARAM_SCREEN_NAME_LEN)
            name_len = MIXER_PARAM_SCREEN_NAME_LEN;
        memset(nonvolatile_global_cfg.usb_audio_class_name, '\0', MIXER_PARAM_SCREEN_NAME_LEN);
        memcpy(nonvolatile_global_cfg.usb_audio_class_name, args->name, name_len);
        nonvolatile_global_cfg_save();
    }
    else
        log_w("set mode name err.mode:%d", args->mode);
    return NULL;
}

static void *get_mode_name(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t mode;
    } __attribute__((packed)) get_mode_name_def;
    get_mode_name_def *args = (get_mode_name_def *)data;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xF9;
    a2b_comm.ackl = 20;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 1, &msg->payload[msg->len], MIXER_PARAM_MODE_NAME_LEN);
        msg->len += MIXER_PARAM_MODE_NAME_LEN;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    if (args->mode < YM_MIXER_MAX_MODE)
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);
        memcpy(&msg->payload[msg->len], nonvolatile_global_cfg.mode_name[args->mode], MIXER_PARAM_MODE_NAME_LEN);
        msg->len += MIXER_PARAM_MODE_NAME_LEN;
        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
    else if (args->mode == 0x22) // uac name
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);
        memcpy(&msg->payload[msg->len], nonvolatile_global_cfg.usb_audio_class_name, MIXER_PARAM_SCREEN_NAME_LEN);
        msg->len += MIXER_PARAM_SCREEN_NAME_LEN;
        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
    return NULL;
}

static void *set_chn_attr(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t chn;
        uint8_t attr;
    } __attribute__((packed)) set_chn_attr_def;
    set_chn_attr_def *args = (set_chn_attr_def *)data;
    database_value tmp = {.i32 = args->attr};
    if (args->chn < MIXER_OUTPUT_CHN_MAX)
        database_write(ichn1_attr_index + args->chn, tmp);
    return NULL;
}

static void *get_chn_attr(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t chn;
    } __attribute__((packed)) get_chn_attr_def;
    get_chn_attr_def *args = (get_chn_attr_def *)data;
    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    msg->payload[msg->len++] = args->chn;
    msg->payload[msg->len++] = database_get(ichn1_attr_index + args->chn)->i32;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *factory_test(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t ctrl;
        uint8_t data;
    } __attribute__((packed)) factory_test_def;
    factory_test_def *args = (factory_test_def *)data;
    if (args->ctrl == 0x03)
    {
#ifndef HP_MUTE_GATE_DISABLED
        set_gpio(hp_mute_gate, args->data);
#endif
    }
    return NULL;
}

static void *get_factory_test(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t ctrl;
        uint8_t data;
    } __attribute__((packed)) get_factory_test;
    get_factory_test *args = (get_factory_test *)data;
    if (args->ctrl == 0x03)
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);
#ifndef HP_MUTE_GATE_DISABLED
        msg->payload[msg->len++] = (uint8_t)(get_gpio(hp_mute_gate));
#else
        msg->payload[msg->len++] = 0;
#endif
        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
    return NULL;
}

static void *get_a2b_slave_cnt(uint8_t device, uint8_t *data)
{
    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    msg->payload[msg->len++] = database_get(a2b_slave_nodes_index)->i32;
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static void *set_a2b_slave_clk(uint8_t device, uint8_t *data)
{
#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0x91;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, NULL, 0, NULL, 0);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */
    return NULL;
}

static void *get_a2b_slave_beamformer_angle(uint8_t device, uint8_t *data)
{
#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0x92;
    a2b_comm.ackl = 9;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, NULL, 0, &msg->payload[msg->len], 5);

        msg->len += 5;
        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */
    return NULL;
}

static void *set_a2b_slave_ref_sig_level(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t snode;
        uint8_t level;
    } __attribute__((packed)) set_a2b_slave_ref_sig_level_def;
    set_a2b_slave_ref_sig_level_def *args = (set_a2b_slave_ref_sig_level_def *)data;
    // TODO:
    (void)args;
    return NULL;
}

static void *get_a2b_slave_source_voice_angle(uint8_t device, uint8_t *data)
{
#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0x94;
    a2b_comm.ackl = 9;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, NULL, 0, &msg->payload[msg->len], 5);

        msg->len += 5;
        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */
    return NULL;
}

static void *get_a2b_slave_save_params(uint8_t device, uint8_t *data)
{
#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0x95;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, NULL, 0, NULL, 0);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */
    return NULL;
}

static void *get_a2b_node_attr(uint8_t device, uint8_t *data)
{
    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    msg->payload[msg->len++] = 1; /* always master node */
    YMLINK_MESSAGE_TAIL(msg);
    YMLINK_MESSAGE_APPEND(msg);
}

static int trans_bank_check_status(ymlink_message_p msg, struct ymlink_stack *ym_stack)
{
    int ret = 1;
    typedef struct
    {
        uint8_t bank;
        uint8_t sum;
        uint8_t len;
    } upgared_payload;
    /**
     * 判断接收data是否满足len
     */
    if (ym_stack->rx_pos >= 3)
    {
        upgared_payload *args = (upgared_payload *)msg->payload;
        if (ym_stack->rx_pos == (args->len + 3))
        {
            ret = 0;
        }
    }
    return ret;
}

#if defined(ANDROID_WINDOWS_VOL_SYNC)
extern void android_playback_vol_set(int vol);
extern int android_playback_vol_get(void);
extern int uac_capture_vol_get(void);
extern void uac_capture_vol_set(int vol);
#endif /* end ANDROID_WINDOWS_VOL_SYNC */
static void *set_chn_vol(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t chn;
        union
        {
            int8_t i8;
            uint8_t u8;
        } vol;
    } __attribute__((packed)) set_chn_vol_def;
    set_chn_vol_def *args = (set_chn_vol_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xA0;
    a2b_comm.ackl = 0;
    a2b_comm.ackt = 0;
    if (YM_PROTO_SLAVE_ID(device))
    {
        bsp_ad2428_s_comm(&a2b_comm, data, 2, NULL, 0);
        return NULL;
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    if (args->chn >= IN_CHN_MIN && args->chn <= IN_CHN_MAX)
    {
        database_value tmp = {.i32 = args->vol.u8};
        database_write(input_channel_vol1_index + args->chn, tmp);
    }
    else if (args->chn >= OUT_CHN_MIN && args->chn <= OUT_CHN_MAX)
    {
        args->chn -= OUT_CHN_MIN;
        database_value tmp = {.i32 = args->vol.u8};
        database_write(output_channel_vol1_ms_index + base + args->chn, tmp);
    }
    else if (args->chn == MIC_GLOBAL_VOL)
    {
        database_value tmp = {.i32 = args->vol.u8};
        database_write(mic_global_sys_vol_index, tmp);
    }
    else if (args->chn == OUT_GLOBAL_VOL)
    {
        database_value tmp = {.i32 = args->vol.u8};
        database_write(global_sys_out_vol_index, tmp);
    }
    else if (args->chn >= IN_CHN17 && args->chn <= IN_CHN24)
    {
        database_value tmp = {.i32 = args->vol.u8};
        args->chn -= IN_CHN17;
        database_write(input_channel_vol17_index + args->chn, tmp);
    }
    else if (args->chn == ALL_SPK_VOL)
    {
        database_value tmp = {.i32 = args->vol.u8};
        database_write(global_skp_out_vol_index, tmp);
    }
    else if (args->chn == TUNE_MODE_VOL)
    {
        database_value tmp = {.i32 = args->vol.u8};
        database_write(global_tune_mode_vol_index, tmp);
    }
    else if (args->chn == AEC_OUT_VOL)
    {
        database_value tmp = {.i32 = args->vol.u8};
        database_write(global_aec_out_vol_index, tmp);
    }
    else if (args->chn == AFC_OUT_VOL)
    {
        database_value tmp = {.i32 = args->vol.u8};
        database_write(global_afc_out_vol_index, tmp);
    }
    else if (args->chn == ANDROID_OUT_VOL)
    {
#if defined(ANDROID_WINDOWS_VOL_SYNC)
        android_playback_vol_set(args->vol.u8);
#endif /* end ANDROID_WINDOWS_VOL_SYNC */
    }
    else if (args->chn == UAC_CAPTURE_VOL)
    {
#if defined(ANDROID_WINDOWS_VOL_SYNC)
#endif /* end ANDROID_WINDOWS_VOL_SYNC */
    }
    else if (args->chn >= IN_CHN_STEP_MIN && args->chn <= IN_CHN_STEP_MAX)
    {
        args->chn -= IN_CHN_STEP_MIN;
        database_value tmp = {.i32 = args->vol.i8 + database_get(input_channel_vol1_index + args->chn)->i32};
        tmp.i32 = tmp.i32 < 0 ? 0 : tmp.i32;
        tmp.i32 = tmp.i32 > 220 ? 220 : tmp.i32;
        database_write(input_channel_vol1_index + args->chn, tmp);
    }
    else if (args->chn >= OUT_CHN_STEP_MIN && args->chn <= OUT_CHN_STEP_MAX)
    {
        args->chn -= OUT_CHN_STEP_MIN;
        database_value tmp = {.i32 = args->vol.i8 + database_get(output_channel_vol1_ms_index + base + args->chn)->i32};
        tmp.i32 = tmp.i32 < 0 ? 0 : tmp.i32;
        tmp.i32 = tmp.i32 > 220 ? 220 : tmp.i32;
        database_write(output_channel_vol1_ms_index + base + args->chn, tmp);
    }
    else if (args->chn == MIC_GLOBAL_MUTE)
    {
        database_value tmp = {.i32 = args->vol.i8 + database_get(mic_global_sys_vol_index)->i32};
        if (tmp.i32 > 160)
            tmp.i32 = 160;
        else if (tmp.i32 < 40)
            tmp.i32 = 40;
        database_write(mic_global_sys_vol_index, tmp);
    }
    else if (args->chn == OUT_GLOBAL_MUTE)
    {
        database_value tmp = {.i32 = args->vol.i8 + database_get(global_sys_out_vol_index)->i32};
        if (tmp.i32 > 160)
            tmp.i32 = 160;
        else if (tmp.i32 < 40)
            tmp.i32 = 40;
        database_write(global_sys_out_vol_index, tmp);
    }
    else if (args->chn >= IN_MUTE_CHN17 && args->chn <= IN_MUTE_CHN24)
    {
        args->chn -= IN_MUTE_CHN17;
        database_value tmp = {.i32 = args->vol.i8 + database_get(input_channel_vol17_index + args->chn)->i32};
        tmp.i32 = tmp.i32 < 0 ? 0 : tmp.i32;
        tmp.i32 = tmp.i32 > 220 ? 220 : tmp.i32;
        database_write(input_channel_vol17_index + args->chn, tmp);
    }
    else if (args->chn == ALL_SPEAKER_MUTE)
    {
        database_value tmp = {.i32 = args->vol.i8 + database_get(global_skp_out_vol_index)->i32};
        if (tmp.i32 > 160)
            tmp.i32 = 160;
        else if (tmp.i32 < 40)
            tmp.i32 = 40;
        database_write(global_skp_out_vol_index, tmp);
    }
    else if (args->chn == TRAILER_VOL_STEP)
    {
        database_value tmp = {.i32 = args->vol.i8 + database_get(global_tune_mode_vol_index)->i32};
        if (tmp.i32 > 220)
            tmp.i32 = 220;
        else if (tmp.i32 < 0)
            tmp.i32 = 0;
        database_write(global_tune_mode_vol_index, tmp);
    }
    else if (args->chn == AEC_OUT_VOL_STEP)
    {
        database_value tmp = {.i32 = args->vol.i8 + database_get(global_aec_out_vol_index)->i32};
        if (tmp.i32 > 160)
            tmp.i32 = 160;
        else if (tmp.i32 < 40)
            tmp.i32 = 40;
        database_write(global_aec_out_vol_index, tmp);
    }
    else if (args->chn == AFC_OUT_VOL_STEP)
    {
        database_value tmp = {.i32 = args->vol.i8 + database_get(global_afc_out_vol_index)->i32};
        if (tmp.i32 > 160)
            tmp.i32 = 160;
        else if (tmp.i32 < 40)
            tmp.i32 = 40;
        database_write(global_afc_out_vol_index, tmp);
    }
    return NULL;
}

static void *get_chn_vol(uint8_t device, uint8_t *data)
{
    typedef struct
    {
        uint8_t chn;
    } __attribute__((packed)) get_chn_vol_def;
    get_chn_vol_def *args = (get_chn_vol_def *)data;
    const int base = device == 0x01 ? MIXER1_BASE_INDEX : MIXER2_BASE_INDEX;

#if defined(COMM_WITH_MIC_MATRIX_BY_A2B)
    a2b_comm_misc_t a2b_comm;
    a2b_comm.type = A2B_SLAVE_COMM_SET_FULL_BYPASS;
    a2b_comm.node = YM_PROTO_SLAVE_ID(device);
    a2b_comm.core = YM_PROTO_CORE_ID(device);
    a2b_comm.cmd  = 0xA1;
    a2b_comm.ackl = 5;
    a2b_comm.ackt = 2;
    if (YM_PROTO_SLAVE_ID(device))
    {
        MALLOC_YMLINK_MESSAGE();
        YMLINK_MESSAGE_HEAD(msg);

        bsp_ad2428_s_comm(&a2b_comm, data, 2, &msg->payload[msg->len], 1);
        msg->len += 1;

        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
#endif /* end COMM_WITH_MIC_MATRIX_BY_A2B */

    MALLOC_YMLINK_MESSAGE();
    YMLINK_MESSAGE_HEAD(msg);
    if (args->chn >= IN_CHN_MIN && args->chn <= IN_CHN_MAX)
    {
        msg->payload[msg->len++] = database_get(input_channel_vol1_index + args->chn)->u32;
    }
    else if (args->chn >= OUT_CHN_MIN && args->chn <= OUT_CHN_MAX)
    {
        args->chn -= OUT_CHN_MIN;
        msg->payload[msg->len++] = database_get(output_channel_vol1_ms_index + base + args->chn)->u32;
    }
    else if (args->chn == MIC_GLOBAL_VOL)
    {
        msg->payload[msg->len++] = database_get(mic_global_sys_vol_index)->i32;
    }
    else if (args->chn == OUT_GLOBAL_VOL)
    {
        msg->payload[msg->len++] = database_get(global_sys_out_vol_index)->i32;
    }
    else if (args->chn >= IN_CHN17 && args->chn <= IN_CHN24)
    {
        args->chn -= IN_CHN17;
        msg->payload[msg->len++] = database_get(input_channel_vol17_index + args->chn)->i32;
    }
    else if (args->chn == ALL_SPK_VOL)
    {
        msg->payload[msg->len++] = database_get(global_skp_out_vol_index)->i32;
    }
    else if (args->chn == TUNE_MODE_VOL)
    {
        msg->payload[msg->len++] = database_get(global_tune_mode_vol_index)->i32;
    }
    else if (args->chn == AEC_OUT_VOL)
    {
        msg->payload[msg->len++] = database_get(global_aec_out_vol_index)->i32;
    }
    else if (args->chn == AFC_OUT_VOL)
    {
        msg->payload[msg->len++] = database_get(global_afc_out_vol_index)->i32;
    }
    else if (args->chn == ANDROID_OUT_VOL)
    {
#if defined(ANDROID_WINDOWS_VOL_SYNC)
        msg->payload[msg->len++] = android_playback_vol_get();
#else
        msg->payload[msg->len++] = 0x00;
#endif /* end ANDROID_WINDOWS_VOL_SYNC */
    }
    else if (args->chn == UAC_CAPTURE_VOL)
    {
#if defined(ANDROID_WINDOWS_VOL_SYNC)
        msg->payload[msg->len++] = uac_capture_vol_get();
#else
        msg->payload[msg->len++] = 0x00;
#endif /* end ANDROID_WINDOWS_VOL_SYNC */
    }
    else if (args->chn >= IN_CHN_STEP_MIN && args->chn <= IN_CHN_STEP_MAX)
    {
        args->chn -= IN_CHN_STEP_MIN;
        msg->payload[msg->len++] = 0; // TODO: 单步音量返回？
    }
    else if (args->chn >= OUT_CHN_STEP_MIN && args->chn <= OUT_CHN_STEP_MAX)
    {
        args->chn -= OUT_CHN_STEP_MIN;
        msg->payload[msg->len++] = 0; // TODO: 单步音量返回？
    }
    else if (args->chn == MIC_GLOBAL_MUTE)
    {
        msg->payload[msg->len++] = 0; // TODO: 单步音量返回？
    }
    else if (args->chn == OUT_GLOBAL_MUTE)
    {
        msg->payload[msg->len++] = 0; // TODO: 单步音量返回？
    }
    else if (args->chn >= IN_MUTE_CHN17 && args->chn <= IN_MUTE_CHN24)
    {
        args->chn -= IN_MUTE_CHN17;
        msg->payload[msg->len++] = 0; // TODO: 单步音量返回？
    }
    else if (args->chn == ALL_SPEAKER_MUTE)
    {
        msg->payload[msg->len++] = 0; // TODO: 单步音量返回？
    }

    if (msg->len == 2)
    {
        ym_mem_pool_free(ym.message_pool, msg);
        return NULL;
    }
    else
    {
        YMLINK_MESSAGE_TAIL(msg);
        YMLINK_MESSAGE_APPEND(msg);
    }
}

static int ymlink_app_init(void)
{
    // register handle message
    ymlink_register(0xA0, set_chn_vol); // DONE,some todo on platform
    ymlink_register(0xA1, get_chn_vol); // DONE,some todo on platform

    ymlink_register(0xA2, set_chn_mute);     // DONE
    ymlink_register(0xA3, get_chn_mute);     // DONE
    ymlink_register(0xA4, set_matrix_vol);   // DONE
    ymlink_register(0xA5, get_matrix_vol);   // DONE
    ymlink_register(0xA6, set_matrix_mute);  // DONE
    ymlink_register(0xA7, get_matrix_mute);  // DONE
    ymlink_register(0xA8, set_mic_gain);     // DONE
    ymlink_register(0xA9, get_mic_gain);     // DONE
    ymlink_register(0xAA, set_outchn_type);  // DONE
    ymlink_register(0xAB, get_outchn_type);  // DONE
    ymlink_register(0xAC, set_aec_mode);     // DONE
    ymlink_register(0xAD, get_aec_mode);     // DONE
    ymlink_register(0xAE, set_inchn_ducker); // DONE
    ymlink_register(0xAF, get_inchn_ducker); // DONE

    ymlink_register(0xB0, set_test_signal);  // DONE
    ymlink_register(0xB1, get_test_signal);  // DONE
    ymlink_register(0xB2, restore_settings); // DELAY
    ymlink_register(0xB3, reset_self);       // DONE
    ymlink_register(0xB4, get_sys_state);    // DONE,some todo
    ymlink_register(0xB5, save_tune_mode);   // TODO
    ymlink_register(0xB6, set_tune_mode);    // TODO
    ymlink_register(0xB7, get_tune_mode);    // DONE
    ymlink_register(0xB8, set_module_en);    // DONE
    ymlink_register(0xB9, get_module_en);    // DONE
    ymlink_register(0xBA, set_sync_state);   // DELAY
    ymlink_register(0xBB, get_sync_state);   // DELAY
    ymlink_register(0xBC, set_io_delay);     // DONE
    ymlink_register(0xBD, get_io_delay);     // DONE
    ymlink_register(0xBE, set_out_invt);     // DONE
    ymlink_register(0xBF, get_out_invt);     // DONE

    ymlink_register(0xC0, get_version);             // DONE
    ymlink_register(0xC1, firmware_start);          // DONE
    ymlink_register(0xC2, firmware_bank);           // DONE
    ymlink_register(0xC4, set_module_threshold);    // DONE
    ymlink_register(0xC5, get_module_threshold);    // DONE
    ymlink_register(0xC6, set_module_compensation); // DONE
    ymlink_register(0xC7, get_module_compensation); // DONE
    ymlink_register(0xC8, set_module_attack_time);  // DONE
    ymlink_register(0xC9, get_module_attack_time);  // DONE
    ymlink_register(0xCA, set_module_release_time); // DONE
    ymlink_register(0xCB, get_module_release_time); // DONE
    ymlink_register(0xCC, set_module_others_param); // DONE
    ymlink_register(0xCD, get_module_others_param); // DONE
    ymlink_register(0xCE, set_geq_peq_band);        // DONE
    ymlink_register(0xCF, get_geq_peq_band);        // DONE

    ymlink_register(0xD0, set_geq_peq_freq); // DONE
    ymlink_register(0xD1, get_geq_peq_freq); // DONE
    ymlink_register(0xD2, set_geq_peq_gain); // DONE
    ymlink_register(0xD3, get_geq_peq_gain); // DONE
    ymlink_register(0xD4, set_peq_filter);   // DONE
    ymlink_register(0xD5, get_peq_filter);   // DONE
    ymlink_register(0xD6, set_peq_qval);     // DONE
    ymlink_register(0xD7, get_peq_qval);     // DONE
#ifdef BEAM_ENABLE
    ymlink_register(0xD8, set_beamforming); // DELAY
    ymlink_register(0xD9, get_beamforming); // DELAY
#endif
    ymlink_register(0xDA, set_led_func); // DELAY
    ymlink_register(0xDB, get_led_func); // DELAY

    ymlink_register(0xDC, slave_ota_ctrl); // DELAY
    ymlink_register(0xDD, slave_ota_prog); // DELAY

    ymlink_register(0xEC, set_mic_aec);  // DONE
    ymlink_register(0xED, get_mic_aec);  // DONE
    ymlink_register(0xEE, set_aec_ctrl); // DONE,some todo
    ymlink_register(0xEF, get_aec_ctrl); // DONE,some todo

    ymlink_register(0xF0, set_mic_ans);      // DONE
    ymlink_register(0xF1, get_mic_ans);      // DONE
    ymlink_register(0xF2, set_barcode);      // DONE
    ymlink_register(0xF3, get_barcode);      // DONE
    ymlink_register(0xF4, reset_netconfig);  // DONE
    ymlink_register(0xF5, get_netconfig);    // DONE
    ymlink_register(0xF6, set_48v_pwr);      // DONE
    ymlink_register(0xF7, get_48v_pwr);      // DONE
    ymlink_register(0xF8, set_mode_name);    // DONE
    ymlink_register(0xF9, get_mode_name);    // DONE
    ymlink_register(0xFA, set_chn_attr);     // DONE
    ymlink_register(0xFB, get_chn_attr);     // DONE
    ymlink_register(0xFC, factory_test);     // DONE
    ymlink_register(0xFD, get_factory_test); // DONE

    ymlink_register(0x90, get_a2b_slave_cnt);                // DELAY
    ymlink_register(0x91, set_a2b_slave_clk);                // DELAY
    ymlink_register(0x92, get_a2b_slave_beamformer_angle);   // DELAY
    ymlink_register(0x93, set_a2b_slave_ref_sig_level);      // DELAY
    ymlink_register(0x94, get_a2b_slave_source_voice_angle); // DELAY
    ymlink_register(0x95, get_a2b_slave_save_params);        // DELAY
    ymlink_register(0x96, get_a2b_node_attr);                // DELAY

    ymlink_special_status_register(0xC2, trans_bank_check_status);

    log_i("ymlink register successful.");

    return 0;
}
INIT_COMPONENT_EXPORT(ymlink_app_init);

/**
 * @description: ymlink 帧解析状态机
 * @param {uint8_t} c
 * @return {ymlink_message_p} NULL 表示为解析未完成
 * @author: Zhichao.An
 */
static ymlink_message_p ymlink_parse_frame(struct ymlink_stack *ym_stack)
{
    ymlink_message_p result = NULL;
    // printf("0x%x ", c);
    switch (ym_stack->parse_status)
    {
    case ym_status_wait_head1:
        memset(&ym_stack->msg, 0, sizeof(ymlink_message_t));
    __idle:
        if (ym_stack->c == 0x7B)
        {
            ym_stack->parse_status = ym_status_wait_head2;
            ym_stack->msg.header1 = ym_stack->c;
        }
        break;
    case ym_status_wait_head2:
        if (ym_stack->c == 0x7D)
        {
            ym_stack->parse_status = ym_status_wait_devid;
            ym_stack->msg.header2 = ym_stack->c;
        }
        else
        {
            ym_stack->parse_status = ym_status_wait_head1;
            goto __idle;
        }
        break;
    case ym_status_wait_devid:
        if (YM_PROTO_SLAVE_ID(ym_stack->c))
        {
            ym_stack->msg.dev_id = ym_stack->c;
            ym_stack->parse_status = ym_status_wait_cmd;
        }
        else
        {
            if ((ym_stack->c == 0x01) || (ym_stack->c == 0x02))
            {
                ym_stack->msg.dev_id = ym_stack->c;
                ym_stack->parse_status = ym_status_wait_cmd;
            }
            else
            {
                ym_stack->parse_status = ym_status_wait_head1;
                goto __idle;
            }
        }
        break;
    case ym_status_wait_cmd:
        ym_stack->parse_status = ym_status_wait_tail1;
        ym_stack->msg.command = ym_stack->c;
        ym_stack->rx_pos = 0;
        ym_stack->cur_check = find_special_status_check(ym_stack->c);
        break;
    case ym_status_wait_tail1:
        if (ym_stack->c == 0x7d)
        {
            if (ym_stack->cur_check)
            {
                if (ym_stack->cur_check->callback(&ym_stack->msg, ym_stack) == 0)
                {
                    ym_stack->msg.tail1 = ym_stack->c;
                    ym_stack->parse_status = ym_status_wait_tail2;
                }
                else
                {
                    goto __recv_back;
                }
            }
            else
            {
                ym_stack->msg.tail1 = ym_stack->c;
                ym_stack->parse_status = ym_status_wait_tail2;
            }
        }
        else
        {
        __recv_back:
            ym_stack->msg.payload[ym_stack->rx_pos++] = ym_stack->c;
            if (ym_stack->rx_pos >= YM_PROTO_PAYLOAD_MAX)
            {
                ym_stack->parse_status = ym_status_wait_head1;
            }
        }
        break;
    case ym_status_wait_tail2:
        if (ym_stack->c == 0x7b)
        {
            // recieve successful
            ym_stack->msg.tail2 = ym_stack->c;
            result = &ym_stack->msg;
        }
        else
        {
            /* for 0x7d in the data domain, special case here */
            ym_stack->msg.payload[ym_stack->rx_pos++] = 0x7d;
            ym_stack->msg.payload[ym_stack->rx_pos++] = ym_stack->c;
            if (ym_stack->rx_pos >= YM_PROTO_PAYLOAD_MAX)
            {
                ym_stack->parse_status = ym_status_wait_head1;
            }
            else
            {
                if (ym_stack->c == 0x7d)
                {
                    ym_stack->parse_status = ym_status_wait_tail2;
                }
                else
                {
                    ym_stack->parse_status = ym_status_wait_tail1;
                }
                break;
            }
        }
        ym_stack->parse_status = ym_status_wait_head1;
        break;
    default:
        ym_stack->parse_status = ym_status_wait_head1;
        break;
    }
    return result;
}

void *uart_protocol_thread_entry(void *arg)
{
    ymlink_message_p msg, tmp_msg;
    ymlink_callback_def_p callback;
    int len, pos, rcnt;
    int serial_fd = *(int *)arg;
    struct ymlink_stack ym_stack;
    memset(&ym_stack, 0, sizeof(struct ymlink_stack));
    char bypass_buf[512];

    for (;;)
    {
        memset(bypass_buf, 0, 512);
        rcnt = read(serial_fd, bypass_buf, 512);
#if defined(PROTOCOL_SERIAL_PORT_BYPASS)
        if (serial_port_bypass_flag_get() > 0)
        {
            if (serial_fd == serial_port_protocol_fd_get())
            {
                if (rcnt > 0)
                {
                    rcnt = write(serial_port_bypass_if_fd_get(), bypass_buf, rcnt);
                }
                continue;
            }
        }
#endif /* end PROTOCOL_SERIAL_PORT_BYPASS */

        for (int i = 0; i < rcnt; i++)
        {
            printf("0x%x ", bypass_buf[i]);
        }
        printf("\n");
        //if (read(serial_fd, &ym_stack.c, 1) == 1) // low efficiency
        {
            for (int i = 0; rcnt > i; i++)
            {
                ym_stack.c = bypass_buf[i];
                // ymlink 帧解析状态机
                msg = ymlink_parse_frame(&ym_stack);
                if (msg)
                {
                    callback = find_callback(msg->command);
                    if (callback)
                    {
                        handle_callback_list_p callback_elem;
                        DL_FOREACH(callback->callback_list, callback_elem)
                        {
                            tmp_msg = callback_elem->callback(msg->dev_id, msg->payload);
                            if (tmp_msg)
                            {
                                pos = 0;
                                do
                                {
                                    len = write(serial_fd, &tmp_msg->payload[pos], tmp_msg->len);
                                    if (len < 0)
                                    {
                                        log_e("write err:%d", len);
                                    }
                                    else
                                    {
                                        tmp_msg->len -= len;
                                        pos += len;
                                    }
                                } while (tmp_msg->len);
                                ym_mem_pool_free(ym.message_pool, tmp_msg);
                            }
                        }
                    }
                    else
                        log_e("command :%d don't have callback", msg->command);
                }
            }
        }
    }
    return NULL;
}

void *eth_protocol_thread_handle(void *arg)
{
    ymlink_message_p msg, tmp_msg;
    ymlink_callback_def_p callback;
    int len, pos;
    struct ymlink_stack ym_stack;
    memset(&ym_stack, 0, sizeof(struct ymlink_stack));

    int server_socket, client_socket;
    struct sockaddr_in server_addr, client_addr;
    socklen_t client_addr_len = sizeof(client_addr);
    ssize_t bytes_received;

    int optval;
    static char eth_buffer[2048];
    int i;

    // create socket
    if ((server_socket = socket(AF_INET, SOCK_STREAM, 0)) == -1)
    {
        log_e("Socket creation failed");
        exit(EXIT_FAILURE);
    }

    // bind
    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = INADDR_ANY;
    server_addr.sin_port = htons(ETH_PROTOCOL_PORT);
    if (bind(server_socket, (struct sockaddr *)&server_addr, sizeof(server_addr)) == -1)
    {
        log_e("Socket bind failed");
        exit(EXIT_FAILURE);
    }

    // listen
    if (listen(server_socket, 1) == -1)
    {
        log_e("Socket listen failed");
        exit(EXIT_FAILURE);
    }

    for (;;)
    {
        log_i("Server listening on port %d...", ETH_PROTOCOL_PORT);

        // accept
        if ((client_socket = accept(server_socket, (struct sockaddr *)&client_addr, &client_addr_len)) == -1)
        {
            log_e("Accept failed");
            continue;
        }

        optval = 1;
        if (setsockopt(client_socket, SOL_SOCKET, SO_KEEPALIVE, &optval, sizeof(optval)) < 0)
        {
            perror("setsockopt SO_KEEPALIVE");
            close(client_socket);
            exit(EXIT_FAILURE);
        }

        optval = 1;
        if (setsockopt(client_socket, IPPROTO_TCP, TCP_KEEPIDLE, &optval, sizeof(optval)) < 0)
        {
            perror("setsockopt TCP_KEEPIDLE");
            close(client_socket);
            exit(EXIT_FAILURE);
        }

        optval = 1;
        if (setsockopt(client_socket, IPPROTO_TCP, TCP_KEEPINTVL, &optval, sizeof(optval)) < 0)
        {
            perror("setsockopt TCP_KEEPINTVL");
            close(client_socket);
            exit(EXIT_FAILURE);
        }

        optval = 2;
        if (setsockopt(client_socket, IPPROTO_TCP, TCP_KEEPCNT, &optval, sizeof(optval)) < 0)
        {
            perror("setsockopt TCP_KEEPCNT");
            close(client_socket);
            exit(EXIT_FAILURE);
        }

        log_i("connect client ip:%s:%d", inet_ntoa(client_addr.sin_addr), ntohs(client_addr.sin_port));

        // com
        for (;;)
        {
            bytes_received = recv(client_socket, eth_buffer, sizeof(eth_buffer), 0);
            if (bytes_received <= 0)
            {
                log_e("Receive failed: %s", strerror(errno));
                goto __exit;
            }
            for (i = 0; i < bytes_received; i++)
            {
                // ymlink 帧解析状态机
                ym_stack.c = eth_buffer[i];
                msg = ymlink_parse_frame(&ym_stack);
                if (msg)
                {
                    callback = find_callback(msg->command);
                    if (callback)
                    {
                        handle_callback_list_p callback_elem;
                        DL_FOREACH(callback->callback_list, callback_elem)
                        {
                            tmp_msg = callback_elem->callback(msg->dev_id, msg->payload);
                            if (tmp_msg)
                            {
                                pos = 0;
                                do
                                {
                                    // 回复客户端
                                    len = send(client_socket, &tmp_msg->payload[pos], tmp_msg->len, 0);
                                    if (len < 0)
                                    {
                                        log_e("write err:%d", len);
                                        ym_mem_pool_free(ym.message_pool, tmp_msg);
                                        goto __exit;
                                    }
                                    else
                                    {
                                        tmp_msg->len -= len;
                                        pos += len;
                                    }
                                } while (tmp_msg->len);
                                ym_mem_pool_free(ym.message_pool, tmp_msg);
                            }
                        }
                    }
                    else
                        log_e("command :%d don't have callback", msg->command);
                }
            }
        }
    __exit:
        // close
        close(client_socket);
    }
    return NULL;
}

// static int ym_stack_init(void)
// {
// #ifndef PCBA_TEST
//     static int serial_fd;
//     serial_fd = open("/dev/ttyS4", O_RDWR | O_NOCTTY);
//     if (serial_fd < 0)
//     {
//         log_e("serial open err.");
//         return 1;
//     }
//     ym_protocol_serial_set_param(serial_fd, B115200);
// #if defined(PROTOCOL_SERIAL_PORT_BYPASS)
//     serial_port_protocol_232_fd_set(serial_fd);
// #endif /* end PROTOCOL_SERIAL_PORT_BYPASS */
//     ym_thread_create("uart_protocol",
//                      default_thread_cpuid,
//                      uart_protocol_thread_prio,
//                      uart_protocol_thread_entry,
//                      (void *)&serial_fd);
// #endif // !PCBA_TEST
//     return 0;
// }
// INIT_APP_EXPORT(ym_stack_init);
