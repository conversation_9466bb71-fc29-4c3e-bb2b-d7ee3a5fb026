/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-26 13:21:44
 * @LastEditTime: 2024-12-02 18:39:21
 * @LastEditors: Anzhi<PERSON>o
 * @Description: mixer参数初始化，参数回调
 * @FilePath: /audio_mixer_rk3308/component/ym_mixer_param.c
 * Copyright (c) 2006-2024, Yinkman Development Team
 */

#include "config.h"
#include <stdio.h>
#include <semaphore.h>
#include "ym_utilities.h"
#include "ym_startup.h"
#include <unistd.h>
#include "utlist.h"
#include <pthread.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <termios.h>
#include "ym_database.h"
#include <arpa/inet.h>
#include "ym_shell.h"
#include "ym_protocol.h"
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>
#include <dirent.h>
#include <ctype.h>
#include "ym_log.h"
#ifdef BEAM_ENABLE
#include "ym_beam.h"
#endif /* BEAM_ENABLE */
// #include "uac_name.h"
#include "ym_mixer_beam.h"

extern void *ym_mixer_master_handle;
extern void *ym_mixer_slave_handle;

config_ctrl_def gconfig_ctrl;
nonvolatile_global_cfg_t nonvolatile_global_cfg;

static void input_channel_vol_callback(int index, database_value old_value, database_value new_value)
{
    if (index - input_channel_vol1_index >= MIXER_INPUT_CHN_MAX)
    {
        database_write_no_callback(index, old_value);
        return;
    }

    mixer_lib_lock(1);
    int err = matrix_set_channel_mode[LATENCY_5MS](ym_mixer_master_handle,
                                      YM_MATRIX_SET_INPUT_CHANNEL_VOLUME,
                                      index - input_channel_vol1_index,
                                      new_value.i32);
    mixer_lib_unlock(1);

    mixer_lib_lock(2);
    err = matrix_set_channel_mode[LATENCY_10MS](ym_mixer_slave_handle,
                                  YM_MATRIX_SET_INPUT_CHANNEL_VOLUME,
                                  index - input_channel_vol1_index,
                                  new_value.i32);
    mixer_lib_unlock(2);

    if (err)
    {
        log_e("set input(%d) channel vol(%d) err:%d", index - input_channel_vol1_index, new_value.i32, err);
        database_write_no_callback(index, old_value);
    }
}
static void output_channel_vol_callback(int index, database_value old_value, database_value new_value)
{
    int base = index >= MIXER2_BASE_INDEX ? MIXER2_BASE_INDEX : MIXER1_BASE_INDEX;
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    latency_e latency = (index >= MIXER2_BASE_INDEX) ? LATENCY_10MS : LATENCY_5MS;
    index -= base;
    if (index - output_channel_vol1_ms_index >= MIXER_OUTPUT_CHN_MAX)
    {
        database_write_no_callback(index + base, old_value);
        return;
    }
    mixer_lib_lock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    int err = matrix_set_channel_mode[latency](ym_mixer_handle,
                                      YM_MATRIX_SET_OUTPUT_CHANNEL_VOLUME,
                                      index - output_channel_vol1_ms_index,
                                      new_value.i32);
    mixer_lib_unlock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    if (err)
    {
        log_e("set output(%d) channel vol(%d) err:%d", index - output_channel_vol1_ms_index, new_value.i32, err);
        database_write_no_callback(index + base, old_value);
    }
}

static void input_channel_aec_mode_callback(int index, database_value old_value, database_value new_value)
{
    int base = index >= MIXER2_BASE_INDEX ? MIXER2_BASE_INDEX : MIXER1_BASE_INDEX;
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    latency_e latency = (index >= MIXER2_BASE_INDEX) ? LATENCY_10MS : LATENCY_5MS;
    index -= base;
    if (index - input_channel1_aec_mode_ms_index >= MIXER_INPUT_CHN_MAX)
    {
        database_write_no_callback(index + base, old_value);
        return;
    }
    mixer_lib_lock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    int err = matrix_set_channel_mode[latency](ym_mixer_handle,
                                      YM_MATRIX_SET_INPUT_CHANNEL_AEC_MODE,
                                      index - input_channel1_aec_mode_ms_index,
                                      new_value.i32);
    mixer_lib_unlock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    if (err)
    {
        log_e("set input(%d) channel aec mode(%d) err:%d", index - input_channel1_aec_mode_ms_index, new_value.i32, err);
        database_write_no_callback(index + base, old_value);
    }
}

static void input_channel_mute_callback(int index, database_value old_value, database_value new_value)
{
    if (index - input_channel1_mute_index >= MIXER_INPUT_CHN_MAX)
    {
        database_write_no_callback(index, old_value);
        return;
    }
    int err = 0;
    mixer_lib_lock(1);
    err |= matrix_set_channel_mode[LATENCY_5MS](ym_mixer_master_handle,
                                   YM_MATRIX_SET_INPUT_CHANNEL_MUTE,
                                   index - input_channel1_mute_index,
                                   new_value.i32);
    mixer_lib_unlock(1);
    mixer_lib_lock(2);
    err |= matrix_set_channel_mode[LATENCY_10MS](ym_mixer_slave_handle,
                                   YM_MATRIX_SET_INPUT_CHANNEL_MUTE,
                                   index - input_channel1_mute_index,
                                   new_value.i32);
    mixer_lib_unlock(2);
    if (err)
    {
        log_e("set input(%d) channel mute(%d) err:%d", index - input_channel1_mute_index, new_value.i32, err);
        database_write_no_callback(index, old_value);
    }
}

static void output_channel_mute_callback(int index, database_value old_value, database_value new_value)
{
    int base = index >= MIXER2_BASE_INDEX ? MIXER2_BASE_INDEX : MIXER1_BASE_INDEX;
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    latency_e latency = (index >= MIXER2_BASE_INDEX) ? LATENCY_10MS : LATENCY_5MS;
    index -= base;
    if (index - output_channel1_mute_ms_index >= MIXER_OUTPUT_CHN_MAX)
    {
        database_write_no_callback(index + base, old_value);
        return;
    }
    mixer_lib_lock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    int err = matrix_set_channel_mode[latency](ym_mixer_handle,
                                      YM_MATRIX_SET_OUTPUT_CHANNEL_MUTE,
                                      index - output_channel1_mute_ms_index,
                                      new_value.i32);
    mixer_lib_unlock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    if (err)
    {
        log_e("set output(%d) channel mute(%d) err:%d", index - output_channel1_mute_ms_index, new_value.i32, err);
        database_write_no_callback(index + base, old_value);
    }
}

static void input_channel_atm_callback(int index, database_value old_value, database_value new_value)
{
    int base = index >= MIXER2_BASE_INDEX ? MIXER2_BASE_INDEX : MIXER1_BASE_INDEX;
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    latency_e latency = (index >= MIXER2_BASE_INDEX) ? LATENCY_10MS : LATENCY_5MS;
    index -= base;
    if (index - input_channel1_atm_mode_ms_index >= MIXER_INPUT_CHN_MAX)
    {
        database_write_no_callback(index + base, old_value);
        return;
    }
    mixer_lib_lock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    int err = matrix_set_channel_mode[latency](ym_mixer_handle,
                                      YM_MATRIX_SET_INPUT_MIC_AUTOMIX_PRIORITY,
                                      index - input_channel1_atm_mode_ms_index,
                                      new_value.i32);
    mixer_lib_unlock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    if (err)
    {
        log_e("set input(%d) channel atm(%d) err:%d", index - input_channel1_atm_mode_ms_index, new_value.i32, err);
        database_write_no_callback(index + base, old_value);
    }
}

static void input_channel_atm_decay_callback(int index, database_value old_value, database_value new_value)
{
    int base = index >= MIXER2_BASE_INDEX ? MIXER2_BASE_INDEX : MIXER1_BASE_INDEX;
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    latency_e latency = (index >= MIXER2_BASE_INDEX) ? LATENCY_10MS : LATENCY_5MS;
    index -= base;
    if (index - input_channel1_atm_decay_ms_index >= MIXER_INPUT_CHN_MAX)
    {
        database_write_no_callback(index + base, old_value);
        return;
    }
    mixer_lib_lock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    int err = matrix_set_channel_mode[latency](ym_mixer_handle,
                                      YM_MATRIX_SET_INPUT_MIC_AUTOMIX_DECAY_RATIO,
                                      index - input_channel1_atm_decay_ms_index,
                                      new_value.i32);
    mixer_lib_unlock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    if (err)
    {
        log_e("set input(%d) channel decay(%d) err:%d", index - input_channel1_atm_decay_ms_index, new_value.i32, err);
        database_write_no_callback(index + base, old_value);
    }
}

static void input_channel_ducker_callback(int index, database_value old_value, database_value new_value)
{
    int err = 0;
    if (index - input_channel1_ducker_index >= MIXER_INPUT_CHN_MAX)
    {
        database_write_no_callback(index, old_value);
        return;
    }

    mixer_lib_lock(1);
    err |= matrix_set_channel_mode[LATENCY_5MS](ym_mixer_master_handle,
                                   YM_MATRIX_SET_INPUT_CHANNEL_DUCKER_MODE,
                                   index - input_channel1_ducker_index,
                                   new_value.i32);
    mixer_lib_unlock(1);

    mixer_lib_lock(2);
    err |= matrix_set_channel_mode[LATENCY_10MS](ym_mixer_slave_handle,
                                   YM_MATRIX_SET_INPUT_CHANNEL_DUCKER_MODE,
                                   index - input_channel1_ducker_index,
                                   new_value.i32);
    mixer_lib_unlock(2);

    if (err)
    {
        log_e("set input(%d) channel ducker(%d) err:%d", index - input_channel1_ducker_index, new_value.i32, err);
        database_write_no_callback(index, old_value);
    }
}

static void input_channel_delay_callback(int index, database_value old_value, database_value new_value)
{
    int err = 0;
    int base = index >= MIXER2_BASE_INDEX ? MIXER2_BASE_INDEX : MIXER1_BASE_INDEX;
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    latency_e latency = (index >= MIXER2_BASE_INDEX) ? LATENCY_10MS : LATENCY_5MS;
    index -= base;
    if (index - input_channel1_delay_ms_index >= MIXER_INPUT_CHN_MAX)
    {
        database_write_no_callback(index + base, old_value);
        return;
    }
    mixer_lib_lock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    err = matrix_set_channel_mode[latency](ym_mixer_handle,
                                  YM_MATRIX_SET_INPUT_CHANNEL_DELAY_MS,
                                  index - input_channel1_delay_ms_index,
                                  new_value.i32);
    mixer_lib_unlock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    if (err)
    {
        log_e("set input(%d) channel delay(%d) err:%d", index - input_channel1_delay_ms_index, new_value.i32, err);
        database_write_no_callback(index + base, old_value);
    }
}

static void output_channel_type_callback(int index, database_value old_value, database_value new_value)
{
    if (index - output_channel1_type_index >= MIXER_OUTPUT_CHN_MAX)
    {
        database_write_no_callback(index, old_value);
        return;
    }
    int err = 0;

    mixer_lib_lock(1);
    err |= matrix_set_channel_mode[LATENCY_5MS](ym_mixer_master_handle,
                                   YM_MATRIX_SET_OUTPUT_CHANNEL_TYPE,
                                   index - output_channel1_type_index,
                                   new_value.i32);
    if (err)
    {
        log_e("set output(%d) channel type(%d) err:%d", index - output_channel1_type_index, new_value.i32, err);
    }
    mixer_lib_unlock(1);

    mixer_lib_lock(2);
    err |= matrix_set_channel_mode[LATENCY_10MS](ym_mixer_slave_handle,
                                   YM_MATRIX_SET_OUTPUT_CHANNEL_TYPE,
                                   index - output_channel1_type_index,
                                   new_value.i32);
    if (err)
    {
        log_e("set output(%d) channel type(%d) err:%d", index - output_channel1_type_index, new_value.i32, err);
    }
    mixer_lib_unlock(2);

    if (err)
    {
        database_write_no_callback(index, old_value);
    }
}

static void output_channel_phase_invert_callback(int index, database_value old_value, database_value new_value)
{
    if (index - output_channel1_phase_invert_index >= MIXER_OUTPUT_CHN_MAX)
    {
        database_write_no_callback(index, old_value);
        return;
    }
    int err = 0;
    mixer_lib_lock(1);
    err |= matrix_set_channel_mode[LATENCY_5MS](ym_mixer_master_handle,
                                   YM_MATRIX_SET_OUTPUT_CHANNEL_PHASE_INVERT,
                                   index - output_channel1_phase_invert_index,
                                   new_value.i32);

    mixer_lib_unlock(1);
    if (err)
    {
        log_e("set output(%d) channel phase invert(%d) err:%d", index - output_channel1_phase_invert_index, new_value.i32, err);
    }
    mixer_lib_lock(2);
    err |= matrix_set_channel_mode[LATENCY_10MS](ym_mixer_slave_handle,
                                   YM_MATRIX_SET_OUTPUT_CHANNEL_PHASE_INVERT,
                                   index - output_channel1_phase_invert_index,
                                   new_value.i32);

    mixer_lib_unlock(2);
    if (err)
    {
        log_e("set output(%d) channel phase invert(%d) err:%d", index - output_channel1_phase_invert_index, new_value.i32, err);
        database_write_no_callback(index, old_value);
    }
}

static void output_channel_delay_callback(int index, database_value old_value, database_value new_value)
{
    int err = 0;
    int base = index >= MIXER2_BASE_INDEX ? MIXER2_BASE_INDEX : MIXER1_BASE_INDEX;
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    latency_e latency = (index >= MIXER2_BASE_INDEX) ? LATENCY_10MS : LATENCY_5MS;
    index -= base;
    if (index - output_channel1_delay_ms_index >= MIXER_OUTPUT_CHN_MAX)
    {
        database_write_no_callback(index + base, old_value);
        return;
    }
    mixer_lib_lock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    err = matrix_set_channel_mode[latency](ym_mixer_handle,
                                  YM_MATRIX_SET_OUTPUT_CHANNEL_DELAY_MS,
                                  index - output_channel1_delay_ms_index,
                                  new_value.i32);
    mixer_lib_unlock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    if (err)
    {
        log_e("set output(%d) channel delay(%d) err:%d", index - output_channel1_delay_ms_index, new_value.i32, err);
        database_write_no_callback(index + base, old_value);
    }
}

static void matrix_mute_callback(int index, database_value old_value, database_value new_value)
{
#define __matrix_mute_callback(x)                                               \
    do                                                                          \
    {                                                                           \
                                                                                \
        mixer_lib_lock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);       \
        err = matrix_set_matrix_map_params[latency](ym_mixer_handle,                     \
                                           YM_MATRIX_SET_MATRIX_MAP_MUTE,       \
                                           (x),                                 \
                                           index - ochn1_ichn_mute_ms_index,    \
                                           (new_value.u32 & (1 << i)) ? 1 : 0); \
        mixer_lib_unlock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);     \
        if (err)                                                                \
        {                                                                       \
            new_value.u32 &= ~(1 << i);                                         \
            if (old_value.u32 & (1 << i))                                       \
                new_value.u32 |= (1 << i);                                      \
            log_e("matrix_set_matrix_map_params err:%d", err);                  \
            database_write_no_callback(index + base, new_value);                \
        }                                                                       \
    } while (0)

    int err;
    int base = index >= MIXER2_BASE_INDEX ? MIXER2_BASE_INDEX : MIXER1_BASE_INDEX;
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    latency_e latency = (index >= MIXER2_BASE_INDEX) ? LATENCY_10MS : LATENCY_5MS;
    index -= base;
    if (index - ochn1_ichn_mute_ms_index >= MIXER_OUTPUT_CHN_MAX)
    {
        database_write_no_callback(index + base, old_value);
        return;
    }
    // normal channel
    for (size_t i = 0; i < MIXER_INPUT_CHN_MAX; i++)
    {
        __matrix_mute_callback(i);
    }
    for (size_t i = YM_MATRIX_INPUT_AEC_OUTPUT_CHANNEL; i <= YM_MATRIX_INPUT_AFC_OUTPUT_CHANNEL; i++)
    {
        __matrix_mute_callback(i);
    }
}

static void matrix_vol_callback(int index, database_value old_value, database_value new_value)
{
    int base = index >= MIXER2_BASE_INDEX ? MIXER2_BASE_INDEX : MIXER1_BASE_INDEX;
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    latency_e latency = (index >= MIXER2_BASE_INDEX) ? LATENCY_10MS : LATENCY_5MS;
    index -= base;
    int ochn = (index - ochn1_ichn1_vol_ms_index) / (ochn1_ichn1_vol_unit_count);
    int ichn = (index - ochn1_ichn1_vol_ms_index) % (ochn1_ichn1_vol_unit_count);
    int err;
    if ((ichn == (YM_MATRIX_INPUT_AEC_OUTPUT_CHANNEL) || ichn == (YM_MATRIX_INPUT_AFC_OUTPUT_CHANNEL)) && (ochn < MIXER_OUTPUT_CHN_MAX))
        goto __exit;

    if ((ichn >= MIXER_INPUT_CHN_MAX) ||
        (ochn >= MIXER_OUTPUT_CHN_MAX))
    {
        database_write_no_callback(index + base, old_value);
        return;
    }
__exit:
    mixer_lib_lock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    err = matrix_set_matrix_map_params[latency](ym_mixer_handle,
                                       YM_MATRIX_SET_MATRIX_MAP_VOL_LEVEL,
                                       ichn,
                                       ochn,
                                       new_value.i32);
    mixer_lib_unlock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    if (err)
    {
        database_write_no_callback(index + base, old_value);
        log_e("matrix %d-%d vol err:%d", ichn, ochn, err);
    }
}

static void mixer_set_sys_module_params_generic(int index, database_value old_value, database_value new_value, YM_MIXER_CONTROL_SYS_MODULE_PARAMS_ID id)
{
    int err = 0;
    mixer_lib_lock(1);
    err |= mixer_set_sys_module_params[LATENCY_5MS](ym_mixer_master_handle, id, new_value.i32);
    mixer_lib_unlock(1);
    mixer_lib_lock(2);
    err |= mixer_set_sys_module_params[LATENCY_10MS](ym_mixer_slave_handle, id, new_value.i32);
    mixer_lib_unlock(2);
    if (err)
    {
        log_e("mixer_set_sys_module_params err:%d", err);
        database_write_no_callback(index, old_value);
    }
}

static void mic_global_vol_callback(int index, database_value old_value, database_value new_value)
{
    mixer_set_sys_module_params_generic(index, old_value, new_value, YM_MATRIX_SET_MIC_INPUT_VOLUME);
}

static void global_sys_out_vol_callback(int index, database_value old_value, database_value new_value)
{
    mixer_set_sys_module_params_generic(index, old_value, new_value, YM_MATRIX_SET_SYSTEM_OUT_VOLUME);
}

static void global_aec_out_vol_callback(int index, database_value old_value, database_value new_value)
{
    mixer_set_sys_module_params_generic(index, old_value, new_value, YM_MATRIX_SET_AEC_OUT_VOLUME);
}

static void global_afc_out_vol_callback(int index, database_value old_value, database_value new_value)
{
    mixer_set_sys_module_params_generic(index, old_value, new_value, YM_MATRIX_SET_AFC_OUT_VOLUME);
}

static void global_aec_out_mute_callback(int index, database_value old_value, database_value new_value)
{
    mixer_set_sys_module_params_generic(index, old_value, new_value, YM_MATRIX_SET_AEC_OUT_MUTE);
}

static void global_afc_out_mute_callback(int index, database_value old_value, database_value new_value)
{
    mixer_set_sys_module_params_generic(index, old_value, new_value, YM_MATRIX_SET_AFC_OUT_MUTE);
}

static void global_mic_mute_callback(int index, database_value old_value, database_value new_value)
{
    mixer_set_sys_module_params_generic(index, old_value, new_value, YM_MATRIX_SET_MIC_INPUT_MUTE);
}

static void global_out_mute_callback(int index, database_value old_value, database_value new_value)
{
    mixer_set_sys_module_params_generic(index, old_value, new_value, YM_MATRIX_SET_SYSTEM_OUT_MUTE);
}

static void global_mic_delay_callback(int index, database_value old_value, database_value new_value)
{
    mixer_set_sys_module_params_generic(index, old_value, new_value, YM_MIXER_SET_MIC_DELAY_LEVELS);
}

static void global_aux_delay_callback(int index, database_value old_value, database_value new_value)
{
    mixer_set_sys_module_params_generic(index, old_value, new_value, YM_MIXER_SET_MUSIC_DELAY_LEVELS);
}

static void global_bgm_delay_callback(int index, database_value old_value, database_value new_value)
{
    mixer_set_sys_module_params_generic(index, old_value, new_value, YM_MIXER_SET_AUX_BGM_DELAY_LEVELS);
}

static void mic_ans_mode_callback(int index, database_value old_value, database_value new_value)
{
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    int err = 0;
    if (ym_mixer_handle == ym_mixer_slave_handle)
    {
        mixer_lib_lock(2);
        err = mixer_set_sys_module_params[LATENCY_10MS](ym_mixer_handle,
                                          YM_MIXER_SET_MIC_NS_MODE,
                                          (YM_MIXER_NS_MODE)new_value.i32);
        mixer_lib_unlock(2);
    }
    else
    {
        // // 主核，将new_value 1-6 映射到 12-17
        // if (new_value.i32 > 0 && new_value.i32 <= 6)
        // {
        //     new_value.i32 += 12;
        //     database_write_no_callback(index, new_value);
        // }
        mixer_lib_lock(1);
        err = mixer_set_sys_module_params[LATENCY_5MS](ym_mixer_handle,
                                          YM_MIXER_SET_MIC_NS_MODE,
                                          (YM_MIXER_NS_MODE)new_value.i32);
        mixer_lib_unlock(1);
    }

    if (err)
    {
        log_e("mixer_set_sys_module_params err:%d", err);
        database_write_no_callback(index, old_value);
    }
}

static void dereverb_level_callback(int index, database_value old_value, database_value new_value)
{
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    latency_e latency = (index >= MIXER2_BASE_INDEX) ? LATENCY_10MS : LATENCY_5MS;
    mixer_lib_lock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    int err = mixer_set_sys_module_params[latency](ym_mixer_handle,
                                          YM_MIXER_SET_DEREVERB_MODE,
                                          (YM_MATRIX_DEREVERB_ID)new_value.i32);
    mixer_lib_unlock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    if (err)
    {
        log_e("mixer_set_sys_module_params err:%d", err);
        database_write_no_callback(index, old_value);
    }
}

static void test_sig_callback(int index, database_value old_value, database_value new_value)
{
    int base = index >= MIXER2_BASE_INDEX ? MIXER2_BASE_INDEX : MIXER1_BASE_INDEX;
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    latency_e latency = (index >= MIXER2_BASE_INDEX) ? LATENCY_10MS : LATENCY_5MS;
    index -= base;
    int err = 0;
    mixer_lib_lock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    switch (index)
    {
    case test_sig_type_ms_index:
        err = mixer_set_sys_module_params[latency](ym_mixer_handle, YM_MIXER_SET_TEST_SINGAL_MODE, new_value.i32);
        break;
    case test_sig_freq_ms_index:
        err = mixer_set_sys_module_params[latency](ym_mixer_handle, YM_MIXER_SET_TEST_SINGAL_FREQ, new_value.i32);
        break;
    case test_sig_amp_ms_index:
        err = mixer_set_sys_module_params[latency](ym_mixer_handle, YM_MIXER_SET_TEST_SINGAL_LEVELDB, new_value.i32);
        break;
    default:
        break;
    }
    mixer_lib_unlock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    if (err)
    {
        log_e("set test sig:%d err:%d", index, err);
        database_write_no_callback(index + base, old_value);
    }
}

static void gain_control_callback(int index, database_value old_value, database_value new_value)
{
    int err = 0;
    int moduleID = (index - mic_noise_gate_gain_control_on_off_index) / YM_MATRIX_MAX_GAIN_CONTROL_PARAMS;
    int paramID = (index - mic_noise_gate_gain_control_on_off_index) % YM_MATRIX_MAX_GAIN_CONTROL_PARAMS;
    if (paramID > YM_MATRIX_GAIN_CONTROL_COMPR_RATIO)
        paramID = YM_MATRIX_GAIN_CONTROL_COMPR_RATIO;
    mixer_lib_lock(1);
    err |= mixer_set_gain_control_params[LATENCY_5MS](ym_mixer_master_handle,
                                         moduleID,
                                         paramID,
                                         new_value.i32);
    mixer_lib_unlock(1);

    mixer_lib_lock(2);
    err |= mixer_set_gain_control_params[LATENCY_10MS](ym_mixer_slave_handle,
                                         moduleID,
                                         paramID,
                                         new_value.i32);
    mixer_lib_unlock(2);

    if (err)
    {
        log_e("%d-%d value:%d\terr:%d", index, paramID, new_value.i32, err);
        database_write_no_callback(index, old_value);
    }
}

static void aec_atm_gain_control_callback(int index, database_value old_value, database_value new_value)
{
    int moduleID = ((index - mic_aec1_auto_mixer_gain_control_on_off_index) / YM_MATRIX_MAX_GAIN_CONTROL_PARAMS) + YM_MATRIX_MIC_AEC1_AUTO_MIXER;
    int paramID = (index - mic_aec1_auto_mixer_gain_control_on_off_index) % YM_MATRIX_MAX_GAIN_CONTROL_PARAMS;
    if (paramID > YM_MATRIX_GAIN_CONTROL_COMPR_RATIO)
        paramID = YM_MATRIX_GAIN_CONTROL_COMPR_RATIO;
    mixer_lib_lock(1);
    int err = mixer_set_gain_control_params[LATENCY_5MS](ym_mixer_master_handle,
                                            moduleID,
                                            paramID,
                                            new_value.i32);
    mixer_lib_unlock(1);
    if (err)
    {
        log_e("%d-%d value:%d\terr:%d", index, paramID, new_value.i32, err);
        database_write_no_callback(index, old_value);
    }

    mixer_lib_lock(2);
    err = mixer_set_gain_control_params[LATENCY_10MS](ym_mixer_slave_handle,
                                        moduleID,
                                        paramID,
                                        new_value.i32);
    mixer_lib_unlock(2);
    if (err)
    {
        log_e("%d-%d value:%d\terr:%d", index, paramID, new_value.i32, err);
        database_write_no_callback(index, old_value);
    }
}

static void howling_enable_callback(int index, database_value old_value, database_value new_value)
{
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    int base = index >= MIXER2_BASE_INDEX ? MIXER2_BASE_INDEX : MIXER1_BASE_INDEX;
    latency_e latency = (index >= MIXER2_BASE_INDEX)? LATENCY_10MS : LATENCY_5MS;
    short val;
    /**
     * howling_enable == YM_MIXER_HOWLING_DETECTION_NEARFIELD_MIC 的前置处理逻辑。
     */
    if (new_value.i32 == YM_MIXER_HOWLING_DETECTION_NEARFIELD_MIC)
    {
        static unsigned short howling_freq[YM_PEQ_MAX_BANDS_NUM];
        static short howling_gain[YM_PEQ_MAX_BANDS_NUM];
        static short howling_Q[YM_PEQ_MAX_BANDS_NUM];
        static int num_howling_freq = 0, i;
        static database_value tmp;

        mixer_read_last_howling_info[latency](ym_mixer_handle, (unsigned short *)howling_freq, (short *)howling_gain, (short *)howling_Q);

        /* set the PEQ params */
        for (i = 0; i < YM_PEQ_MAX_BANDS_NUM; i++)
        {
            if (howling_gain[i] < 0)
            {
                log_i("Set PEQ [%d] freq %d, gain %d *0.1dB, Qvalue %d * 0.001.",
                      i, howling_freq[i], howling_gain[i], howling_Q[i]);

                tmp.i32 = howling_freq[i];
                database_write(peq_anf_2_freq1_ms_index + i + base, tmp);
                tmp.i32 = howling_gain[i];
                database_write(peq_anf_2_gain1_ms_index + i + base, tmp);
                tmp.i32 = YM_PEQ_PEAKEQ_FILTER;
                database_write(peq_anf_2_filter1_ms_index + i + base, tmp);
                tmp.i32 = howling_Q[i];
                database_write(peq_anf_2_qval1_ms_index + i + base, tmp);
                num_howling_freq++;
            }
            else
            {
                tmp.i32 = 0;
                database_write(peq_anf_2_gain1_ms_index + i + base, tmp);
            }
        }
        tmp.i32 = YM_PEQ_MAX_BANDS_NUM;
        database_write(peq_anf_2_cnt_ms_index + i + base, tmp);

        tmp.i32 = num_howling_freq > 0;
        database_write(peq_anf_2_onoff_ms_index + i + base, tmp);
    }
    val = new_value.i32;
    mixer_lib_lock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    int err = mixer_set_echo_reverb_params[latency](ym_mixer_handle,
                                           YM_MIXER_ENABLE_HOWLING_DETECTION,
                                           &val);
    mixer_lib_unlock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    if (err)
    {
        log_e("mixer_set_echo_reverb_params err:%d", err);
        database_write_no_callback(index, old_value);
    }
}

static void mixer_set_echo_reverb_params_generic(int index, database_value old_value, database_value new_value, YM_MIXER_CONTROL_ECHO_REVERB_PARAMS_ID id)
{
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    latency_e latency = (index >= MIXER2_BASE_INDEX)? LATENCY_10MS : LATENCY_5MS;
    short val = new_value.i32;
    mixer_lib_lock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    int err = mixer_set_echo_reverb_params[latency](ym_mixer_handle, id, &val);
    mixer_lib_unlock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    if (err)
    {
        log_e("mixer_set_echo_reverb_params err:%d", err);
        database_write_no_callback(index, old_value);
    }
}

static void mic_aec_mode_callback(int index, database_value old_value, database_value new_value)
{
    mixer_set_echo_reverb_params_generic(index, old_value, new_value, YM_MIXER_SET_AEC_MODE);
}

static void aec_mic_num_callback(int index, database_value old_value, database_value new_value)
{
    mixer_set_echo_reverb_params_generic(index, old_value, new_value, YM_MIXER_SET_AEC_MIC_NUMBER);
}

static void aec_spk_num_callback(int index, database_value old_value, database_value new_value)
{
    mixer_set_echo_reverb_params_generic(index, old_value, new_value, YM_MIXER_SET_AEC_SPEAKER_NUMBER);
}

static void anf_filter_num_callback(int index, database_value old_value, database_value new_value)
{
    mixer_set_echo_reverb_params_generic(index, old_value, new_value, YM_MIXER_SET_HOWLING_FILTER_NUM);
}

static void anf_filter_dep_callback(int index, database_value old_value, database_value new_value)
{
    mixer_set_echo_reverb_params_generic(index, old_value, new_value, YM_MIXER_SET_HOWLING_FILTER_DEPTH);
}

static void aec_comfor_noise_callback(int index, database_value old_value, database_value new_value)
{
    mixer_set_echo_reverb_params_generic(index, old_value, new_value, YM_MIXER_SET_AFC_REPRO_LEVEL);
}

static void aec_dwmx_mode_callback(int index, database_value old_value, database_value new_value)
{
    database_write_no_callback(index, old_value);
    // TODO:
    // void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    //  short val = new_value.i32;
    //  int err = mixer_set_echo_reverb_params(ym_mixer_handle,
    //                                         YM_MIXER_SET_AEC_PATH_DOWNMIX_MODE,
    //                                         &val);
    //  if (err)
    //  {
    //      log_e("mixer_set_echo_reverb_params err:%d", err);
    //      database_write_no_callback(index, old_value);
    //  }
}

static void afc_path_mode_callback(int index, database_value old_value, database_value new_value)
{
    database_write_no_callback(index, old_value);
    // void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    // short val = new_value.i32;
    // int err = mixer_set_echo_reverb_params(ym_mixer_handle,
    //                                        YM_MIXER_SET_AFC_PATH_ROUTE_MODE,
    //                                        &val);
    // if (err)
    // {
    //     log_e("mixer_set_echo_reverb_params err:%d", err);
    //     database_write_no_callback(index, old_value);
    // }
}

static void afc_freq_shift_callback(int index, database_value old_value, database_value new_value)
{
    mixer_set_echo_reverb_params_generic(index, old_value, new_value, YM_MIXER_SET_AFC_FREQ_SHIFT_LEVEL);
}

static void aec_noise_level_callback(int index, database_value old_value, database_value new_value)
{
    mixer_set_echo_reverb_params_generic(index, old_value, new_value, YM_MIXER_SET_AEC_NOISE_GATE_LEVEL);
}

static void aec_0_energy_callback(int index, database_value old_value, database_value new_value)
{
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    latency_e latency = (index >= MIXER2_BASE_INDEX) ? LATENCY_10MS : LATENCY_5MS;
    mixer_lib_lock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    mixerSetMeasuredAECFilterEng[latency](ym_mixer_handle, 0, (float)new_value.i32);
    mixer_lib_unlock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
}

static void aec_1_energy_callback(int index, database_value old_value, database_value new_value)
{
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    latency_e latency = (index >= MIXER2_BASE_INDEX) ? LATENCY_10MS : LATENCY_5MS;
    mixer_lib_lock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    mixerSetMeasuredAECFilterEng[latency](ym_mixer_handle, 1, (float)new_value.i32);
    mixer_lib_unlock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
}

static void aec_delay_callback(int index, database_value old_value, database_value new_value)
{
    short tmp[4];
    database_value tmp1;
    int base = index >= MIXER2_BASE_INDEX ? MIXER2_BASE_INDEX : MIXER1_BASE_INDEX;
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    latency_e latency = (index >= MIXER2_BASE_INDEX) ? LATENCY_10MS : LATENCY_5MS;
    index -= base;
    switch (index)
    {
    case aec0_delay_ms_index:
        break;
    case aec0_length_ms_index:
        if (new_value.i32 > 240)
        {
            new_value.i32 = 240;
            database_write_no_callback(index + base, new_value);
        }
        break;
    case aec1_delay_ms_index:
        break;
    case aec1_length_ms_index:
        if (new_value.i32 > 120)
        {
            new_value.i32 = 120;
            database_write_no_callback(index + base, new_value);
        }
        break;
    }
    for (size_t i = 0; i < 4; i++)
    {
        tmp1 = database_read(aec0_delay_ms_index + i + base);
        tmp[i] = tmp1.i32;
    }
    mixer_lib_lock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    int err = mixer_set_echo_reverb_params[latency](ym_mixer_handle, YM_MIXER_SET_AEC_DELAYLINE_MS_FILTER_LEN_MS, tmp);
    mixer_lib_unlock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    if (err)
    {
        log_e("mixer_set_echo_reverb_params err:%d", err);
        database_write_no_callback(index + base, old_value);
    }
}

static void aec_bkg_decay_callback(int index, database_value old_value, database_value new_value)
{
    int base = index >= MIXER2_BASE_INDEX ? MIXER2_BASE_INDEX : MIXER1_BASE_INDEX;
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    latency_e latency = (index >= MIXER2_BASE_INDEX) ? LATENCY_10MS : LATENCY_5MS;
    index -= base;
    mixer_lib_lock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    int err = mixer_set_sys_module_params[latency](ym_mixer_handle, YM_MIXER_SET_AEC_BKG_DECAY_DB, new_value.i32);
    mixer_lib_unlock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    if (err)
    {
        log_e("mixer_set_sys_module_params err:%d", err);
        database_write_no_callback(index + base, old_value);
    }
}

static void aec_bypass_callback(int index, database_value old_value, database_value new_value)
{
    int base = index >= MIXER2_BASE_INDEX ? MIXER2_BASE_INDEX : MIXER1_BASE_INDEX;
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    latency_e latency = (index >= MIXER2_BASE_INDEX) ? LATENCY_10MS : LATENCY_5MS;
    index -= base;
    mixer_lib_lock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    int err = mixer_set_sys_module_params[latency](ym_mixer_handle, YM_MIXER_SET_AEC1_AEC1_BYPASS_MODE, new_value.i32);
    mixer_lib_unlock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    if (err)
    {
        log_e("mixer_set_sys_module_params err:%d", err);
        database_write_no_callback(index + base, old_value);
    }
}

static void geq_callback(int index, database_value old_value, database_value new_value)
{
    int err = 0;
    int moduleID = (index - geq_mic_onoff_index) / 66;
    int paramID = (index - geq_mic_onoff_index) % 66 + geq_mic_onoff_index;
    short val[MIXER_PARAM_GEQ_MAX];
    if (paramID < geq_mic_freq1_index)
    {
        paramID = paramID - geq_mic_onoff_index;
        val[0] = new_value.i32;
    }
    else if (paramID < geq_mic_gain1_index)
    {
        paramID = YM_MIXER_SET_GEQ_BANDS_FREQ;
        int idx = moduleID * 66 + geq_mic_freq1_index;
        database_value tmp;
        for (size_t i = 0; i < MIXER_PARAM_GEQ_MAX; i++)
        {
            tmp = database_read(idx + i);
            val[i] = tmp.i32;
        }
    }
    else
    {
        paramID = YM_MIXER_SET_GEQ_BANDS_GAIN;
        int idx = moduleID * 66 + geq_mic_gain1_index;
        database_value tmp;
        for (size_t i = 0; i < MIXER_PARAM_GEQ_MAX; i++)
        {
            tmp = database_read(idx + i);
            val[i] = tmp.i32;
        }
    }
    mixer_lib_lock(1);
    err |= mixer_set_geq_filter[LATENCY_5MS](ym_mixer_master_handle,
                                (YM_MIXER_PROCESS_MODULE_ID)moduleID,
                                (YM_MIXER_EQ_FILTER_PARAMS_ID)paramID,
                                val);
    mixer_lib_unlock(1);
    mixer_lib_lock(2);
    err |= mixer_set_geq_filter[LATENCY_10MS](ym_mixer_slave_handle,
                                (YM_MIXER_PROCESS_MODULE_ID)moduleID,
                                (YM_MIXER_EQ_FILTER_PARAMS_ID)paramID,
                                val);
    mixer_lib_unlock(2);
    if (err)
    {
        log_e("%d-%d err:%d", index, paramID, err);
        database_write_no_callback(index, old_value);
    }
}

static void peq_callback(int index, database_value old_value, database_value new_value)
{
    int moduleID = (index - peq_mic_onoff_index) / 34;
    int paramID = ((index - peq_mic_onoff_index) % 34) + peq_mic_onoff_index;
    short val[MIXER_PARAM_PEQ_MAX];
    if (paramID < peq_mic_freq1_index)
    {
        paramID = paramID - peq_mic_onoff_index + YM_MIXER_SET_PEQ_ON_OFF;
        val[0] = new_value.i32;
    }
    else if (paramID < peq_mic_gain1_index)
    {
        paramID = YM_MIXER_SET_PEQ_BANDS_FREQ;
        int idx = moduleID * 34 + peq_mic_freq1_index;
        database_value tmp;
        for (size_t i = 0; i < MIXER_PARAM_PEQ_MAX; i++)
        {
            tmp = database_read(idx + i);
            val[i] = tmp.i32;
        }
    }
    else if (paramID < peq_mic_filter1_index)
    {
        paramID = YM_MIXER_SET_PEQ_BANDS_GAIN;
        int idx = moduleID * 34 + peq_mic_gain1_index;
        database_value tmp;
        for (size_t i = 0; i < MIXER_PARAM_PEQ_MAX; i++)
        {
            tmp = database_read(idx + i);
            val[i] = tmp.i32;
        }
    }
    else if (paramID < peq_mic_qval1_index)
    {
        paramID = YM_MIXER_SET_PEQ_FILTER_TYPE;
        int idx = moduleID * 34 + peq_mic_filter1_index;
        database_value tmp;
        for (size_t i = 0; i < MIXER_PARAM_PEQ_MAX; i++)
        {
            tmp = database_read(idx + i);
            val[i] = tmp.i32;
        }
    }
    else
    {
        paramID = YM_MIXER_SET_PEQ_BANDS_QVALUE;
        int idx = moduleID * 34 + peq_mic_qval1_index;
        database_value tmp;
        for (size_t i = 0; i < MIXER_PARAM_PEQ_MAX; i++)
        {
            tmp = database_read(idx + i);
            val[i] = tmp.i32;
        }
    }
    int err = 0;
    mixer_lib_lock(1);
    err |= mixer_set_geq_filter[LATENCY_5MS](ym_mixer_master_handle,
                                (YM_MIXER_PROCESS_MODULE_ID)moduleID,
                                (YM_MIXER_EQ_FILTER_PARAMS_ID)paramID,
                                val);
    mixer_lib_unlock(1);

    mixer_lib_lock(2);
    err |= mixer_set_geq_filter[LATENCY_10MS](ym_mixer_slave_handle,
                                (YM_MIXER_PROCESS_MODULE_ID)moduleID,
                                (YM_MIXER_EQ_FILTER_PARAMS_ID)paramID,
                                val);
    mixer_lib_unlock(2);

    if (err)
    {
        log_e("%d-%d err:%d", index, paramID, err);
        database_write_no_callback(index, old_value);
    }
}

void peq_ms_callback(int index, database_value old_value, database_value new_value)
{
    int base = index >= MIXER2_BASE_INDEX ? MIXER2_BASE_INDEX : MIXER1_BASE_INDEX;
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    latency_e latency = (index >= MIXER2_BASE_INDEX) ? LATENCY_10MS : LATENCY_5MS;
    index -= base;
    int moduleID = (index - peq_anf_0_onoff_ms_index) / 34;
    int paramID = ((index - peq_anf_0_onoff_ms_index) % 34) + peq_anf_0_onoff_ms_index;
    short val[MIXER_PARAM_PEQ_MAX];
    if (paramID < peq_anf_0_freq1_ms_index)
    {
        paramID = paramID - peq_anf_0_onoff_ms_index + YM_MIXER_SET_PEQ_ON_OFF;
        val[0] = new_value.i32;
    }
    else if (paramID < peq_anf_0_gain1_ms_index)
    {
        paramID = YM_MIXER_SET_PEQ_BANDS_FREQ;
        int idx = moduleID * 34 + peq_anf_0_freq1_ms_index + base;
        database_value tmp;
        for (size_t i = 0; i < MIXER_PARAM_PEQ_MAX; i++)
        {
            tmp = database_read(idx + i);
            val[i] = tmp.i32;
        }
    }
    else if (paramID < peq_anf_0_filter1_ms_index)
    {
        paramID = YM_MIXER_SET_PEQ_BANDS_GAIN;
        int idx = moduleID * 34 + peq_anf_0_gain1_ms_index + base;
        database_value tmp;
        for (size_t i = 0; i < MIXER_PARAM_PEQ_MAX; i++)
        {
            tmp = database_read(idx + i);
            val[i] = tmp.i32;
        }
    }
    else if (paramID < peq_anf_0_qval1_ms_index)
    {
        paramID = YM_MIXER_SET_PEQ_FILTER_TYPE;
        int idx = moduleID * 34 + peq_anf_0_filter1_ms_index + base;
        database_value tmp;
        for (size_t i = 0; i < MIXER_PARAM_PEQ_MAX; i++)
        {
            tmp = database_read(idx + i);
            val[i] = tmp.i32;
        }
    }
    else
    {
        paramID = YM_MIXER_SET_PEQ_BANDS_QVALUE;
        int idx = moduleID * 34 + peq_anf_0_qval1_ms_index + base;
        database_value tmp;
        for (size_t i = 0; i < MIXER_PARAM_PEQ_MAX; i++)
        {
            tmp = database_read(idx + i);
            val[i] = tmp.i32;
        }
    }
    mixer_lib_lock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    int err = mixer_set_geq_filter[latency](ym_mixer_handle,
                                   (YM_MIXER_PROCESS_MODULE_ID)(moduleID + YM_MIXER_MIC_HOWLING_SUPPR0),
                                   (YM_MIXER_EQ_FILTER_PARAMS_ID)paramID,
                                   val);
    mixer_lib_unlock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    if (err)
    {
        log_e("%d-%d err:%d", index, paramID, err);
        database_write_no_callback(index + base, old_value);
    }
}

static void low_shelf_callback(int index, database_value old_value, database_value new_value)
{
    int base = index >= MIXER2_BASE_INDEX ? MIXER2_BASE_INDEX : MIXER1_BASE_INDEX;
    void *ym_mixer_handle = index >= MIXER2_BASE_INDEX ? ym_mixer_slave_handle : ym_mixer_master_handle;
    latency_e latency = (index >= MIXER2_BASE_INDEX) ? LATENCY_10MS : LATENCY_5MS;
    index -= base;
    mixer_lib_lock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
    switch (index)
    {
    case low_shelf_freq_ms_index:
    case low_shelf_gain_ms_index:
    case low_shelf_qval_ms_index:
    {
        mixer_aecMicLowShelfBiquadsDesign[latency](ym_mixer_handle,
                                          database_get(low_shelf_freq_ms_index + base)->i32,
                                          0.1f * database_get(low_shelf_gain_ms_index + base)->i32,
                                          0.001f * database_get(low_shelf_qval_ms_index + base)->i32);
    }
    break;
    case aec_highshelf_level_ms_index:
    {
        mixer_aecMicHighShelfBiquadsDesign[latency](ym_mixer_handle, new_value.i32);
    }
    break;
    default:
        break;
    }
    mixer_lib_unlock(ym_mixer_handle == ym_mixer_slave_handle ? 2 : 1);
}

static void global_skp_out_vol_callback(int index, database_value old_value, database_value new_value)
{
    int err = 0;
    mixer_lib_lock(1);
    err |= mixer_set_sys_module_params[LATENCY_5MS](ym_mixer_master_handle, YM_MATRIX_SET_SPEAKER_OUT_VOLUME, new_value.i32);
    mixer_lib_unlock(1);
    mixer_lib_lock(2);
    err |= mixer_set_sys_module_params[LATENCY_10MS](ym_mixer_slave_handle, YM_MATRIX_SET_SPEAKER_OUT_VOLUME, new_value.i32);
    mixer_lib_unlock(2);
    if (err)
    {
        log_e("mixer_set_sys_module_params err:%d", err);
        database_write_no_callback(index, old_value);
    }
}

static void global_tune_mode_vol_callback(int index, database_value old_value, database_value new_value)
{
    mixer_lib_lock(1);
    mixer_set_trailer_volume[LATENCY_5MS](ym_mixer_master_handle, new_value.i32);
    mixer_lib_unlock(1);
    mixer_lib_lock(2);
    mixer_set_trailer_volume[LATENCY_10MS](ym_mixer_slave_handle, new_value.i32);
    mixer_lib_unlock(2);
}

static void global_aecref_aec2_denoise_callback(int index, database_value old_value, database_value new_value)
{
    int err = 0;
    switch (index)
    {
    case aec_ref_denoise_index:
        mixer_lib_lock(1);
        err |= mixer_set_sys_module_params[LATENCY_5MS](ym_mixer_master_handle, YM_MIXER_SET_AEC_REF_DS_MODE, new_value.i32);
        mixer_lib_unlock(1);
        mixer_lib_lock(2);
        err |= mixer_set_sys_module_params[LATENCY_10MS](ym_mixer_slave_handle, YM_MIXER_SET_AEC_REF_DS_MODE, new_value.i32);
        mixer_lib_unlock(2);
        break;
    case aec2_denoise_ms_index + MIXER1_BASE_INDEX:
        mixer_lib_lock(1);
        err |= mixer_set_sys_module_params[LATENCY_5MS](ym_mixer_master_handle, YM_MIXER_SET_AEC2_DS_MODE, new_value.i32);
        mixer_lib_unlock(1);
        break;
    case aec2_denoise_ms_index + MIXER2_BASE_INDEX:
        mixer_lib_lock(2);
        err |= mixer_set_sys_module_params[LATENCY_10MS](ym_mixer_slave_handle, YM_MIXER_SET_AEC2_DS_MODE, new_value.i32);
        mixer_lib_unlock(2);
        break;
    case speech_enhance_level_index:
        mixer_lib_lock(1);
        err |= mixer_set_sys_module_params[LATENCY_5MS](ym_mixer_master_handle, YM_MIXER_SET_MALE_SPEECH_ENHANCE_LEVEL, new_value.i32);
        mixer_lib_unlock(1);
        mixer_lib_lock(2);
        err |= mixer_set_sys_module_params[LATENCY_10MS](ym_mixer_slave_handle, YM_MIXER_SET_MALE_SPEECH_ENHANCE_LEVEL, new_value.i32);
        mixer_lib_unlock(2);
        break;
    case speech_restore_level_index:
        mixer_lib_lock(1);
        err |= mixer_set_sys_module_params[LATENCY_5MS](ym_mixer_master_handle, YM_MIXER_SET_SPEECH_RESTORE_LEVEL, new_value.i32);
        mixer_lib_unlock(1);
        mixer_lib_lock(2);
        err |= mixer_set_sys_module_params[LATENCY_10MS](ym_mixer_slave_handle, YM_MIXER_SET_SPEECH_RESTORE_LEVEL, new_value.i32);
        mixer_lib_unlock(2);
        break;
    default:
        break;
    }
    if (err)
    {
        log_e("mixer_set_sys_module_params err:%d", err);
        database_write_no_callback(index, old_value);
    }
}

static void global_skp_mute_callback(int index, database_value old_value, database_value new_value)
{
    int err = 0;
    mixer_lib_lock(1);
    err |= mixer_set_sys_module_params[LATENCY_5MS](ym_mixer_master_handle, YM_MATRIX_SET_SPEAKER_OUTPUT_MUTE, new_value.i32);
    mixer_lib_unlock(1);
    mixer_lib_lock(2);
    err |= mixer_set_sys_module_params[LATENCY_10MS](ym_mixer_slave_handle, YM_MATRIX_SET_SPEAKER_OUTPUT_MUTE, new_value.i32);
    mixer_lib_unlock(2);
    if (err)
    {
        log_e("mixer_set_sys_module_params err:%d", err);
        database_write_no_callback(index, old_value);
    }
}

void mastre_mixer_matrix_param_default(void)
{
    /**
     * 通过遍历输出来初始化整个矩阵，规则如下：
     *  判断输出通道类型：
     *      如果输出通道类型为从核，整个输出静音。
     *      如果输出通道类型为录音，输入类型为从核，打开。
     *      如果输出通道类型为扬声器、耳机，对输入通道为AUX的，打开。
     *      如果输出通道类型为扬声器、耳机，对输入通道为参考的，打开。
     */
    const int base = MIXER1_BASE_INDEX;
    database_value tmp;
    for (size_t i = 0; i < MIXER_OUTPUT_CHN_MAX - 2; i++)
    {
        tmp.u32 = 0xFFFFFFFF;
        for (size_t j = 0; j < MIXER_INPUT_CHN_MAX; j++)
        {
            if (database_get(input_channel1_aec_mode_ms_index + base + j)->i32 < YM_MATRIX_INPUT_AEC_REF)
            {
                // 输入通道为[无AEC、AEC1、AEC2]时，对应所有输出静音（设置1）
            }
            else if (database_get(input_channel1_aec_mode_ms_index + base + j)->i32 == YM_MATRIX_INPUT_AEC_REF)
            {
                // 输入通道为[AEC参考]时，对扬声器和耳机通道关闭静音，对录音通道静音
                if ((database_get(output_channel1_type_index + i)->i32 == YM_MATRIX_OUTPUT_CHANNEL_MAIN_SPEAKER) ||
                    (database_get(output_channel1_type_index + i)->i32 == YM_MATRIX_OUTPUT_CHANNEL_SUB_HEADPHONE))
                {
                    tmp.u32 &= ~(1 << j);
                }
            }
            else if (database_get(input_channel1_aec_mode_ms_index + base + j)->i32 == YM_MATRIX_INPUT_AEC_AUX_NONE)
            {
                if ((database_get(output_channel1_type_index + i)->i32 == YM_MATRIX_OUTPUT_CHANNEL_MAIN_SPEAKER) ||
                    (database_get(output_channel1_type_index + i)->i32 == YM_MATRIX_OUTPUT_CHANNEL_SUB_HEADPHONE) ||
                    (database_get(output_channel1_type_index + i)->i32 == YM_MATRIX_OUTPUT_CHANNEL_REMOTE_OUT_REC))
                {
                    // 输入通道为[AUX直通]时，对应所有输出关闭静音（设置0）
                    tmp.u32 &= ~(1 << j);
                }
            }
        }
        // 如果输出通道为 REC，并且全部静音，则关闭AEC静音
        if ((database_get(output_channel1_type_index + i)->i32 == YM_MATRIX_OUTPUT_CHANNEL_REMOTE_OUT_REC))
        {
            tmp.u32 &= ~(1 << YM_MATRIX_INPUT_AEC_OUTPUT_CHANNEL);
        }
        database_write(ochn1_ichn_mute_ms_index + base + i, tmp);
    }
    // 如果输出通道类型为从核，整个输出静音。
    tmp.u32 = 0xFFFFFFFF;
    tmp.u32 &= ~(1 << (YM_MATRIX_INPUT_AEC_OUTPUT_CHANNEL));
    database_write(ochn1_ichn_mute_ms_index + base + MIXER_OUTPUT_CHN_MAX - 2, tmp);
    database_write(ochn1_ichn_mute_ms_index + base + MIXER_OUTPUT_CHN_MAX - 1, tmp);
}

void slave_mixer_matrix_param_default(void)
{
    /**
     * 全部静音。
     * 遍历输出，如果输出类型为扬声器、耳机，对输入通道为参考的，打开。
     * 将输出到主核的AEC打开。
     */
    const int base = MIXER2_BASE_INDEX;
    database_value tmp;
    for (size_t i = 0; i < MIXER_OUTPUT_CHN_MAX - 2; i++)
    {
        tmp.u32 = 0xFFFFFFFF;
        for (size_t j = 0; j < MIXER_INPUT_CHN_MAX; j++)
        {
            if ((database_get(input_channel1_aec_mode_ms_index + base + j)->i32 == YM_MATRIX_INPUT_AEC_REF) ||
                (database_get(input_channel1_aec_mode_ms_index + base + j)->i32 == YM_MATRIX_INPUT_AEC_AUX_NONE))
            {
                // 输入通道为[AEC参考]时，对扬声器和耳机通道关闭静音
                if ((database_get(output_channel1_type_index + i)->i32 == YM_MATRIX_OUTPUT_CHANNEL_MAIN_SPEAKER) ||
                    (database_get(output_channel1_type_index + i)->i32 == YM_MATRIX_OUTPUT_CHANNEL_SUB_HEADPHONE))
                {
                    tmp.u32 &= ~(1 << j);
                }
            }
        }
        database_write(ochn1_ichn_mute_ms_index + base + i, tmp);
    }
    // 将输出到主核的AEC打开。
    tmp.u32 = 0xFFFFFFFF;
    tmp.u32 &= ~(1 << (YM_MATRIX_INPUT_AEC_OUTPUT_CHANNEL));
    database_write(ochn1_ichn_mute_ms_index + base + MIXER_OUTPUT_CHN_MAX - 2, tmp);
    database_write(ochn1_ichn_mute_ms_index + base + MIXER_OUTPUT_CHN_MAX - 1, tmp);
}

static void mixer_lib_ms_comm_param_default(void)
{
    database_value tmp;
    for (size_t i = 0; i < MIXER_INPUT_CHN_MAX; i++)
    {
        tmp.i32 = YM_MATRIX_INPUT_DUCKER_NONE;
        database_write(input_channel1_ducker_index + i, tmp);
        tmp.i32 = 100;
        database_write(input_channel_vol1_index + i, tmp);
    }
    tmp.i32 = 100;
    database_write(mic_global_sys_vol_index, tmp);
    database_write(global_sys_out_vol_index, tmp);
    database_write(global_skp_out_vol_index, tmp);
    database_write(global_aec_out_vol_index, tmp);
    database_write(global_afc_out_vol_index, tmp);
    tmp.i32 = 0;
    database_write(global_tune_mode_vol_index, tmp);
    database_write(aec_ref_denoise_index, tmp);
    database_write(speech_enhance_level_index, tmp);
    database_write(speech_restore_level_index, tmp);
    tmp.i32 = YM_MATRIX_FALSE;
    database_write(global_mic_mute_index, tmp);
    database_write(global_out_mute_index, tmp);
    database_write(global_skp_mute_index, tmp);
    database_write(global_aec_out_mute_index, tmp);
    database_write(global_afc_out_mute_index, tmp);
    tmp.i32 = 0;
    database_write(global_mic_delay_index, tmp);
    tmp.i32 = 0;
    database_write(global_aux_delay_index, tmp);
    tmp.i32 = 255;
    database_write(global_bgm_delay_index, tmp);
    tmp.i32 = 1;
    database_write(mic_noise_gate_gain_control_on_off_index, tmp);
    tmp.i32 = -60 * 10;
    database_write(mic_noise_gate_gain_control_theshold_target_index, tmp);
    tmp.i32 = -6 * 10;
    database_write(mic_noise_gate_gain_control_makeup_boost_suppress_index, tmp);
    tmp.i32 = 6;
    database_write(mic_noise_gate_gain_control_attacktime_index, tmp);
    tmp.i32 = 60;
    database_write(mic_noise_gate_gain_control_releasetime_index, tmp);
    tmp.i32 = 0;
    database_write(mic_noise_gate_gain_control_compr_ratio_index, tmp);

    tmp.i32 = 0;
    database_write(aux_noise_gate_gain_control_on_off_index, tmp);
    tmp.i32 = -72 * 10;
    database_write(aux_noise_gate_gain_control_theshold_target_index, tmp);
    tmp.i32 = YM_MIXER_MIC_NG_DEF_COMP_GAIN_DB * 10;
    database_write(aux_noise_gate_gain_control_makeup_boost_suppress_index, tmp);
    tmp.i32 = YM_MATRIX_GAIN_DEF_ATTACK_TIME_MS;
    database_write(aux_noise_gate_gain_control_attacktime_index, tmp);
    tmp.i32 = YM_MATRIX_GAIN_DEF_RELEASE_TIME_MS;
    database_write(aux_noise_gate_gain_control_releasetime_index, tmp);
    tmp.i32 = 0;
    database_write(aux_noise_gate_gain_control_compr_ratio_index, tmp);

    tmp.i32 = 0;
    database_write(mic_drc_gain_control_on_off_index, tmp);
    tmp.i32 = (3 + YM_MIXER_DRC_DEF_THRESHOLD_DB) * 10;
    database_write(mic_drc_gain_control_theshold_target_index, tmp);
    tmp.i32 = 0;
    database_write(mic_drc_gain_control_makeup_boost_suppress_index, tmp);
    tmp.i32 = YM_MATRIX_GAIN_DEF_ATTACK_TIME_MS;
    database_write(mic_drc_gain_control_attacktime_index, tmp);
    tmp.i32 = YM_MATRIX_GAIN_DEF_RELEASE_TIME_MS;
    database_write(mic_drc_gain_control_releasetime_index, tmp);
    tmp.i32 = YM_MIXER_DRC_EXPAND_DEF_COMPR_RATIO * 10;
    database_write(mic_drc_gain_control_compr_ratio_index, tmp);

    tmp.i32 = 0;
    database_write(aux_drc_gain_control_on_off_index, tmp);
    tmp.i32 = YM_MIXER_DRC_DEF_THRESHOLD_DB * 10;
    database_write(aux_drc_gain_control_theshold_target_index, tmp);
    tmp.i32 = 0;
    database_write(aux_drc_gain_control_makeup_boost_suppress_index, tmp);
    tmp.i32 = YM_MATRIX_GAIN_DEF_ATTACK_TIME_MS;
    database_write(aux_drc_gain_control_attacktime_index, tmp);
    tmp.i32 = YM_MATRIX_GAIN_DEF_RELEASE_TIME_MS;
    database_write(aux_drc_gain_control_releasetime_index, tmp);
    tmp.i32 = YM_MIXER_DRC_EXPAND_DEF_COMPR_RATIO * 10;
    database_write(aux_drc_gain_control_compr_ratio_index, tmp);

    tmp.i32 = 1;
    database_write(mic_expander_gain_control_on_off_index, tmp);
    tmp.i32 = -57 * 10;
    database_write(mic_expander_gain_control_theshold_target_index, tmp);
    tmp.i32 = 6 * 10;
    database_write(mic_expander_gain_control_makeup_boost_suppress_index, tmp);
    tmp.i32 = 80;
    database_write(mic_expander_gain_control_attacktime_index, tmp);
    tmp.i32 = 40;
    database_write(mic_expander_gain_control_releasetime_index, tmp);
    tmp.i32 = 2 * 10;
    database_write(mic_expander_gain_control_compr_ratio_index, tmp);

    tmp.i32 = 1;
    database_write(aux_expander_gain_control_on_off_index, tmp);
    tmp.i32 = -66 * 10;
    database_write(aux_expander_gain_control_theshold_target_index, tmp);
    tmp.i32 = 18 * 10;
    database_write(aux_expander_gain_control_makeup_boost_suppress_index, tmp);
    tmp.i32 = 1200;
    database_write(aux_expander_gain_control_attacktime_index, tmp);
    tmp.i32 = 40;
    database_write(aux_expander_gain_control_releasetime_index, tmp);
    tmp.i32 = 2 * 10;
    database_write(aux_expander_gain_control_compr_ratio_index, tmp);

    tmp.i32 = 1;
    database_write(mic_agc_gain_control_on_off_index, tmp);
    tmp.i32 = -21 * 10;
    database_write(mic_agc_gain_control_theshold_target_index, tmp);
    tmp.i32 = 12 * 10;
    database_write(mic_agc_gain_control_makeup_boost_suppress_index, tmp);
    tmp.i32 = 300;
    database_write(mic_agc_gain_control_attacktime_index, tmp);
    tmp.i32 = 200;
    database_write(mic_agc_gain_control_releasetime_index, tmp);
    tmp.i32 = -57 * 10;
    database_write(mic_agc_gain_control_compr_ratio_index, tmp);

    tmp.i32 = 0;
    database_write(aux_agc_gain_control_on_off_index, tmp);
    tmp.i32 = YM_AGC_TARGET_LEVEL_DEF_DB * 10;
    database_write(aux_agc_gain_control_theshold_target_index, tmp);
    tmp.i32 = YM_AGC_COMPR_GAIN_DEF_DB * 10;
    database_write(aux_agc_gain_control_makeup_boost_suppress_index, tmp);
    tmp.i32 = YM_AGC_DEF_ATTACK_TIME_MS;
    database_write(aux_agc_gain_control_attacktime_index, tmp);
    tmp.i32 = YM_AGC_DEF_RELEASE_TIME_MS;
    database_write(aux_agc_gain_control_releasetime_index, tmp);
    tmp.i32 = -60 * 10;
    database_write(aux_agc_gain_control_compr_ratio_index, tmp);

    tmp.i32 = 0;
    database_write(mic_limiter_gain_control_on_off_index, tmp);
    tmp.i32 = YM_LIMITER_DEF_THRESHOLD_DB * 10;
    database_write(mic_limiter_gain_control_theshold_target_index, tmp);
    tmp.i32 = 0;
    database_write(mic_limiter_gain_control_makeup_boost_suppress_index, tmp);
    tmp.i32 = YM_LIMITER_DEF_ATTACK_TIME_MS;
    database_write(mic_limiter_gain_control_attacktime_index, tmp);
    tmp.i32 = YM_LIMITER_DEF_RELEASE_TIME_MS;
    database_write(mic_limiter_gain_control_releasetime_index, tmp);
    tmp.i32 = 0;
    database_write(mic_limiter_gain_control_compr_ratio_index, tmp);

    tmp.i32 = 0;
    database_write(aux_limiter_gain_control_on_off_index, tmp);
    tmp.i32 = YM_LIMITER_DEF_THRESHOLD_DB * 10;
    database_write(aux_limiter_gain_control_theshold_target_index, tmp);
    tmp.i32 = 0;
    database_write(aux_limiter_gain_control_makeup_boost_suppress_index, tmp);
    tmp.i32 = YM_LIMITER_DEF_ATTACK_TIME_MS;
    database_write(aux_limiter_gain_control_attacktime_index, tmp);
    tmp.i32 = YM_LIMITER_DEF_RELEASE_TIME_MS;
    database_write(aux_limiter_gain_control_releasetime_index, tmp);
    tmp.i32 = 0;
    database_write(aux_limiter_gain_control_compr_ratio_index, tmp);

    tmp.i32 = 1;
    database_write(sp_limiter_gain_control_on_off_index, tmp);
    tmp.i32 = -12 * 10;
    database_write(sp_limiter_gain_control_theshold_target_index, tmp);
    tmp.i32 = 6 * 10;
    database_write(sp_limiter_gain_control_makeup_boost_suppress_index, tmp);
    tmp.i32 = YM_LIMITER_DEF_ATTACK_TIME_MS;
    database_write(sp_limiter_gain_control_attacktime_index, tmp);
    tmp.i32 = YM_LIMITER_DEF_RELEASE_TIME_MS;
    database_write(sp_limiter_gain_control_releasetime_index, tmp);
    tmp.i32 = 0;
    database_write(sp_limiter_gain_control_compr_ratio_index, tmp);

    tmp.i32 = 1;
    database_write(hp_limiter_gain_control_on_off_index, tmp);
    tmp.i32 = -15 * 10;
    database_write(hp_limiter_gain_control_theshold_target_index, tmp);
    tmp.i32 = 6 * 10;
    database_write(hp_limiter_gain_control_makeup_boost_suppress_index, tmp);
    tmp.i32 = YM_LIMITER_DEF_ATTACK_TIME_MS;
    database_write(hp_limiter_gain_control_attacktime_index, tmp);
    tmp.i32 = YM_LIMITER_DEF_RELEASE_TIME_MS;
    database_write(hp_limiter_gain_control_releasetime_index, tmp);
    tmp.i32 = 0;
    database_write(hp_limiter_gain_control_compr_ratio_index, tmp);

    tmp.i32 = 1;
    database_write(rec_limiter_gain_control_on_off_index, tmp);
    tmp.i32 = -7 * 10;
    database_write(rec_limiter_gain_control_theshold_target_index, tmp);
    tmp.i32 = 6 * 10;
    database_write(rec_limiter_gain_control_makeup_boost_suppress_index, tmp);
    tmp.i32 = YM_LIMITER_DEF_ATTACK_TIME_MS;
    database_write(rec_limiter_gain_control_attacktime_index, tmp);
    tmp.i32 = YM_LIMITER_DEF_RELEASE_TIME_MS;
    database_write(rec_limiter_gain_control_releasetime_index, tmp);
    tmp.i32 = 0;
    database_write(rec_limiter_gain_control_compr_ratio_index, tmp);

    tmp.i32 = 1;
    database_write(auto_ducker_gain_control_on_off_index, tmp);
    tmp.i32 = (-36) * 10;
    database_write(auto_ducker_gain_control_theshold_target_index, tmp);
    tmp.i32 = (-24) * 10;
    database_write(auto_ducker_gain_control_makeup_boost_suppress_index, tmp);
    tmp.i32 = YM_DUCK_DEF_ATTACK_TIME_MS / 2;
    database_write(auto_ducker_gain_control_attacktime_index, tmp);
    tmp.i32 = YM_DUCK_DEF_RELEASE_TIME_MS;
    database_write(auto_ducker_gain_control_releasetime_index, tmp);
    tmp.i32 = YM_DUCK_DEF_KEEP_TIME_MS;
    database_write(auto_ducker_gain_control_compr_ratio_index, tmp);

    tmp.i32 = 1;
    database_write(mic_aec1_auto_mixer_gain_control_on_off_index, tmp);
    tmp.i32 = -42 * 10;
    database_write(mic_aec1_auto_mixer_gain_control_theshold_target_index, tmp);
    tmp.i32 = 0;
    database_write(mic_aec1_auto_mixer_gain_control_makeup_boost_suppress_index, tmp);
    tmp.i32 = 40;
    database_write(mic_aec1_auto_mixer_gain_control_attacktime_index, tmp);
    tmp.i32 = 80;
    database_write(mic_aec1_auto_mixer_gain_control_releasetime_index, tmp);
    tmp.i32 = 30 * 10;
    database_write(mic_aec1_auto_mixer_gain_control_compr_ratio_index, tmp);

    tmp.i32 = 1;
    database_write(mic_aec0_auto_mixer_gain_control_on_off_index, tmp);
    tmp.i32 = -421;
    database_write(mic_aec0_auto_mixer_gain_control_theshold_target_index, tmp);
    tmp.i32 = 0;
    database_write(mic_aec0_auto_mixer_gain_control_makeup_boost_suppress_index, tmp);
    tmp.i32 = 240;
    database_write(mic_aec0_auto_mixer_gain_control_attacktime_index, tmp);
    tmp.i32 = 300;
    database_write(mic_aec0_auto_mixer_gain_control_releasetime_index, tmp);
    tmp.i32 = 30 * 10 + 1;
    database_write(mic_aec0_auto_mixer_gain_control_compr_ratio_index, tmp);
}

static void mixer_lib_param_default(const int base)
{
    database_value tmp;

    tmp.i32 = 96 * YM_MATRIX_FRAME_DB_QUANT;
    for (size_t i = 0; i < mixer_input_channel1_sig_db_ms_unit; i++)
    {
        database_write(mixer_input_channel1_sig_db_ms_index + base + i, tmp);
    }

    for (size_t i = 0; i < MIXER_INPUT_CHN_MAX; i++)
    {
        tmp.i32 = 100;
        database_write(input_channel_vol1_index + i, tmp);
        tmp.i32 = YM_MATRIX_FALSE;
        database_write(input_channel1_mute_index + i, tmp);
        tmp.i32 = YM_MIXER_ATM_IN_CHAN_DEF_PRIORITY;
        database_write(input_channel1_atm_mode_ms_index + base + i, tmp);
        tmp.i32 = YM_MIXER_ATM_IN_CHAN_DEF_DECAYRATIO;
        database_write(input_channel1_atm_decay_ms_index + base + i, tmp);
        tmp.i32 = 0;
        database_write(input_channel1_delay_ms_index + base + i, tmp);
        // if (i < AUTOMIXER_MIC_CHNS)
        // {
        //     tmp.i32 = YM_MATRIX_INPUT_AEC0_MICS;
        //     database_write(input_channel1_aec_mode_ms_index + base + i, tmp);
        // }
        // else if (i < AUTOMIXER_MIC_CHNS + AUTOMIXER_IAUX_CHNS)
        // {
        //     tmp.i32 = YM_MATRIX_INPUT_AEC_AUX_NONE;
        //     database_write(input_channel1_aec_mode_ms_index + base + i, tmp);
        // }
        // else if (i < AUTOMIXER_MIC_CHNS + AUTOMIXER_IAUX_CHNS + AUTOMIXER_WMIC_CHNS)
        // {
        //     tmp.i32 = YM_MATRIX_INPUT_AEC1_MICS;
        //     database_write(input_channel1_aec_mode_ms_index + base + i, tmp);
        // }
        // else if (i < AUTOMIXER_MIC_CHNS + AUTOMIXER_IAUX_CHNS + AUTOMIXER_WMIC_CHNS + AUTOMIXER_SMIC_CHNS)
        // {
        //     tmp.i32 = YM_MATRIX_INPUT_AEC0_MICS;
        //     database_write(input_channel1_aec_mode_ms_index + base + i, tmp);
        // }
        // else if (i < AUTOMIXER_MIC_CHNS + AUTOMIXER_IAUX_CHNS + AUTOMIXER_WMIC_CHNS + AUTOMIXER_SMIC_CHNS + AUTOMIXER_IREC_CHNS)
        // {
        //     tmp.i32 = YM_MATRIX_INPUT_AEC_REF;
        //     database_write(input_channel1_aec_mode_ms_index + base + i, tmp);
        // }
        // else
        // {
        //     tmp.i32 = YM_MATRIX_INPUT_AEC_MIC_NONE;
        //     database_write(input_channel1_aec_mode_ms_index + base + i, tmp);
        // }
    }

    for (size_t i = 0; i < MIXER_OUTPUT_CHN_MAX; i++)
    {
        if (base == MIXER1_BASE_INDEX)
        {
            tmp.i32 = 100;
        }
        else if (base == MIXER2_BASE_INDEX)
        {
            tmp.i32 = 1;
        }
        database_write(output_channel_vol1_ms_index + base + i, tmp);
        tmp.i32 = YM_MATRIX_FALSE;
        database_write(output_channel1_mute_ms_index + base + i, tmp);
        tmp.i32 = YM_MATRIX_FALSE;
        database_write(output_channel1_phase_invert_index + i, tmp);
        tmp.i32 = 0;
        database_write(output_channel1_delay_ms_index + base + i, tmp);
        if (i < AUTOMIXER_SPK_CHNS)
        {
            if (base == MIXER1_BASE_INDEX)
            {
                tmp.i32 = 50;
            }
            else if (base == MIXER2_BASE_INDEX)
            {
                tmp.i32 = 1;
            }
            database_write(output_channel_vol1_ms_index + base + i, tmp);
            tmp.i32 = YM_MATRIX_OUTPUT_CHANNEL_MAIN_SPEAKER;
            database_write(output_channel1_type_index + i, tmp);
        }
        else if (i < AUTOMIXER_SPK_CHNS + AUTOMIXER_HP_CHNS)
        {
            tmp.i32 = YM_MATRIX_OUTPUT_CHANNEL_SUB_HEADPHONE;
            database_write(output_channel1_type_index + i, tmp);
        }
        else
        {
            tmp.i32 = YM_MATRIX_OUTPUT_CHANNEL_REMOTE_OUT_REC;
            database_write(output_channel1_type_index + i, tmp);
        }
    }

    tmp.i32 = 100;
    for (size_t i = 0; i < MIXER_OUTPUT_CHN_MAX; i++)
    {
        for (size_t j = 0; j < (MIXER_INPUT_CHN_MAX); j++)
        {
            database_write(ochn1_ichn1_vol_ms_index + base + i * (ochn1_ichn1_vol_unit_count) + j, tmp);
        }
        database_write(ochn1_ichn1_vol_ms_index + base + i * (ochn1_ichn1_vol_unit_count) + YM_MATRIX_INPUT_AEC_OUTPUT_CHANNEL, tmp); // AEC
        database_write(ochn1_ichn1_vol_ms_index + base + i * (ochn1_ichn1_vol_unit_count) + YM_MATRIX_INPUT_AFC_OUTPUT_CHANNEL, tmp); // AFC
    }

    tmp.i32 = YM_MIXER_NS_MODE4;
    // if (base == MIXER1_BASE_INDEX)
    //     tmp.i32 += 12;
    database_write(mic_ans_mode_ms_index + base, tmp); // mic ans here
    tmp.i32 = YM_MATRIX_DEREVERB_MODE2;
    database_write(dereverb_level_ms_index + base, tmp);
    tmp.i32 = YM_MATRIX_NO_TEST_SIGNAL;
    database_write(test_sig_type_ms_index + base, tmp);
    tmp.i32 = YM_MATRIX_TEST_SIGNAL_DEF_FREQ;
    database_write(test_sig_freq_ms_index + base, tmp);
    tmp.i32 = 10 * YM_MATRIX_TEST_SIGNAL_DEF_LEVEL_DB;
    database_write(test_sig_amp_ms_index + base, tmp);

    tmp.i32 = 0;
    database_write(howling_enable_ms_index + base, tmp);
    tmp.i32 = YM_MIXER_AEC_DTD_MODE3;
    database_write(mic_aec_mode_ms_index + base, tmp);
    // tmp.i32 = AUTOMIXER_MIC_CHNS;
    // database_write(aec_mic_num_ms_index + base, tmp);
    tmp.i32 = AUTOMIXER_SPK_CHNS;
    database_write(aec_spk_num_ms_index + base, tmp);
    tmp.i32 = YM_MIXER_ANF_DEF_PEQ_FILTERS_NUM;
    database_write(anf_filter_num_ms_index + base, tmp);
    // tmp.i32 = YM_MIXER_ANF_DEF_NOTCH_DEPTH;
    tmp.i32 = 15;
    database_write(anf_filter_dep_ms_index + base, tmp);
    tmp.i32 = 3;
    database_write(aec_comfor_noise_ms_index + base, tmp);
    // TODO:
    //  tmp.i32 = YM_MIXER_AEC_PATH_DOWNMIX_AFTER_DENOISE;
    //  database_write(aec_dwmx_mode_ms_index + base, tmp);
    //  tmp.i32 = YM_MIXER_AEC_PATH_ROUTE_AEC0_ONLY;
    //  database_write(afc_path_mode_ms_index + base, tmp);
    tmp.i32 = 59;
    database_write(afc_freq_shift_ms_index + base, tmp);
    tmp.i32 = -54 * 10;
    database_write(aec_noise_level_ms_index + base, tmp);
    tmp.i32 = 0;
    database_write(aec_0_energy_ms_index + base, tmp);
    tmp.i32 = 0;
    database_write(aec_1_energy_ms_index + base, tmp);

    if (base == MIXER1_BASE_INDEX)
    {
        tmp.i32 = 10;
    }
    else
    {
        tmp.i32 = 5;
    }
    database_write(aec0_delay_ms_index + base, tmp);
    database_write(aec1_delay_ms_index + base, tmp);

    tmp.i32 = 200;
    database_write(aec0_length_ms_index + base, tmp);
    tmp.i32 = 20;
    database_write(aec1_length_ms_index + base, tmp);

    tmp.i32 = 150;
    database_write_no_callback(low_shelf_freq_ms_index + base, tmp);
    tmp.i32 = -9 * 10;
    database_write_no_callback(low_shelf_gain_ms_index + base, tmp);
    tmp.i32 = 707;
    database_write(low_shelf_qval_ms_index + base, tmp);
    tmp.i32 = 0;
    database_write(aec_highshelf_level_ms_index + base, tmp);
    database_write(aec2_denoise_ms_index + base, tmp);
    database_write(aec_bypass_ms_index + base, tmp);
}

static void mixer_lib_geq_init(void)
{
    database_value tmp;

    int default_band_freq[YM_MIXER_DEFAULT_GEQ_BANDS_MIC] =
        {31, 40, 50, 63, 80, 100, 125, 160, 200, 250,
         315, 400, 500, 630, 800, 1000, 1250, 1600, 2000, 2500,
         3200, 4000, 5000, 6300, 8000, 10000, 12500, 16000, 20000, 22000};

    int default_band_gain[YM_MIXER_DEFAULT_GEQ_BANDS_MIC] =
        {0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
         0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
         0, 0, 0, 0, 0, 0, 0, 0, 0, 0};

    {
        // MIC_IN
        tmp.i32 = 0;
        database_write(geq_mic_onoff_index, tmp);
        tmp.i32 = YM_MIXER_DEFAULT_GEQ_BANDS_MIC;
        database_write(geq_mic_cnt_index, tmp);
        database_write2_no_callback(geq_mic_freq1_index, (database_value *)default_band_freq, YM_MIXER_DEFAULT_GEQ_BANDS_MIC);
        database_write2_no_callback(geq_mic_gain1_index, (database_value *)default_band_gain, YM_MIXER_DEFAULT_GEQ_BANDS_MIC);
        database_write(geq_mic_freq1_index, *database_get(geq_mic_freq1_index));
        database_write(geq_mic_gain1_index, *database_get(geq_mic_gain1_index));
    }

    {
        // MUSIC_IN
        tmp.i32 = 0;
        database_write(geq_aux_onoff_index, tmp);
        tmp.i32 = YM_MIXER_DEFAULT_GEQ_BANDS_MIC;
        database_write(geq_aux_cnt_index, tmp);
        database_write2_no_callback(geq_aux_freq1_index, (database_value *)default_band_freq, YM_MIXER_DEFAULT_GEQ_BANDS_MIC);
        database_write2_no_callback(geq_aux_gain1_index, (database_value *)default_band_gain, YM_MIXER_DEFAULT_GEQ_BANDS_MIC);
        database_write(geq_aux_freq1_index, *database_get(geq_aux_freq1_index));
        database_write(geq_aux_gain1_index, *database_get(geq_aux_gain1_index));
    }

    {
        // SP_OUT
        tmp.i32 = 0;
        database_write(geq_spk_onoff_index, tmp);
        tmp.i32 = YM_MIXER_DEFAULT_GEQ_BANDS_MIC;
        database_write(geq_spk_cnt_index, tmp);
        database_write2_no_callback(geq_spk_freq1_index, (database_value *)default_band_freq, YM_MIXER_DEFAULT_GEQ_BANDS_MIC);
        database_write2_no_callback(geq_spk_gain1_index, (database_value *)default_band_gain, YM_MIXER_DEFAULT_GEQ_BANDS_MIC);
        database_write(geq_spk_freq1_index, *database_get(geq_spk_freq1_index));
        database_write(geq_spk_gain1_index, *database_get(geq_spk_gain1_index));
    }

    {
        // HP_OUT
        tmp.i32 = 0;
        database_write(geq_hp_onoff_index, tmp);
        tmp.i32 = YM_MIXER_DEFAULT_GEQ_BANDS_MIC;
        database_write(geq_hp_cnt_index, tmp);
        database_write2_no_callback(geq_hp_freq1_index, (database_value *)default_band_freq, YM_MIXER_DEFAULT_GEQ_BANDS_MIC);
        database_write2_no_callback(geq_hp_gain1_index, (database_value *)default_band_gain, YM_MIXER_DEFAULT_GEQ_BANDS_MIC);
        database_write(geq_hp_freq1_index, *database_get(geq_hp_freq1_index));
        database_write(geq_hp_gain1_index, *database_get(geq_hp_gain1_index));
    }

    {
        // REC_OUT
        tmp.i32 = 0;
        database_write(geq_rec_onoff_index, tmp);
        tmp.i32 = YM_MIXER_DEFAULT_GEQ_BANDS_MIC;
        database_write(geq_rec_cnt_index, tmp);
        database_write2_no_callback(geq_rec_freq1_index, (database_value *)default_band_freq, YM_MIXER_DEFAULT_GEQ_BANDS_MIC);
        database_write2_no_callback(geq_rec_gain1_index, (database_value *)default_band_gain, YM_MIXER_DEFAULT_GEQ_BANDS_MIC);
        database_write(geq_rec_freq1_index, *database_get(geq_rec_freq1_index));
        database_write(geq_rec_gain1_index, *database_get(geq_rec_gain1_index));
    }
}

const int band_freq[MIXER_PARAM_PEQ_MAX] = {50, 125, 250, 500, 1000, 2000, 4000, 6000};
const int band_gain[MIXER_PARAM_PEQ_MAX] = {0, 0, 0, 0, 0, 0, 0, 0};
const int band_qval[MIXER_PARAM_PEQ_MAX] = {YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE,
                                            YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE, YM_PEQ_DEF_Q_VALUE};
const int filter_type[MIXER_PARAM_PEQ_MAX] = {YM_PEQ_LOW_SHELF_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER,
                                              YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_PEAKEQ_FILTER, YM_PEQ_HIGH_SHELF_FILTER};

static void mixer_lib_peq_init(void)
{

    database_value tmp;

    {
        // MIC_IN
        tmp.i32 = 0;
        database_write(peq_mic_onoff_index, tmp);
        tmp.i32 = YM_PEQ_MAX_BANDS_NUM;
        database_write(peq_mic_cnt_index, tmp);
        database_write2_no_callback(peq_mic_freq1_index, (database_value *)band_freq, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_mic_gain1_index, (database_value *)band_gain, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_mic_filter1_index, (database_value *)filter_type, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_mic_qval1_index, (database_value *)band_qval, MIXER_PARAM_PEQ_MAX);
        database_write(peq_mic_freq1_index, *database_get(peq_mic_freq1_index));
        database_write(peq_mic_gain1_index, *database_get(peq_mic_gain1_index));
        database_write(peq_mic_filter1_index, *database_get(peq_mic_filter1_index));
        database_write(peq_mic_qval1_index, *database_get(peq_mic_qval1_index));
    }
    {
        // MUSIC_IN
        tmp.i32 = 0;
        database_write(peq_aux_onoff_index, tmp);
        tmp.i32 = YM_PEQ_MAX_BANDS_NUM;
        database_write(peq_aux_cnt_index, tmp);
        database_write2_no_callback(peq_aux_freq1_index, (database_value *)band_freq, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_aux_gain1_index, (database_value *)band_gain, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_aux_filter1_index, (database_value *)filter_type, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_aux_qval1_index, (database_value *)band_qval, MIXER_PARAM_PEQ_MAX);
        database_write(peq_aux_freq1_index, *database_get(peq_aux_freq1_index));
        database_write(peq_aux_gain1_index, *database_get(peq_aux_gain1_index));
        database_write(peq_aux_filter1_index, *database_get(peq_aux_filter1_index));
        database_write(peq_aux_qval1_index, *database_get(peq_aux_qval1_index));
    }
    {
        // SP_OUT
        tmp.i32 = 0;
        database_write(peq_spk_onoff_index, tmp);
        tmp.i32 = YM_PEQ_MAX_BANDS_NUM;
        database_write(peq_spk_cnt_index, tmp);
        database_write2_no_callback(peq_spk_freq1_index, (database_value *)band_freq, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_spk_gain1_index, (database_value *)band_gain, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_spk_filter1_index, (database_value *)filter_type, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_spk_qval1_index, (database_value *)band_qval, MIXER_PARAM_PEQ_MAX);
        database_write(peq_spk_freq1_index, *database_get(peq_spk_freq1_index));
        database_write(peq_spk_gain1_index, *database_get(peq_spk_gain1_index));
        database_write(peq_spk_filter1_index, *database_get(peq_spk_filter1_index));
        database_write(peq_spk_qval1_index, *database_get(peq_spk_qval1_index));
    }
    {
        // HP_OUT
        tmp.i32 = 0;
        database_write(peq_hp_onoff_index, tmp);
        tmp.i32 = YM_PEQ_MAX_BANDS_NUM;
        database_write(peq_hp_cnt_index, tmp);
        database_write2_no_callback(peq_hp_freq1_index, (database_value *)band_freq, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_hp_gain1_index, (database_value *)band_gain, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_hp_filter1_index, (database_value *)filter_type, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_hp_qval1_index, (database_value *)band_qval, MIXER_PARAM_PEQ_MAX);
        database_write(peq_hp_freq1_index, *database_get(peq_hp_freq1_index));
        database_write(peq_hp_gain1_index, *database_get(peq_hp_gain1_index));
        database_write(peq_hp_filter1_index, *database_get(peq_hp_filter1_index));
        database_write(peq_hp_qval1_index, *database_get(peq_hp_qval1_index));
    }
    {
        // REC_OUT
        tmp.i32 = 0;
        database_write(peq_rec_onoff_index, tmp);
        tmp.i32 = YM_PEQ_MAX_BANDS_NUM;
        database_write(peq_rec_cnt_index, tmp);
        database_write2_no_callback(peq_rec_freq1_index, (database_value *)band_freq, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_rec_gain1_index, (database_value *)band_gain, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_rec_filter1_index, (database_value *)filter_type, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_rec_qval1_index, (database_value *)band_qval, MIXER_PARAM_PEQ_MAX);
        database_write(peq_rec_freq1_index, *database_get(peq_rec_freq1_index));
        database_write(peq_rec_gain1_index, *database_get(peq_rec_gain1_index));
        database_write(peq_rec_filter1_index, *database_get(peq_rec_filter1_index));
        database_write(peq_rec_qval1_index, *database_get(peq_rec_qval1_index));
    }
}

static void mixer_lib_peq_ms_init(const int base)
{
    database_value tmp;
    {
        // MIC_HOWLING_SUPPR0
        tmp.i32 = 0;
        database_write(peq_anf_0_onoff_ms_index + base, tmp);
        tmp.i32 = YM_PEQ_MAX_BANDS_NUM;
        database_write(peq_anf_0_cnt_ms_index + base, tmp);
        database_write2_no_callback(peq_anf_0_freq1_ms_index + base, (database_value *)band_freq, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_anf_0_gain1_ms_index + base, (database_value *)band_gain, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_anf_0_filter1_ms_index + base, (database_value *)filter_type, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_anf_0_qval1_ms_index + base, (database_value *)band_qval, MIXER_PARAM_PEQ_MAX);
        database_write(peq_anf_0_freq1_ms_index + base, *database_get(peq_anf_0_freq1_ms_index + base));
        database_write(peq_anf_0_gain1_ms_index + base, *database_get(peq_anf_0_gain1_ms_index + base));
        database_write(peq_anf_0_filter1_ms_index + base, *database_get(peq_anf_0_filter1_ms_index + base));
        database_write(peq_anf_0_qval1_ms_index + base, *database_get(peq_anf_0_qval1_ms_index + base));
    }
    {
        // MIC_HOWLING_SUPPR1
        tmp.i32 = 0;
        database_write(peq_anf_1_onoff_ms_index + base, tmp);
        tmp.i32 = YM_PEQ_MAX_BANDS_NUM;
        database_write(peq_anf_1_cnt_ms_index + base, tmp);
        database_write2_no_callback(peq_anf_1_freq1_ms_index + base, (database_value *)band_freq, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_anf_1_gain1_ms_index + base, (database_value *)band_gain, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_anf_1_filter1_ms_index + base, (database_value *)filter_type, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_anf_1_qval1_ms_index + base, (database_value *)band_qval, MIXER_PARAM_PEQ_MAX);
        database_write(peq_anf_1_freq1_ms_index + base, *database_get(peq_anf_1_freq1_ms_index + base));
        database_write(peq_anf_1_gain1_ms_index + base, *database_get(peq_anf_1_gain1_ms_index + base));
        database_write(peq_anf_1_filter1_ms_index + base, *database_get(peq_anf_1_filter1_ms_index + base));
        database_write(peq_anf_1_qval1_ms_index + base, *database_get(peq_anf_1_qval1_ms_index + base));
    }
    {
        // MIC_HOWLING_SUPPR2
        tmp.i32 = 0;
        database_write(peq_anf_2_onoff_ms_index + base, tmp);
        tmp.i32 = YM_PEQ_MAX_BANDS_NUM;
        database_write(peq_anf_2_cnt_ms_index + base, tmp);
        database_write2_no_callback(peq_anf_2_freq1_ms_index + base, (database_value *)band_freq, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_anf_2_gain1_ms_index + base, (database_value *)band_gain, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_anf_2_filter1_ms_index + base, (database_value *)filter_type, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_anf_2_qval1_ms_index + base, (database_value *)band_qval, MIXER_PARAM_PEQ_MAX);
        database_write(peq_anf_2_freq1_ms_index + base, *database_get(peq_anf_2_freq1_ms_index + base));
        database_write(peq_anf_2_gain1_ms_index + base, *database_get(peq_anf_2_gain1_ms_index + base));
        database_write(peq_anf_2_filter1_ms_index + base, *database_get(peq_anf_2_filter1_ms_index + base));
        database_write(peq_anf_2_qval1_ms_index + base, *database_get(peq_anf_2_qval1_ms_index + base));
    }

    {
        // AEC1
        tmp.i32 = 0;
        database_write(peq_aec_1_onoff_ms_index + base, tmp);
        tmp.i32 = YM_PEQ_MAX_BANDS_NUM;
        database_write(peq_aec_1_cnt_ms_index + base, tmp);
        database_write2_no_callback(peq_aec_1_freq1_ms_index + base, (database_value *)band_freq, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_aec_1_gain1_ms_index + base, (database_value *)band_gain, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_aec_1_filter1_ms_index + base, (database_value *)filter_type, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_aec_1_qval1_ms_index + base, (database_value *)band_qval, MIXER_PARAM_PEQ_MAX);
        database_write(peq_aec_1_freq1_ms_index + base, *database_get(peq_aec_1_freq1_ms_index + base));
        database_write(peq_aec_1_gain1_ms_index + base, *database_get(peq_aec_1_gain1_ms_index + base));
        database_write(peq_aec_1_filter1_ms_index + base, *database_get(peq_aec_1_filter1_ms_index + base));
        database_write(peq_aec_1_qval1_ms_index + base, *database_get(peq_aec_1_qval1_ms_index + base));
    }

    {
        // AEC2
        tmp.i32 = 0;
        database_write(peq_aec_2_onoff_ms_index + base, tmp);
        tmp.i32 = YM_PEQ_MAX_BANDS_NUM;
        database_write(peq_aec_2_cnt_ms_index + base, tmp);
        database_write2_no_callback(peq_aec_2_freq1_ms_index + base, (database_value *)band_freq, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_aec_2_gain1_ms_index + base, (database_value *)band_gain, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_aec_2_filter1_ms_index + base, (database_value *)filter_type, MIXER_PARAM_PEQ_MAX);
        database_write2_no_callback(peq_aec_2_qval1_ms_index + base, (database_value *)band_qval, MIXER_PARAM_PEQ_MAX);
        database_write(peq_aec_2_freq1_ms_index + base, *database_get(peq_aec_2_freq1_ms_index + base));
        database_write(peq_aec_2_gain1_ms_index + base, *database_get(peq_aec_2_gain1_ms_index + base));
        database_write(peq_aec_2_filter1_ms_index + base, *database_get(peq_aec_2_filter1_ms_index + base));
        database_write(peq_aec_2_qval1_ms_index + base, *database_get(peq_aec_2_qval1_ms_index + base));
    }
}

__weak void platform_mixer_lib_ms_comm_param_register(void) {}

__weak void platform_mixer_lib_param_register(const int base) {}

__weak void platform_mastre_mixer_matrix_param_default(void) {}
__weak void platform_slave_mixer_matrix_param_default(void) {}

__weak void platform_mixer_lib_auto_param_init(void) {}

void mixer_lib_auto_param_init(void)
{
    // 如果通道类型设置为 AEC0，将对应通道衰减因子改为0，只设置第一个AEC0通道。
    // 其他通道设置为20
    int is_set_decay = 0;
    for (size_t i = 0; i < MIXER_INPUT_CHN_MAX; i++)
    {
        database_value tmp;
        tmp = *database_get(input_channel1_aec_mode_ms_index + i + MIXER1_BASE_INDEX);
        if ((tmp.i32 == YM_MATRIX_INPUT_AEC0_MICS || tmp.i32 == YM_MATRIX_INPUT_AEC0_MICS_PEQ) && (is_set_decay == 0))
        {
            tmp.i32 = 0;
            is_set_decay = 1;
            database_write(input_channel1_atm_decay_ms_index + i + MIXER1_BASE_INDEX, tmp);
        }
        else
        {
            tmp.i32 = 20;
            database_write(input_channel1_atm_decay_ms_index + i + MIXER1_BASE_INDEX, tmp);
        }
    }
#if !defined(WEN_XIANG_BOARD)
    is_set_decay = 0;
    for (size_t i = 0; i < MIXER_INPUT_CHN_MAX; i++)
    {
        database_value tmp;
        tmp = *database_get(input_channel1_aec_mode_ms_index + i + MIXER2_BASE_INDEX);
        if ((tmp.i32 == YM_MATRIX_INPUT_AEC0_MICS || tmp.i32 == YM_MATRIX_INPUT_AEC0_MICS_PEQ) && (is_set_decay == 0))
        {
            tmp.i32 = 0;
            is_set_decay = 1;
            database_write(input_channel1_atm_decay_ms_index + i + MIXER2_BASE_INDEX, tmp);
        }
        else if (tmp.i32 == YM_MATRIX_INPUT_AEC_MIC_NONE || tmp.i32 == YM_MATRIX_INPUT_AEC1_MICS || tmp.i32 == YM_MATRIX_INPUT_AEC1_MICS_PEQ)
        {
            tmp.i32 = 20;
            database_write(input_channel1_atm_decay_ms_index + i + MIXER2_BASE_INDEX, tmp);
        }
    }
#endif /* end #if !defined(WEN_XIANG_BOARD) */
}

static void mixer_lib_ms_comm_param_register(void)
{
    database_register(mic_global_sys_vol_index, mic_global_vol_callback);
    database_register(global_sys_out_vol_index, global_sys_out_vol_callback);

    database_register(global_aec_out_vol_index, global_aec_out_vol_callback);
    database_register(global_afc_out_vol_index, global_afc_out_vol_callback);
    database_register(global_aec_out_mute_index, global_aec_out_mute_callback);
    database_register(global_afc_out_mute_index, global_afc_out_mute_callback);

    database_register(global_mic_mute_index, global_mic_mute_callback);
    database_register(global_out_mute_index, global_out_mute_callback);

    database_register(global_mic_delay_index, global_mic_delay_callback);
    database_register(global_aux_delay_index, global_aux_delay_callback);
    database_register(global_bgm_delay_index, global_bgm_delay_callback);
    database_register(global_skp_out_vol_index, global_skp_out_vol_callback);
    database_register(global_tune_mode_vol_index, global_tune_mode_vol_callback);
    database_register(global_skp_mute_index, global_skp_mute_callback);
    database_register(aec_ref_denoise_index, global_aecref_aec2_denoise_callback);

    database_register(speech_enhance_level_index, global_aecref_aec2_denoise_callback);
    database_register(speech_restore_level_index, global_aecref_aec2_denoise_callback);

    database_register(mic_noise_gate_gain_control_on_off_index, gain_control_callback);
    database_register(mic_noise_gate_gain_control_theshold_target_index, gain_control_callback);
    database_register(mic_noise_gate_gain_control_makeup_boost_suppress_index, gain_control_callback);
    database_register(mic_noise_gate_gain_control_attacktime_index, gain_control_callback);
    database_register(mic_noise_gate_gain_control_releasetime_index, gain_control_callback);
    database_register(mic_noise_gate_gain_control_compr_ratio_index, gain_control_callback);
    database_register(aux_noise_gate_gain_control_on_off_index, gain_control_callback);
    database_register(aux_noise_gate_gain_control_theshold_target_index, gain_control_callback);
    database_register(aux_noise_gate_gain_control_makeup_boost_suppress_index, gain_control_callback);
    database_register(aux_noise_gate_gain_control_attacktime_index, gain_control_callback);
    database_register(aux_noise_gate_gain_control_releasetime_index, gain_control_callback);
    database_register(aux_noise_gate_gain_control_compr_ratio_index, gain_control_callback);
    database_register(mic_drc_gain_control_on_off_index, gain_control_callback);
    database_register(mic_drc_gain_control_theshold_target_index, gain_control_callback);
    database_register(mic_drc_gain_control_makeup_boost_suppress_index, gain_control_callback);
    database_register(mic_drc_gain_control_attacktime_index, gain_control_callback);
    database_register(mic_drc_gain_control_releasetime_index, gain_control_callback);
    database_register(mic_drc_gain_control_compr_ratio_index, gain_control_callback);
    database_register(aux_drc_gain_control_on_off_index, gain_control_callback);
    database_register(aux_drc_gain_control_theshold_target_index, gain_control_callback);
    database_register(aux_drc_gain_control_makeup_boost_suppress_index, gain_control_callback);
    database_register(aux_drc_gain_control_attacktime_index, gain_control_callback);
    database_register(aux_drc_gain_control_releasetime_index, gain_control_callback);
    database_register(aux_drc_gain_control_compr_ratio_index, gain_control_callback);
    database_register(mic_expander_gain_control_on_off_index, gain_control_callback);
    database_register(mic_expander_gain_control_theshold_target_index, gain_control_callback);
    database_register(mic_expander_gain_control_makeup_boost_suppress_index, gain_control_callback);
    database_register(mic_expander_gain_control_attacktime_index, gain_control_callback);
    database_register(mic_expander_gain_control_releasetime_index, gain_control_callback);
    database_register(mic_expander_gain_control_compr_ratio_index, gain_control_callback);
    database_register(aux_expander_gain_control_on_off_index, gain_control_callback);
    database_register(aux_expander_gain_control_theshold_target_index, gain_control_callback);
    database_register(aux_expander_gain_control_makeup_boost_suppress_index, gain_control_callback);
    database_register(aux_expander_gain_control_attacktime_index, gain_control_callback);
    database_register(aux_expander_gain_control_releasetime_index, gain_control_callback);
    database_register(aux_expander_gain_control_compr_ratio_index, gain_control_callback);
    database_register(mic_agc_gain_control_on_off_index, gain_control_callback);
    database_register(mic_agc_gain_control_theshold_target_index, gain_control_callback);
    database_register(mic_agc_gain_control_makeup_boost_suppress_index, gain_control_callback);
    database_register(mic_agc_gain_control_attacktime_index, gain_control_callback);
    database_register(mic_agc_gain_control_releasetime_index, gain_control_callback);
    database_register(mic_agc_gain_control_compr_ratio_index, gain_control_callback);
    database_register(aux_agc_gain_control_on_off_index, gain_control_callback);
    database_register(aux_agc_gain_control_theshold_target_index, gain_control_callback);
    database_register(aux_agc_gain_control_makeup_boost_suppress_index, gain_control_callback);
    database_register(aux_agc_gain_control_attacktime_index, gain_control_callback);
    database_register(aux_agc_gain_control_releasetime_index, gain_control_callback);
    database_register(aux_agc_gain_control_compr_ratio_index, gain_control_callback);
    database_register(mic_limiter_gain_control_on_off_index, gain_control_callback);
    database_register(mic_limiter_gain_control_theshold_target_index, gain_control_callback);
    database_register(mic_limiter_gain_control_makeup_boost_suppress_index, gain_control_callback);
    database_register(mic_limiter_gain_control_attacktime_index, gain_control_callback);
    database_register(mic_limiter_gain_control_releasetime_index, gain_control_callback);
    database_register(mic_limiter_gain_control_compr_ratio_index, gain_control_callback);
    database_register(aux_limiter_gain_control_on_off_index, gain_control_callback);
    database_register(aux_limiter_gain_control_theshold_target_index, gain_control_callback);
    database_register(aux_limiter_gain_control_makeup_boost_suppress_index, gain_control_callback);
    database_register(aux_limiter_gain_control_attacktime_index, gain_control_callback);
    database_register(aux_limiter_gain_control_releasetime_index, gain_control_callback);
    database_register(aux_limiter_gain_control_compr_ratio_index, gain_control_callback);
    database_register(sp_limiter_gain_control_on_off_index, gain_control_callback);
    database_register(sp_limiter_gain_control_theshold_target_index, gain_control_callback);
    database_register(sp_limiter_gain_control_makeup_boost_suppress_index, gain_control_callback);
    database_register(sp_limiter_gain_control_attacktime_index, gain_control_callback);
    database_register(sp_limiter_gain_control_releasetime_index, gain_control_callback);
    database_register(sp_limiter_gain_control_compr_ratio_index, gain_control_callback);
    database_register(hp_limiter_gain_control_on_off_index, gain_control_callback);
    database_register(hp_limiter_gain_control_theshold_target_index, gain_control_callback);
    database_register(hp_limiter_gain_control_makeup_boost_suppress_index, gain_control_callback);
    database_register(hp_limiter_gain_control_attacktime_index, gain_control_callback);
    database_register(hp_limiter_gain_control_releasetime_index, gain_control_callback);
    database_register(hp_limiter_gain_control_compr_ratio_index, gain_control_callback);
    database_register(rec_limiter_gain_control_on_off_index, gain_control_callback);
    database_register(rec_limiter_gain_control_theshold_target_index, gain_control_callback);
    database_register(rec_limiter_gain_control_makeup_boost_suppress_index, gain_control_callback);
    database_register(rec_limiter_gain_control_attacktime_index, gain_control_callback);
    database_register(rec_limiter_gain_control_releasetime_index, gain_control_callback);
    database_register(rec_limiter_gain_control_compr_ratio_index, gain_control_callback);
    database_register(auto_ducker_gain_control_on_off_index, gain_control_callback);
    database_register(auto_ducker_gain_control_theshold_target_index, gain_control_callback);
    database_register(auto_ducker_gain_control_makeup_boost_suppress_index, gain_control_callback);
    database_register(auto_ducker_gain_control_attacktime_index, gain_control_callback);
    database_register(auto_ducker_gain_control_releasetime_index, gain_control_callback);
    database_register(auto_ducker_gain_control_compr_ratio_index, gain_control_callback);

    database_register(input_channel1_ducker_index, input_channel_ducker_callback);
    database_register(input_channel2_ducker_index, input_channel_ducker_callback);
    database_register(input_channel3_ducker_index, input_channel_ducker_callback);
    database_register(input_channel4_ducker_index, input_channel_ducker_callback);
    database_register(input_channel5_ducker_index, input_channel_ducker_callback);
    database_register(input_channel6_ducker_index, input_channel_ducker_callback);
    database_register(input_channel7_ducker_index, input_channel_ducker_callback);
    database_register(input_channel8_ducker_index, input_channel_ducker_callback);
    database_register(input_channel9_ducker_index, input_channel_ducker_callback);
    database_register(input_channel10_ducker_index, input_channel_ducker_callback);
    database_register(input_channel11_ducker_index, input_channel_ducker_callback);
    database_register(input_channel12_ducker_index, input_channel_ducker_callback);
    database_register(input_channel13_ducker_index, input_channel_ducker_callback);
    database_register(input_channel14_ducker_index, input_channel_ducker_callback);
    database_register(input_channel15_ducker_index, input_channel_ducker_callback);
    database_register(input_channel16_ducker_index, input_channel_ducker_callback);
    database_register(input_channel17_ducker_index, input_channel_ducker_callback);
    database_register(input_channel18_ducker_index, input_channel_ducker_callback);
    database_register(input_channel19_ducker_index, input_channel_ducker_callback);
    database_register(input_channel20_ducker_index, input_channel_ducker_callback);
    database_register(input_channel21_ducker_index, input_channel_ducker_callback);
    database_register(input_channel22_ducker_index, input_channel_ducker_callback);
    database_register(input_channel23_ducker_index, input_channel_ducker_callback);
    database_register(input_channel24_ducker_index, input_channel_ducker_callback);

    database_register(input_channel_vol1_index, input_channel_vol_callback);
    database_register(input_channel_vol2_index, input_channel_vol_callback);
    database_register(input_channel_vol3_index, input_channel_vol_callback);
    database_register(input_channel_vol4_index, input_channel_vol_callback);
    database_register(input_channel_vol5_index, input_channel_vol_callback);
    database_register(input_channel_vol6_index, input_channel_vol_callback);
    database_register(input_channel_vol7_index, input_channel_vol_callback);
    database_register(input_channel_vol8_index, input_channel_vol_callback);
    database_register(input_channel_vol9_index, input_channel_vol_callback);
    database_register(input_channel_vol10_index, input_channel_vol_callback);
    database_register(input_channel_vol11_index, input_channel_vol_callback);
    database_register(input_channel_vol12_index, input_channel_vol_callback);
    database_register(input_channel_vol13_index, input_channel_vol_callback);
    database_register(input_channel_vol14_index, input_channel_vol_callback);
    database_register(input_channel_vol15_index, input_channel_vol_callback);
    database_register(input_channel_vol16_index, input_channel_vol_callback);
    database_register(input_channel_vol17_index, input_channel_vol_callback);
    database_register(input_channel_vol18_index, input_channel_vol_callback);
    database_register(input_channel_vol19_index, input_channel_vol_callback);
    database_register(input_channel_vol20_index, input_channel_vol_callback);
    database_register(input_channel_vol21_index, input_channel_vol_callback);
    database_register(input_channel_vol22_index, input_channel_vol_callback);
    database_register(input_channel_vol23_index, input_channel_vol_callback);
    database_register(input_channel_vol24_index, input_channel_vol_callback);

    database_register(geq_mic_cnt_index, geq_callback);
    database_register(geq_mic_onoff_index, geq_callback);
    database_register(geq_mic_freq1_index, geq_callback);
    database_register(geq_mic_freq2_index, geq_callback);
    database_register(geq_mic_freq3_index, geq_callback);
    database_register(geq_mic_freq4_index, geq_callback);
    database_register(geq_mic_freq5_index, geq_callback);
    database_register(geq_mic_freq6_index, geq_callback);
    database_register(geq_mic_freq7_index, geq_callback);
    database_register(geq_mic_freq8_index, geq_callback);
    database_register(geq_mic_freq9_index, geq_callback);
    database_register(geq_mic_freq10_index, geq_callback);
    database_register(geq_mic_freq11_index, geq_callback);
    database_register(geq_mic_freq12_index, geq_callback);
    database_register(geq_mic_freq13_index, geq_callback);
    database_register(geq_mic_freq14_index, geq_callback);
    database_register(geq_mic_freq15_index, geq_callback);
    database_register(geq_mic_freq16_index, geq_callback);
    database_register(geq_mic_freq17_index, geq_callback);
    database_register(geq_mic_freq18_index, geq_callback);
    database_register(geq_mic_freq19_index, geq_callback);
    database_register(geq_mic_freq20_index, geq_callback);
    database_register(geq_mic_freq21_index, geq_callback);
    database_register(geq_mic_freq22_index, geq_callback);
    database_register(geq_mic_freq23_index, geq_callback);
    database_register(geq_mic_freq24_index, geq_callback);
    database_register(geq_mic_freq25_index, geq_callback);
    database_register(geq_mic_freq26_index, geq_callback);
    database_register(geq_mic_freq27_index, geq_callback);
    database_register(geq_mic_freq28_index, geq_callback);
    database_register(geq_mic_freq29_index, geq_callback);
    database_register(geq_mic_freq30_index, geq_callback);
    database_register(geq_mic_freq31_index, geq_callback);
    database_register(geq_mic_freq32_index, geq_callback);
    database_register(geq_mic_gain1_index, geq_callback);
    database_register(geq_mic_gain2_index, geq_callback);
    database_register(geq_mic_gain3_index, geq_callback);
    database_register(geq_mic_gain4_index, geq_callback);
    database_register(geq_mic_gain5_index, geq_callback);
    database_register(geq_mic_gain6_index, geq_callback);
    database_register(geq_mic_gain7_index, geq_callback);
    database_register(geq_mic_gain8_index, geq_callback);
    database_register(geq_mic_gain9_index, geq_callback);
    database_register(geq_mic_gain10_index, geq_callback);
    database_register(geq_mic_gain11_index, geq_callback);
    database_register(geq_mic_gain12_index, geq_callback);
    database_register(geq_mic_gain13_index, geq_callback);
    database_register(geq_mic_gain14_index, geq_callback);
    database_register(geq_mic_gain15_index, geq_callback);
    database_register(geq_mic_gain16_index, geq_callback);
    database_register(geq_mic_gain17_index, geq_callback);
    database_register(geq_mic_gain18_index, geq_callback);
    database_register(geq_mic_gain19_index, geq_callback);
    database_register(geq_mic_gain20_index, geq_callback);
    database_register(geq_mic_gain21_index, geq_callback);
    database_register(geq_mic_gain22_index, geq_callback);
    database_register(geq_mic_gain23_index, geq_callback);
    database_register(geq_mic_gain24_index, geq_callback);
    database_register(geq_mic_gain25_index, geq_callback);
    database_register(geq_mic_gain26_index, geq_callback);
    database_register(geq_mic_gain27_index, geq_callback);
    database_register(geq_mic_gain28_index, geq_callback);
    database_register(geq_mic_gain29_index, geq_callback);
    database_register(geq_mic_gain30_index, geq_callback);
    database_register(geq_mic_gain31_index, geq_callback);
    database_register(geq_mic_gain32_index, geq_callback);
    database_register(geq_aux_cnt_index, geq_callback);
    database_register(geq_aux_onoff_index, geq_callback);
    database_register(geq_aux_freq1_index, geq_callback);
    database_register(geq_aux_freq2_index, geq_callback);
    database_register(geq_aux_freq3_index, geq_callback);
    database_register(geq_aux_freq4_index, geq_callback);
    database_register(geq_aux_freq5_index, geq_callback);
    database_register(geq_aux_freq6_index, geq_callback);
    database_register(geq_aux_freq7_index, geq_callback);
    database_register(geq_aux_freq8_index, geq_callback);
    database_register(geq_aux_freq9_index, geq_callback);
    database_register(geq_aux_freq10_index, geq_callback);
    database_register(geq_aux_freq11_index, geq_callback);
    database_register(geq_aux_freq12_index, geq_callback);
    database_register(geq_aux_freq13_index, geq_callback);
    database_register(geq_aux_freq14_index, geq_callback);
    database_register(geq_aux_freq15_index, geq_callback);
    database_register(geq_aux_freq16_index, geq_callback);
    database_register(geq_aux_freq17_index, geq_callback);
    database_register(geq_aux_freq18_index, geq_callback);
    database_register(geq_aux_freq19_index, geq_callback);
    database_register(geq_aux_freq20_index, geq_callback);
    database_register(geq_aux_freq21_index, geq_callback);
    database_register(geq_aux_freq22_index, geq_callback);
    database_register(geq_aux_freq23_index, geq_callback);
    database_register(geq_aux_freq24_index, geq_callback);
    database_register(geq_aux_freq25_index, geq_callback);
    database_register(geq_aux_freq26_index, geq_callback);
    database_register(geq_aux_freq27_index, geq_callback);
    database_register(geq_aux_freq28_index, geq_callback);
    database_register(geq_aux_freq29_index, geq_callback);
    database_register(geq_aux_freq30_index, geq_callback);
    database_register(geq_aux_freq31_index, geq_callback);
    database_register(geq_aux_freq32_index, geq_callback);
    database_register(geq_aux_gain1_index, geq_callback);
    database_register(geq_aux_gain2_index, geq_callback);
    database_register(geq_aux_gain3_index, geq_callback);
    database_register(geq_aux_gain4_index, geq_callback);
    database_register(geq_aux_gain5_index, geq_callback);
    database_register(geq_aux_gain6_index, geq_callback);
    database_register(geq_aux_gain7_index, geq_callback);
    database_register(geq_aux_gain8_index, geq_callback);
    database_register(geq_aux_gain9_index, geq_callback);
    database_register(geq_aux_gain10_index, geq_callback);
    database_register(geq_aux_gain11_index, geq_callback);
    database_register(geq_aux_gain12_index, geq_callback);
    database_register(geq_aux_gain13_index, geq_callback);
    database_register(geq_aux_gain14_index, geq_callback);
    database_register(geq_aux_gain15_index, geq_callback);
    database_register(geq_aux_gain16_index, geq_callback);
    database_register(geq_aux_gain17_index, geq_callback);
    database_register(geq_aux_gain18_index, geq_callback);
    database_register(geq_aux_gain19_index, geq_callback);
    database_register(geq_aux_gain20_index, geq_callback);
    database_register(geq_aux_gain21_index, geq_callback);
    database_register(geq_aux_gain22_index, geq_callback);
    database_register(geq_aux_gain23_index, geq_callback);
    database_register(geq_aux_gain24_index, geq_callback);
    database_register(geq_aux_gain25_index, geq_callback);
    database_register(geq_aux_gain26_index, geq_callback);
    database_register(geq_aux_gain27_index, geq_callback);
    database_register(geq_aux_gain28_index, geq_callback);
    database_register(geq_aux_gain29_index, geq_callback);
    database_register(geq_aux_gain30_index, geq_callback);
    database_register(geq_aux_gain31_index, geq_callback);
    database_register(geq_aux_gain32_index, geq_callback);
    database_register(geq_spk_cnt_index, geq_callback);
    database_register(geq_spk_onoff_index, geq_callback);
    database_register(geq_spk_freq1_index, geq_callback);
    database_register(geq_spk_freq2_index, geq_callback);
    database_register(geq_spk_freq3_index, geq_callback);
    database_register(geq_spk_freq4_index, geq_callback);
    database_register(geq_spk_freq5_index, geq_callback);
    database_register(geq_spk_freq6_index, geq_callback);
    database_register(geq_spk_freq7_index, geq_callback);
    database_register(geq_spk_freq8_index, geq_callback);
    database_register(geq_spk_freq9_index, geq_callback);
    database_register(geq_spk_freq10_index, geq_callback);
    database_register(geq_spk_freq11_index, geq_callback);
    database_register(geq_spk_freq12_index, geq_callback);
    database_register(geq_spk_freq13_index, geq_callback);
    database_register(geq_spk_freq14_index, geq_callback);
    database_register(geq_spk_freq15_index, geq_callback);
    database_register(geq_spk_freq16_index, geq_callback);
    database_register(geq_spk_freq17_index, geq_callback);
    database_register(geq_spk_freq18_index, geq_callback);
    database_register(geq_spk_freq19_index, geq_callback);
    database_register(geq_spk_freq20_index, geq_callback);
    database_register(geq_spk_freq21_index, geq_callback);
    database_register(geq_spk_freq22_index, geq_callback);
    database_register(geq_spk_freq23_index, geq_callback);
    database_register(geq_spk_freq24_index, geq_callback);
    database_register(geq_spk_freq25_index, geq_callback);
    database_register(geq_spk_freq26_index, geq_callback);
    database_register(geq_spk_freq27_index, geq_callback);
    database_register(geq_spk_freq28_index, geq_callback);
    database_register(geq_spk_freq29_index, geq_callback);
    database_register(geq_spk_freq30_index, geq_callback);
    database_register(geq_spk_freq31_index, geq_callback);
    database_register(geq_spk_freq32_index, geq_callback);
    database_register(geq_spk_gain1_index, geq_callback);
    database_register(geq_spk_gain2_index, geq_callback);
    database_register(geq_spk_gain3_index, geq_callback);
    database_register(geq_spk_gain4_index, geq_callback);
    database_register(geq_spk_gain5_index, geq_callback);
    database_register(geq_spk_gain6_index, geq_callback);
    database_register(geq_spk_gain7_index, geq_callback);
    database_register(geq_spk_gain8_index, geq_callback);
    database_register(geq_spk_gain9_index, geq_callback);
    database_register(geq_spk_gain10_index, geq_callback);
    database_register(geq_spk_gain11_index, geq_callback);
    database_register(geq_spk_gain12_index, geq_callback);
    database_register(geq_spk_gain13_index, geq_callback);
    database_register(geq_spk_gain14_index, geq_callback);
    database_register(geq_spk_gain15_index, geq_callback);
    database_register(geq_spk_gain16_index, geq_callback);
    database_register(geq_spk_gain17_index, geq_callback);
    database_register(geq_spk_gain18_index, geq_callback);
    database_register(geq_spk_gain19_index, geq_callback);
    database_register(geq_spk_gain20_index, geq_callback);
    database_register(geq_spk_gain21_index, geq_callback);
    database_register(geq_spk_gain22_index, geq_callback);
    database_register(geq_spk_gain23_index, geq_callback);
    database_register(geq_spk_gain24_index, geq_callback);
    database_register(geq_spk_gain25_index, geq_callback);
    database_register(geq_spk_gain26_index, geq_callback);
    database_register(geq_spk_gain27_index, geq_callback);
    database_register(geq_spk_gain28_index, geq_callback);
    database_register(geq_spk_gain29_index, geq_callback);
    database_register(geq_spk_gain30_index, geq_callback);
    database_register(geq_spk_gain31_index, geq_callback);
    database_register(geq_spk_gain32_index, geq_callback);
    database_register(geq_hp_cnt_index, geq_callback);
    database_register(geq_hp_onoff_index, geq_callback);
    database_register(geq_hp_freq1_index, geq_callback);
    database_register(geq_hp_freq2_index, geq_callback);
    database_register(geq_hp_freq3_index, geq_callback);
    database_register(geq_hp_freq4_index, geq_callback);
    database_register(geq_hp_freq5_index, geq_callback);
    database_register(geq_hp_freq6_index, geq_callback);
    database_register(geq_hp_freq7_index, geq_callback);
    database_register(geq_hp_freq8_index, geq_callback);
    database_register(geq_hp_freq9_index, geq_callback);
    database_register(geq_hp_freq10_index, geq_callback);
    database_register(geq_hp_freq11_index, geq_callback);
    database_register(geq_hp_freq12_index, geq_callback);
    database_register(geq_hp_freq13_index, geq_callback);
    database_register(geq_hp_freq14_index, geq_callback);
    database_register(geq_hp_freq15_index, geq_callback);
    database_register(geq_hp_freq16_index, geq_callback);
    database_register(geq_hp_freq17_index, geq_callback);
    database_register(geq_hp_freq18_index, geq_callback);
    database_register(geq_hp_freq19_index, geq_callback);
    database_register(geq_hp_freq20_index, geq_callback);
    database_register(geq_hp_freq21_index, geq_callback);
    database_register(geq_hp_freq22_index, geq_callback);
    database_register(geq_hp_freq23_index, geq_callback);
    database_register(geq_hp_freq24_index, geq_callback);
    database_register(geq_hp_freq25_index, geq_callback);
    database_register(geq_hp_freq26_index, geq_callback);
    database_register(geq_hp_freq27_index, geq_callback);
    database_register(geq_hp_freq28_index, geq_callback);
    database_register(geq_hp_freq29_index, geq_callback);
    database_register(geq_hp_freq30_index, geq_callback);
    database_register(geq_hp_freq31_index, geq_callback);
    database_register(geq_hp_freq32_index, geq_callback);
    database_register(geq_hp_gain1_index, geq_callback);
    database_register(geq_hp_gain2_index, geq_callback);
    database_register(geq_hp_gain3_index, geq_callback);
    database_register(geq_hp_gain4_index, geq_callback);
    database_register(geq_hp_gain5_index, geq_callback);
    database_register(geq_hp_gain6_index, geq_callback);
    database_register(geq_hp_gain7_index, geq_callback);
    database_register(geq_hp_gain8_index, geq_callback);
    database_register(geq_hp_gain9_index, geq_callback);
    database_register(geq_hp_gain10_index, geq_callback);
    database_register(geq_hp_gain11_index, geq_callback);
    database_register(geq_hp_gain12_index, geq_callback);
    database_register(geq_hp_gain13_index, geq_callback);
    database_register(geq_hp_gain14_index, geq_callback);
    database_register(geq_hp_gain15_index, geq_callback);
    database_register(geq_hp_gain16_index, geq_callback);
    database_register(geq_hp_gain17_index, geq_callback);
    database_register(geq_hp_gain18_index, geq_callback);
    database_register(geq_hp_gain19_index, geq_callback);
    database_register(geq_hp_gain20_index, geq_callback);
    database_register(geq_hp_gain21_index, geq_callback);
    database_register(geq_hp_gain22_index, geq_callback);
    database_register(geq_hp_gain23_index, geq_callback);
    database_register(geq_hp_gain24_index, geq_callback);
    database_register(geq_hp_gain25_index, geq_callback);
    database_register(geq_hp_gain26_index, geq_callback);
    database_register(geq_hp_gain27_index, geq_callback);
    database_register(geq_hp_gain28_index, geq_callback);
    database_register(geq_hp_gain29_index, geq_callback);
    database_register(geq_hp_gain30_index, geq_callback);
    database_register(geq_hp_gain31_index, geq_callback);
    database_register(geq_hp_gain32_index, geq_callback);
    database_register(geq_rec_cnt_index, geq_callback);
    database_register(geq_rec_onoff_index, geq_callback);
    database_register(geq_rec_freq1_index, geq_callback);
    database_register(geq_rec_freq2_index, geq_callback);
    database_register(geq_rec_freq3_index, geq_callback);
    database_register(geq_rec_freq4_index, geq_callback);
    database_register(geq_rec_freq5_index, geq_callback);
    database_register(geq_rec_freq6_index, geq_callback);
    database_register(geq_rec_freq7_index, geq_callback);
    database_register(geq_rec_freq8_index, geq_callback);
    database_register(geq_rec_freq9_index, geq_callback);
    database_register(geq_rec_freq10_index, geq_callback);
    database_register(geq_rec_freq11_index, geq_callback);
    database_register(geq_rec_freq12_index, geq_callback);
    database_register(geq_rec_freq13_index, geq_callback);
    database_register(geq_rec_freq14_index, geq_callback);
    database_register(geq_rec_freq15_index, geq_callback);
    database_register(geq_rec_freq16_index, geq_callback);
    database_register(geq_rec_freq17_index, geq_callback);
    database_register(geq_rec_freq18_index, geq_callback);
    database_register(geq_rec_freq19_index, geq_callback);
    database_register(geq_rec_freq20_index, geq_callback);
    database_register(geq_rec_freq21_index, geq_callback);
    database_register(geq_rec_freq22_index, geq_callback);
    database_register(geq_rec_freq23_index, geq_callback);
    database_register(geq_rec_freq24_index, geq_callback);
    database_register(geq_rec_freq25_index, geq_callback);
    database_register(geq_rec_freq26_index, geq_callback);
    database_register(geq_rec_freq27_index, geq_callback);
    database_register(geq_rec_freq28_index, geq_callback);
    database_register(geq_rec_freq29_index, geq_callback);
    database_register(geq_rec_freq30_index, geq_callback);
    database_register(geq_rec_freq31_index, geq_callback);
    database_register(geq_rec_freq32_index, geq_callback);
    database_register(geq_rec_gain1_index, geq_callback);
    database_register(geq_rec_gain2_index, geq_callback);
    database_register(geq_rec_gain3_index, geq_callback);
    database_register(geq_rec_gain4_index, geq_callback);
    database_register(geq_rec_gain5_index, geq_callback);
    database_register(geq_rec_gain6_index, geq_callback);
    database_register(geq_rec_gain7_index, geq_callback);
    database_register(geq_rec_gain8_index, geq_callback);
    database_register(geq_rec_gain9_index, geq_callback);
    database_register(geq_rec_gain10_index, geq_callback);
    database_register(geq_rec_gain11_index, geq_callback);
    database_register(geq_rec_gain12_index, geq_callback);
    database_register(geq_rec_gain13_index, geq_callback);
    database_register(geq_rec_gain14_index, geq_callback);
    database_register(geq_rec_gain15_index, geq_callback);
    database_register(geq_rec_gain16_index, geq_callback);
    database_register(geq_rec_gain17_index, geq_callback);
    database_register(geq_rec_gain18_index, geq_callback);
    database_register(geq_rec_gain19_index, geq_callback);
    database_register(geq_rec_gain20_index, geq_callback);
    database_register(geq_rec_gain21_index, geq_callback);
    database_register(geq_rec_gain22_index, geq_callback);
    database_register(geq_rec_gain23_index, geq_callback);
    database_register(geq_rec_gain24_index, geq_callback);
    database_register(geq_rec_gain25_index, geq_callback);
    database_register(geq_rec_gain26_index, geq_callback);
    database_register(geq_rec_gain27_index, geq_callback);
    database_register(geq_rec_gain28_index, geq_callback);
    database_register(geq_rec_gain29_index, geq_callback);
    database_register(geq_rec_gain30_index, geq_callback);
    database_register(geq_rec_gain31_index, geq_callback);
    database_register(geq_rec_gain32_index, geq_callback);

    database_register(peq_mic_onoff_index, peq_callback);
    database_register(peq_mic_cnt_index, peq_callback);
    database_register(peq_mic_freq1_index, peq_callback);
    database_register(peq_mic_freq2_index, peq_callback);
    database_register(peq_mic_freq3_index, peq_callback);
    database_register(peq_mic_freq4_index, peq_callback);
    database_register(peq_mic_freq5_index, peq_callback);
    database_register(peq_mic_freq6_index, peq_callback);
    database_register(peq_mic_freq7_index, peq_callback);
    database_register(peq_mic_freq8_index, peq_callback);
    database_register(peq_mic_gain1_index, peq_callback);
    database_register(peq_mic_gain2_index, peq_callback);
    database_register(peq_mic_gain3_index, peq_callback);
    database_register(peq_mic_gain4_index, peq_callback);
    database_register(peq_mic_gain5_index, peq_callback);
    database_register(peq_mic_gain6_index, peq_callback);
    database_register(peq_mic_gain7_index, peq_callback);
    database_register(peq_mic_gain8_index, peq_callback);
    database_register(peq_mic_filter1_index, peq_callback);
    database_register(peq_mic_filter2_index, peq_callback);
    database_register(peq_mic_filter3_index, peq_callback);
    database_register(peq_mic_filter4_index, peq_callback);
    database_register(peq_mic_filter5_index, peq_callback);
    database_register(peq_mic_filter6_index, peq_callback);
    database_register(peq_mic_filter7_index, peq_callback);
    database_register(peq_mic_filter8_index, peq_callback);
    database_register(peq_mic_qval1_index, peq_callback);
    database_register(peq_mic_qval2_index, peq_callback);
    database_register(peq_mic_qval3_index, peq_callback);
    database_register(peq_mic_qval4_index, peq_callback);
    database_register(peq_mic_qval5_index, peq_callback);
    database_register(peq_mic_qval6_index, peq_callback);
    database_register(peq_mic_qval7_index, peq_callback);
    database_register(peq_mic_qval8_index, peq_callback);
    database_register(peq_aux_onoff_index, peq_callback);
    database_register(peq_aux_cnt_index, peq_callback);
    database_register(peq_aux_freq1_index, peq_callback);
    database_register(peq_aux_freq2_index, peq_callback);
    database_register(peq_aux_freq3_index, peq_callback);
    database_register(peq_aux_freq4_index, peq_callback);
    database_register(peq_aux_freq5_index, peq_callback);
    database_register(peq_aux_freq6_index, peq_callback);
    database_register(peq_aux_freq7_index, peq_callback);
    database_register(peq_aux_freq8_index, peq_callback);
    database_register(peq_aux_gain1_index, peq_callback);
    database_register(peq_aux_gain2_index, peq_callback);
    database_register(peq_aux_gain3_index, peq_callback);
    database_register(peq_aux_gain4_index, peq_callback);
    database_register(peq_aux_gain5_index, peq_callback);
    database_register(peq_aux_gain6_index, peq_callback);
    database_register(peq_aux_gain7_index, peq_callback);
    database_register(peq_aux_gain8_index, peq_callback);
    database_register(peq_aux_filter1_index, peq_callback);
    database_register(peq_aux_filter2_index, peq_callback);
    database_register(peq_aux_filter3_index, peq_callback);
    database_register(peq_aux_filter4_index, peq_callback);
    database_register(peq_aux_filter5_index, peq_callback);
    database_register(peq_aux_filter6_index, peq_callback);
    database_register(peq_aux_filter7_index, peq_callback);
    database_register(peq_aux_filter8_index, peq_callback);
    database_register(peq_aux_qval1_index, peq_callback);
    database_register(peq_aux_qval2_index, peq_callback);
    database_register(peq_aux_qval3_index, peq_callback);
    database_register(peq_aux_qval4_index, peq_callback);
    database_register(peq_aux_qval5_index, peq_callback);
    database_register(peq_aux_qval6_index, peq_callback);
    database_register(peq_aux_qval7_index, peq_callback);
    database_register(peq_aux_qval8_index, peq_callback);
    database_register(peq_spk_onoff_index, peq_callback);
    database_register(peq_spk_cnt_index, peq_callback);
    database_register(peq_spk_freq1_index, peq_callback);
    database_register(peq_spk_freq2_index, peq_callback);
    database_register(peq_spk_freq3_index, peq_callback);
    database_register(peq_spk_freq4_index, peq_callback);
    database_register(peq_spk_freq5_index, peq_callback);
    database_register(peq_spk_freq6_index, peq_callback);
    database_register(peq_spk_freq7_index, peq_callback);
    database_register(peq_spk_freq8_index, peq_callback);
    database_register(peq_spk_gain1_index, peq_callback);
    database_register(peq_spk_gain2_index, peq_callback);
    database_register(peq_spk_gain3_index, peq_callback);
    database_register(peq_spk_gain4_index, peq_callback);
    database_register(peq_spk_gain5_index, peq_callback);
    database_register(peq_spk_gain6_index, peq_callback);
    database_register(peq_spk_gain7_index, peq_callback);
    database_register(peq_spk_gain8_index, peq_callback);
    database_register(peq_spk_filter1_index, peq_callback);
    database_register(peq_spk_filter2_index, peq_callback);
    database_register(peq_spk_filter3_index, peq_callback);
    database_register(peq_spk_filter4_index, peq_callback);
    database_register(peq_spk_filter5_index, peq_callback);
    database_register(peq_spk_filter6_index, peq_callback);
    database_register(peq_spk_filter7_index, peq_callback);
    database_register(peq_spk_filter8_index, peq_callback);
    database_register(peq_spk_qval1_index, peq_callback);
    database_register(peq_spk_qval2_index, peq_callback);
    database_register(peq_spk_qval3_index, peq_callback);
    database_register(peq_spk_qval4_index, peq_callback);
    database_register(peq_spk_qval5_index, peq_callback);
    database_register(peq_spk_qval6_index, peq_callback);
    database_register(peq_spk_qval7_index, peq_callback);
    database_register(peq_spk_qval8_index, peq_callback);
    database_register(peq_hp_onoff_index, peq_callback);
    database_register(peq_hp_cnt_index, peq_callback);
    database_register(peq_hp_freq1_index, peq_callback);
    database_register(peq_hp_freq2_index, peq_callback);
    database_register(peq_hp_freq3_index, peq_callback);
    database_register(peq_hp_freq4_index, peq_callback);
    database_register(peq_hp_freq5_index, peq_callback);
    database_register(peq_hp_freq6_index, peq_callback);
    database_register(peq_hp_freq7_index, peq_callback);
    database_register(peq_hp_freq8_index, peq_callback);
    database_register(peq_hp_gain1_index, peq_callback);
    database_register(peq_hp_gain2_index, peq_callback);
    database_register(peq_hp_gain3_index, peq_callback);
    database_register(peq_hp_gain4_index, peq_callback);
    database_register(peq_hp_gain5_index, peq_callback);
    database_register(peq_hp_gain6_index, peq_callback);
    database_register(peq_hp_gain7_index, peq_callback);
    database_register(peq_hp_gain8_index, peq_callback);
    database_register(peq_hp_filter1_index, peq_callback);
    database_register(peq_hp_filter2_index, peq_callback);
    database_register(peq_hp_filter3_index, peq_callback);
    database_register(peq_hp_filter4_index, peq_callback);
    database_register(peq_hp_filter5_index, peq_callback);
    database_register(peq_hp_filter6_index, peq_callback);
    database_register(peq_hp_filter7_index, peq_callback);
    database_register(peq_hp_filter8_index, peq_callback);
    database_register(peq_hp_qval1_index, peq_callback);
    database_register(peq_hp_qval2_index, peq_callback);
    database_register(peq_hp_qval3_index, peq_callback);
    database_register(peq_hp_qval4_index, peq_callback);
    database_register(peq_hp_qval5_index, peq_callback);
    database_register(peq_hp_qval6_index, peq_callback);
    database_register(peq_hp_qval7_index, peq_callback);
    database_register(peq_hp_qval8_index, peq_callback);
    database_register(peq_rec_onoff_index, peq_callback);
    database_register(peq_rec_cnt_index, peq_callback);
    database_register(peq_rec_freq1_index, peq_callback);
    database_register(peq_rec_freq2_index, peq_callback);
    database_register(peq_rec_freq3_index, peq_callback);
    database_register(peq_rec_freq4_index, peq_callback);
    database_register(peq_rec_freq5_index, peq_callback);
    database_register(peq_rec_freq6_index, peq_callback);
    database_register(peq_rec_freq7_index, peq_callback);
    database_register(peq_rec_freq8_index, peq_callback);
    database_register(peq_rec_gain1_index, peq_callback);
    database_register(peq_rec_gain2_index, peq_callback);
    database_register(peq_rec_gain3_index, peq_callback);
    database_register(peq_rec_gain4_index, peq_callback);
    database_register(peq_rec_gain5_index, peq_callback);
    database_register(peq_rec_gain6_index, peq_callback);
    database_register(peq_rec_gain7_index, peq_callback);
    database_register(peq_rec_gain8_index, peq_callback);
    database_register(peq_rec_filter1_index, peq_callback);
    database_register(peq_rec_filter2_index, peq_callback);
    database_register(peq_rec_filter3_index, peq_callback);
    database_register(peq_rec_filter4_index, peq_callback);
    database_register(peq_rec_filter5_index, peq_callback);
    database_register(peq_rec_filter6_index, peq_callback);
    database_register(peq_rec_filter7_index, peq_callback);
    database_register(peq_rec_filter8_index, peq_callback);
    database_register(peq_rec_qval1_index, peq_callback);
    database_register(peq_rec_qval2_index, peq_callback);
    database_register(peq_rec_qval3_index, peq_callback);
    database_register(peq_rec_qval4_index, peq_callback);
    database_register(peq_rec_qval5_index, peq_callback);
    database_register(peq_rec_qval6_index, peq_callback);
    database_register(peq_rec_qval7_index, peq_callback);
    database_register(peq_rec_qval8_index, peq_callback);

    database_register(mic_aec1_auto_mixer_gain_control_on_off_index, aec_atm_gain_control_callback);
    database_register(mic_aec1_auto_mixer_gain_control_theshold_target_index, aec_atm_gain_control_callback);
    database_register(mic_aec1_auto_mixer_gain_control_makeup_boost_suppress_index, aec_atm_gain_control_callback);
    database_register(mic_aec1_auto_mixer_gain_control_attacktime_index, aec_atm_gain_control_callback);
    database_register(mic_aec1_auto_mixer_gain_control_releasetime_index, aec_atm_gain_control_callback);
    database_register(mic_aec1_auto_mixer_gain_control_compr_ratio_index, aec_atm_gain_control_callback);
    database_register(mic_aec0_auto_mixer_gain_control_on_off_index, aec_atm_gain_control_callback);
    database_register(mic_aec0_auto_mixer_gain_control_theshold_target_index, aec_atm_gain_control_callback);
    database_register(mic_aec0_auto_mixer_gain_control_makeup_boost_suppress_index, aec_atm_gain_control_callback);
    database_register(mic_aec0_auto_mixer_gain_control_attacktime_index, aec_atm_gain_control_callback);
    database_register(mic_aec0_auto_mixer_gain_control_releasetime_index, aec_atm_gain_control_callback);
    database_register(mic_aec0_auto_mixer_gain_control_compr_ratio_index, aec_atm_gain_control_callback);

    database_register(input_channel1_mute_index, input_channel_mute_callback);
    database_register(input_channel2_mute_index, input_channel_mute_callback);
    database_register(input_channel3_mute_index, input_channel_mute_callback);
    database_register(input_channel4_mute_index, input_channel_mute_callback);
    database_register(input_channel5_mute_index, input_channel_mute_callback);
    database_register(input_channel6_mute_index, input_channel_mute_callback);
    database_register(input_channel7_mute_index, input_channel_mute_callback);
    database_register(input_channel8_mute_index, input_channel_mute_callback);
    database_register(input_channel9_mute_index, input_channel_mute_callback);
    database_register(input_channel10_mute_index, input_channel_mute_callback);
    database_register(input_channel11_mute_index, input_channel_mute_callback);
    database_register(input_channel12_mute_index, input_channel_mute_callback);
    database_register(input_channel13_mute_index, input_channel_mute_callback);
    database_register(input_channel14_mute_index, input_channel_mute_callback);
    database_register(input_channel15_mute_index, input_channel_mute_callback);
    database_register(input_channel16_mute_index, input_channel_mute_callback);
    database_register(input_channel17_mute_index, input_channel_mute_callback);
    database_register(input_channel18_mute_index, input_channel_mute_callback);
    database_register(input_channel19_mute_index, input_channel_mute_callback);
    database_register(input_channel20_mute_index, input_channel_mute_callback);
    database_register(input_channel21_mute_index, input_channel_mute_callback);
    database_register(input_channel22_mute_index, input_channel_mute_callback);
    database_register(input_channel23_mute_index, input_channel_mute_callback);
    database_register(input_channel24_mute_index, input_channel_mute_callback);

    database_register(output_channel1_type_index, output_channel_type_callback);
    database_register(output_channel2_type_index, output_channel_type_callback);
    database_register(output_channel3_type_index, output_channel_type_callback);
    database_register(output_channel4_type_index, output_channel_type_callback);
    database_register(output_channel5_type_index, output_channel_type_callback);
    database_register(output_channel6_type_index, output_channel_type_callback);
    database_register(output_channel7_type_index, output_channel_type_callback);
    database_register(output_channel8_type_index, output_channel_type_callback);
    database_register(output_channel9_type_index, output_channel_type_callback);
    database_register(output_channel10_type_index, output_channel_type_callback);
    database_register(output_channel11_type_index, output_channel_type_callback);
    database_register(output_channel12_type_index, output_channel_type_callback);
    database_register(output_channel13_type_index, output_channel_type_callback);
    database_register(output_channel14_type_index, output_channel_type_callback);
    database_register(output_channel15_type_index, output_channel_type_callback);
    database_register(output_channel16_type_index, output_channel_type_callback);

    database_register(output_channel1_phase_invert_index, output_channel_phase_invert_callback);
    database_register(output_channel2_phase_invert_index, output_channel_phase_invert_callback);
    database_register(output_channel3_phase_invert_index, output_channel_phase_invert_callback);
    database_register(output_channel4_phase_invert_index, output_channel_phase_invert_callback);
    database_register(output_channel5_phase_invert_index, output_channel_phase_invert_callback);
    database_register(output_channel6_phase_invert_index, output_channel_phase_invert_callback);
    database_register(output_channel7_phase_invert_index, output_channel_phase_invert_callback);
    database_register(output_channel8_phase_invert_index, output_channel_phase_invert_callback);
    database_register(output_channel9_phase_invert_index, output_channel_phase_invert_callback);
    database_register(output_channel10_phase_invert_index, output_channel_phase_invert_callback);
    database_register(output_channel11_phase_invert_index, output_channel_phase_invert_callback);
    database_register(output_channel12_phase_invert_index, output_channel_phase_invert_callback);
    database_register(output_channel13_phase_invert_index, output_channel_phase_invert_callback);
    database_register(output_channel14_phase_invert_index, output_channel_phase_invert_callback);
    database_register(output_channel15_phase_invert_index, output_channel_phase_invert_callback);
    database_register(output_channel16_phase_invert_index, output_channel_phase_invert_callback);
}

static void mixer_lib_param_register(const int base)
{
    database_register(aec2_denoise_ms_index + base, global_aecref_aec2_denoise_callback);
    database_register(input_channel1_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel2_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel3_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel4_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel5_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel6_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel7_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel8_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel9_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel10_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel11_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel12_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel13_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel14_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel15_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel16_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel17_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel18_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel19_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel20_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel21_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel22_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel23_delay_ms_index + base, input_channel_delay_callback);
    database_register(input_channel24_delay_ms_index + base, input_channel_delay_callback);

    database_register(output_channel1_delay_ms_index + base, output_channel_delay_callback);
    database_register(output_channel2_delay_ms_index + base, output_channel_delay_callback);
    database_register(output_channel3_delay_ms_index + base, output_channel_delay_callback);
    database_register(output_channel4_delay_ms_index + base, output_channel_delay_callback);
    database_register(output_channel5_delay_ms_index + base, output_channel_delay_callback);
    database_register(output_channel6_delay_ms_index + base, output_channel_delay_callback);
    database_register(output_channel7_delay_ms_index + base, output_channel_delay_callback);
    database_register(output_channel8_delay_ms_index + base, output_channel_delay_callback);
    database_register(output_channel9_delay_ms_index + base, output_channel_delay_callback);
    database_register(output_channel10_delay_ms_index + base, output_channel_delay_callback);
    database_register(output_channel11_delay_ms_index + base, output_channel_delay_callback);
    database_register(output_channel12_delay_ms_index + base, output_channel_delay_callback);
    database_register(output_channel13_delay_ms_index + base, output_channel_delay_callback);
    database_register(output_channel14_delay_ms_index + base, output_channel_delay_callback);
    database_register(output_channel15_delay_ms_index + base, output_channel_delay_callback);
    database_register(output_channel16_delay_ms_index + base, output_channel_delay_callback);

    database_register(input_channel1_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel2_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel3_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel4_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel5_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel6_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel7_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel8_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel9_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel10_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel11_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel12_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel13_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel14_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel15_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel16_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel17_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel18_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel19_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel20_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel21_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel22_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel23_aec_mode_ms_index + base, input_channel_aec_mode_callback);
    database_register(input_channel24_aec_mode_ms_index + base, input_channel_aec_mode_callback);

    database_register(input_channel1_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel2_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel3_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel4_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel5_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel6_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel7_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel8_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel9_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel10_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel11_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel12_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel13_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel14_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel15_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel16_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel17_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel18_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel19_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel20_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel21_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel22_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel23_atm_mode_ms_index + base, input_channel_atm_callback);
    database_register(input_channel24_atm_mode_ms_index + base, input_channel_atm_callback);

    database_register(input_channel1_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel2_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel3_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel4_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel5_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel6_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel7_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel8_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel9_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel10_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel11_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel12_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel13_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel14_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel15_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel16_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel17_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel18_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel19_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel20_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel21_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel22_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel23_atm_decay_ms_index + base, input_channel_atm_decay_callback);
    database_register(input_channel24_atm_decay_ms_index + base, input_channel_atm_decay_callback);

    database_register(output_channel_vol1_ms_index + base, output_channel_vol_callback);
    database_register(output_channel_vol2_ms_index + base, output_channel_vol_callback);
    database_register(output_channel_vol3_ms_index + base, output_channel_vol_callback);
    database_register(output_channel_vol4_ms_index + base, output_channel_vol_callback);
    database_register(output_channel_vol5_ms_index + base, output_channel_vol_callback);
    database_register(output_channel_vol6_ms_index + base, output_channel_vol_callback);
    database_register(output_channel_vol7_ms_index + base, output_channel_vol_callback);
    database_register(output_channel_vol8_ms_index + base, output_channel_vol_callback);
    database_register(output_channel_vol9_ms_index + base, output_channel_vol_callback);
    database_register(output_channel_vol10_ms_index + base, output_channel_vol_callback);
    database_register(output_channel_vol11_ms_index + base, output_channel_vol_callback);
    database_register(output_channel_vol12_ms_index + base, output_channel_vol_callback);
    database_register(output_channel_vol13_ms_index + base, output_channel_vol_callback);
    database_register(output_channel_vol14_ms_index + base, output_channel_vol_callback);
    database_register(output_channel_vol15_ms_index + base, output_channel_vol_callback);
    database_register(output_channel_vol16_ms_index + base, output_channel_vol_callback);

    database_register(output_channel1_mute_ms_index + base, output_channel_mute_callback);
    database_register(output_channel2_mute_ms_index + base, output_channel_mute_callback);
    database_register(output_channel3_mute_ms_index + base, output_channel_mute_callback);
    database_register(output_channel4_mute_ms_index + base, output_channel_mute_callback);
    database_register(output_channel5_mute_ms_index + base, output_channel_mute_callback);
    database_register(output_channel6_mute_ms_index + base, output_channel_mute_callback);
    database_register(output_channel7_mute_ms_index + base, output_channel_mute_callback);
    database_register(output_channel8_mute_ms_index + base, output_channel_mute_callback);
    database_register(output_channel9_mute_ms_index + base, output_channel_mute_callback);
    database_register(output_channel10_mute_ms_index + base, output_channel_mute_callback);
    database_register(output_channel11_mute_ms_index + base, output_channel_mute_callback);
    database_register(output_channel12_mute_ms_index + base, output_channel_mute_callback);
    database_register(output_channel13_mute_ms_index + base, output_channel_mute_callback);
    database_register(output_channel14_mute_ms_index + base, output_channel_mute_callback);
    database_register(output_channel15_mute_ms_index + base, output_channel_mute_callback);
    database_register(output_channel16_mute_ms_index + base, output_channel_mute_callback);

    database_register(ochn1_ichn_mute_ms_index + base, matrix_mute_callback);
    database_register(ochn2_ichn_mute_ms_index + base, matrix_mute_callback);
    database_register(ochn3_ichn_mute_ms_index + base, matrix_mute_callback);
    database_register(ochn4_ichn_mute_ms_index + base, matrix_mute_callback);
    database_register(ochn5_ichn_mute_ms_index + base, matrix_mute_callback);
    database_register(ochn6_ichn_mute_ms_index + base, matrix_mute_callback);
    database_register(ochn7_ichn_mute_ms_index + base, matrix_mute_callback);
    database_register(ochn8_ichn_mute_ms_index + base, matrix_mute_callback);
    database_register(ochn9_ichn_mute_ms_index + base, matrix_mute_callback);
    database_register(ochn10_ichn_mute_ms_index + base, matrix_mute_callback);
    database_register(ochn11_ichn_mute_ms_index + base, matrix_mute_callback);
    database_register(ochn12_ichn_mute_ms_index + base, matrix_mute_callback);
    database_register(ochn13_ichn_mute_ms_index + base, matrix_mute_callback);
    database_register(ochn14_ichn_mute_ms_index + base, matrix_mute_callback);
    database_register(ochn15_ichn_mute_ms_index + base, matrix_mute_callback);
    database_register(ochn16_ichn_mute_ms_index + base, matrix_mute_callback);

    database_register(ochn1_ichn1_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn2_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn3_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn4_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn5_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn6_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn7_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn8_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn9_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn10_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn11_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn12_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn13_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn14_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn15_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn16_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn17_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn18_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn19_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn20_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn21_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn22_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn23_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn1_ichn24_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn1_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn2_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn3_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn4_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn5_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn6_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn7_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn8_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn9_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn10_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn11_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn12_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn13_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn14_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn15_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn16_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn17_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn18_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn19_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn20_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn21_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn22_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn23_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn2_ichn24_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn1_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn2_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn3_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn4_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn5_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn6_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn7_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn8_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn9_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn10_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn11_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn12_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn13_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn14_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn15_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn16_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn17_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn18_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn19_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn20_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn21_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn22_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn23_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn3_ichn24_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn1_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn2_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn3_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn4_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn5_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn6_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn7_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn8_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn9_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn10_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn11_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn12_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn13_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn14_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn15_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn16_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn17_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn18_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn19_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn20_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn21_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn22_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn23_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn4_ichn24_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn1_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn2_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn3_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn4_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn5_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn6_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn7_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn8_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn9_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn10_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn11_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn12_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn13_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn14_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn15_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn16_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn17_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn18_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn19_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn20_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn21_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn22_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn23_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn5_ichn24_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn1_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn2_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn3_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn4_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn5_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn6_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn7_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn8_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn9_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn10_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn11_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn12_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn13_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn14_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn15_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn16_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn17_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn18_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn19_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn20_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn21_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn22_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn23_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn6_ichn24_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn1_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn2_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn3_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn4_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn5_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn6_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn7_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn8_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn9_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn10_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn11_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn12_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn13_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn14_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn15_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn16_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn17_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn18_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn19_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn20_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn21_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn22_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn23_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn7_ichn24_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn1_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn2_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn3_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn4_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn5_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn6_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn7_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn8_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn9_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn10_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn11_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn12_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn13_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn14_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn15_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn16_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn17_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn18_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn19_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn20_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn21_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn22_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn23_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn8_ichn24_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn1_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn2_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn3_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn4_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn5_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn6_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn7_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn8_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn9_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn10_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn11_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn12_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn13_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn14_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn15_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn16_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn17_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn18_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn19_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn20_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn21_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn22_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn23_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn9_ichn24_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn1_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn2_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn3_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn4_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn5_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn6_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn7_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn8_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn9_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn10_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn11_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn12_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn13_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn14_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn15_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn16_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn17_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn18_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn19_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn20_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn21_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn22_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn23_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn10_ichn24_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn1_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn2_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn3_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn4_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn5_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn6_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn7_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn8_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn9_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn10_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn11_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn12_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn13_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn14_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn15_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn16_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn17_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn18_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn19_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn20_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn21_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn22_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn23_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn11_ichn24_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn1_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn2_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn3_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn4_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn5_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn6_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn7_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn8_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn9_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn10_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn11_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn12_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn13_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn14_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn15_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn16_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn17_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn18_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn19_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn20_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn21_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn22_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn23_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn12_ichn24_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn1_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn2_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn3_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn4_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn5_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn6_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn7_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn8_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn9_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn10_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn11_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn12_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn13_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn14_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn15_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn16_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn17_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn18_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn19_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn20_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn21_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn22_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn23_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn13_ichn24_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn1_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn2_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn3_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn4_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn5_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn6_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn7_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn8_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn9_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn10_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn11_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn12_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn13_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn14_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn15_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn16_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn17_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn18_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn19_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn20_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn21_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn22_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn23_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn14_ichn24_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn1_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn2_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn3_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn4_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn5_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn6_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn7_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn8_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn9_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn10_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn11_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn12_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn13_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn14_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn15_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn16_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn17_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn18_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn19_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn20_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn21_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn22_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn23_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn15_ichn24_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn1_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn2_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn3_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn4_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn5_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn6_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn7_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn8_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn9_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn10_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn11_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn12_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn13_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn14_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn15_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn16_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn17_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn18_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn19_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn20_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn21_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn22_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn23_vol_ms_index + base, matrix_vol_callback);
    database_register(ochn16_ichn24_vol_ms_index + base, matrix_vol_callback);

    database_register(mic_ans_mode_ms_index + base, mic_ans_mode_callback);
    database_register(dereverb_level_ms_index + base, dereverb_level_callback);

    database_register(test_sig_type_ms_index + base, test_sig_callback);
    database_register(test_sig_freq_ms_index + base, test_sig_callback);
    database_register(test_sig_amp_ms_index + base, test_sig_callback);

    database_register(howling_enable_ms_index + base, howling_enable_callback);
    database_register(mic_aec_mode_ms_index + base, mic_aec_mode_callback);
    database_register(aec_mic_num_ms_index + base, aec_mic_num_callback);
    database_register(aec_spk_num_ms_index + base, aec_spk_num_callback);
    database_register(anf_filter_num_ms_index + base, anf_filter_num_callback);
    database_register(anf_filter_dep_ms_index + base, anf_filter_dep_callback);
    database_register(aec_comfor_noise_ms_index + base, aec_comfor_noise_callback);
    database_register(aec_dwmx_mode_ms_index + base, aec_dwmx_mode_callback);
    database_register(afc_path_mode_ms_index + base, afc_path_mode_callback);
    database_register(afc_freq_shift_ms_index + base, afc_freq_shift_callback);
    database_register(aec_noise_level_ms_index + base, aec_noise_level_callback);
    database_register(aec_0_energy_ms_index + base, aec_0_energy_callback);
    database_register(aec_1_energy_ms_index + base, aec_1_energy_callback);
    database_register(aec0_delay_ms_index + base, aec_delay_callback);
    database_register(aec0_length_ms_index + base, aec_delay_callback);
    database_register(aec1_delay_ms_index + base, aec_delay_callback);
    database_register(aec1_length_ms_index + base, aec_delay_callback);
    database_register(aec_bkg_decay_ms_index + base, aec_bkg_decay_callback);
    database_register(aec_bypass_ms_index + base, aec_bypass_callback);
    database_register(low_shelf_freq_ms_index + base, low_shelf_callback);
    database_register(low_shelf_gain_ms_index + base, low_shelf_callback);
    database_register(low_shelf_qval_ms_index + base, low_shelf_callback);
    database_register(aec_highshelf_level_ms_index + base, low_shelf_callback);

    database_register(peq_anf_0_onoff_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_cnt_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_freq1_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_freq2_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_freq3_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_freq4_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_freq5_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_freq6_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_freq7_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_freq8_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_gain1_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_gain2_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_gain3_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_gain4_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_gain5_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_gain6_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_gain7_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_gain8_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_filter1_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_filter2_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_filter3_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_filter4_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_filter5_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_filter6_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_filter7_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_filter8_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_qval1_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_qval2_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_qval3_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_qval4_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_qval5_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_qval6_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_qval7_ms_index + base, peq_ms_callback);
    database_register(peq_anf_0_qval8_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_onoff_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_cnt_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_freq1_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_freq2_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_freq3_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_freq4_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_freq5_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_freq6_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_freq7_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_freq8_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_gain1_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_gain2_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_gain3_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_gain4_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_gain5_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_gain6_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_gain7_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_gain8_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_filter1_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_filter2_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_filter3_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_filter4_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_filter5_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_filter6_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_filter7_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_filter8_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_qval1_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_qval2_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_qval3_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_qval4_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_qval5_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_qval6_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_qval7_ms_index + base, peq_ms_callback);
    database_register(peq_anf_1_qval8_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_onoff_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_cnt_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_freq1_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_freq2_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_freq3_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_freq4_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_freq5_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_freq6_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_freq7_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_freq8_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_gain1_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_gain2_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_gain3_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_gain4_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_gain5_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_gain6_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_gain7_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_gain8_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_filter1_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_filter2_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_filter3_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_filter4_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_filter5_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_filter6_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_filter7_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_filter8_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_qval1_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_qval2_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_qval3_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_qval4_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_qval5_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_qval6_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_qval7_ms_index + base, peq_ms_callback);
    database_register(peq_anf_2_qval8_ms_index + base, peq_ms_callback);

    database_register(peq_aec_1_onoff_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_cnt_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_freq1_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_freq2_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_freq3_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_freq4_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_freq5_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_freq6_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_freq7_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_freq8_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_gain1_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_gain2_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_gain3_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_gain4_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_gain5_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_gain6_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_gain7_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_gain8_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_filter1_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_filter2_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_filter3_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_filter4_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_filter5_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_filter6_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_filter7_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_filter8_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_qval1_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_qval2_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_qval3_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_qval4_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_qval5_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_qval6_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_qval7_ms_index + base, peq_ms_callback);
    database_register(peq_aec_1_qval8_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_onoff_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_cnt_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_freq1_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_freq2_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_freq3_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_freq4_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_freq5_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_freq6_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_freq7_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_freq8_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_gain1_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_gain2_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_gain3_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_gain4_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_gain5_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_gain6_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_gain7_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_gain8_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_filter1_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_filter2_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_filter3_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_filter4_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_filter5_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_filter6_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_filter7_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_filter8_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_qval1_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_qval2_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_qval3_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_qval4_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_qval5_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_qval6_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_qval7_ms_index + base, peq_ms_callback);
    database_register(peq_aec_2_qval8_ms_index + base, peq_ms_callback);
}

void master_mixer_lib_param_default(void)
{
    database_value tmp;
    mixer_lib_param_default(MIXER1_BASE_INDEX);
    mixer_lib_peq_ms_init(MIXER1_BASE_INDEX);

    // 主上从输入通道默认类型为AEC混合
    tmp.i32 = YM_MATRIX_INPUT_AEC_MIX;
    database_write(input_channel22_aec_mode_ms_index + MIXER1_BASE_INDEX, tmp);
}

void slave_mixer_lib_param_default(void)
{
    mixer_lib_param_default(MIXER2_BASE_INDEX);
    mixer_lib_peq_ms_init(MIXER2_BASE_INDEX);
    // 从 mic_agc_gain_control_makeup_boost_suppress_ms_index 15
    // tmp.i32 = 15 * 10;
    // database_write(mic_agc_gain_control_makeup_boost_suppress_ms_index + MIXER2_BASE_INDEX, tmp);
}

__weak void mode1_master_param_default(void) {}

__weak void mode1_slave_param_default(void) {}

/* default tune mode name */
#ifdef BD_MIXER_YL_6MIC
static const char tune_mode_default_name[MIXER_PARAM_MODE_NAME_MAX][MIXER_PARAM_MODE_NAME_LEN] = {
    {0x53, 0xcc, 0x5e, 0x08, 0x7c, 0xfb, 0x7e, 0xdf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    {0x54, 0x0a, 0x9e, 0xa6, 0x62, 0x69, 0x58, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    {0x7e, 0xbf, 0x96, 0x35, 0x62, 0x69, 0x58, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    {0x6a, 0x21, 0x5f, 0x0f, 0x56, 0xdb, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    {0x6a, 0x21, 0x5f, 0x0f, 0x4e, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    {0x6a, 0x21, 0x5f, 0x0f, 0x51, 0x6d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    {0x6a, 0x21, 0x5f, 0x0f, 0x4e, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    {0x6a, 0x21, 0x5f, 0x0f, 0x51, 0x6b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
};
#else
static const char tune_mode_default_name[MIXER_PARAM_MODE_NAME_MAX][MIXER_PARAM_MODE_NAME_LEN] = {
    {0x53, 0xcc, 0x5e, 0x08, 0x7c, 0xfb, 0x7e, 0xdf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    {0x53, 0xcc, 0x5e, 0x08, 0x67, 0x2c, 0x57, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    {0x6a, 0x21, 0x5f, 0x0f, 0x4e, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    {0x6a, 0x21, 0x5f, 0x0f, 0x56, 0xdb, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    {0x6a, 0x21, 0x5f, 0x0f, 0x4e, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    {0x6a, 0x21, 0x5f, 0x0f, 0x51, 0x6d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    {0x6a, 0x21, 0x5f, 0x0f, 0x4e, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    {0x6a, 0x21, 0x5f, 0x0f, 0x51, 0x6b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
};
#endif

/* default manufacturer name */
static const char touch_screen_default_name[MIXER_PARAM_SCREEN_NAME_LEN] = {
    0x00, 0x20, 0x00, 0x20, 0x00, 0x20, 0x00, 0x20, 0x00, 0x20, 0x00, 0x20,
    0x00, 0x20, 0x00, 0x20, 0x00, 0x20, 0x00, 0x20, 0x00, 0x20, 0x00, 0x20,
};

/* default usb audio name */
static const char usb_audio_default_name[MIXER_PARAM_SCREEN_NAME_LEN] = {
#if defined(BD_MIXER_YO_8MIC)
    "ClassIn Audio\0"
#elif defined(BD_MIXER_WX_105) || defined(BD_MIXER_WX_107) || defined(BD_MIXER_WX_107_PA) || defined(BD_MIXER_WX_113) || defined(BD_MIXER_WX_128)
    "wx_audio\0"
#else
    "USB Audio\0"
#endif
};

__weak void mode2_master_param_default(void)
{
}
__weak void mode2_slave_param_default(void)
{
}
__weak void platform_mode2_lib_param_default(void)
{
}

static int mixer_lib_param_init(void)
{
    FILE *cfg;
    int index = 0;
    char cfg_name[20] = {0};
    database_value tmp;
    int is_mode_err = 1;
    int ret = 0;
    // TODO:优化参数初始化
    mixer_lib_ms_comm_param_register();
    mixer_lib_param_register(MIXER1_BASE_INDEX);
    mixer_lib_param_register(MIXER2_BASE_INDEX);
#ifdef BEAM_ENABLE
    beam_param_register();
#endif /* BEAM_ENABLE */

    platform_mixer_lib_ms_comm_param_register();
    platform_mixer_lib_param_register(MIXER1_BASE_INDEX);
    platform_mixer_lib_param_register(MIXER2_BASE_INDEX);
    if (is_mode_err)
    {
        gconfig_ctrl.database_version = DATABASE_VERSION;
        if (access("/appcfg/mode-index", 0) != 0) /* not exist */
        {
            gconfig_ctrl.mode = 0;
            gconfig_ctrl.tune_mode_loop_max = 2;
            mode_index_set(gconfig_ctrl.mode);
        }
        else
        {
            log_i("current tune mode is %d", mode_index_get());
        }

        if (access(NONVOLATILE_GLOBAL_CFG_PATH, 0) != 0) /* not exist */
        {
            for (int i = 0; YM_MIXER_MAX_MODE > i; i++)
            {
                memcpy(nonvolatile_global_cfg.mode_name[i], tune_mode_default_name[i], MIXER_PARAM_MODE_NAME_LEN);
            }
            memcpy(nonvolatile_global_cfg.touch_screen_manu_name, touch_screen_default_name, MIXER_PARAM_SCREEN_NAME_LEN);
            memcpy(nonvolatile_global_cfg.touch_screen_school_name, touch_screen_default_name, MIXER_PARAM_SCREEN_NAME_LEN);
            memcpy(nonvolatile_global_cfg.usb_audio_class_name, usb_audio_default_name, MIXER_PARAM_SCREEN_NAME_LEN);
            nonvolatile_global_cfg_save();
        }
        else
        {
            nonvolatile_global_cfg_read();
        }

        // 读取/appcfg/目录下所有的数字开头文件，检测所有保存的参数与当前代码中的数据库版本是否一致，如果不一致则删除
        DIR *dir;
        struct dirent *entry;
        char cfg_dir[20] = "/appcfg/";
        dir = opendir(cfg_dir);
        int param_fd;
        if (dir != NULL)
        {
            while ((entry = readdir(dir)) != NULL)
            {
                if (isdigit(entry->d_name[0]))
                {
                    char cfg_file[30] = {0};
                    int need_rm = 0;
                    snprintf(cfg_file, 29, "%s%.10s", cfg_dir, entry->d_name);
                    param_fd = open(cfg_file, O_RDWR);
                    if (param_fd != -1)
                    {
                        lseek(param_fd, 0, SEEK_SET);
                        ssize_t bytesRead = read(param_fd, &tmp, sizeof(database_value));
                        if (bytesRead == sizeof(database_value))
                        {
                            if (tmp.u32 != DATABASE_VERSION)
                            {
                                log_e("database version mismatch");
                                need_rm = 1;
                            }
                        }
                        else
                        {
                            log_e("Error reading from file");
                        }
                        close(param_fd);
                    }
                    else
                    {
                        // Error opening file
                        log_w("Error opening file");
                    }
                    if (need_rm)
                    {
                        remove(cfg_file);
                    }
                }
            }
            closedir(dir);
            ret = system("sync;sync");
        }
    }

    cfg = fopen("/appcfg/curr-mode", "rb");
    if (cfg)
    {
        fseek(cfg, 0, SEEK_SET);
        if (fread(&tmp, sizeof(database_value), 1, cfg) != 1)
        {
            fclose(cfg);
            ret = system("rm /appcfg/curr-mode");
            goto default_param;
        }
        if (tmp.u32 != DATABASE_VERSION)
        {
            log_e("database version mismatch");
            fclose(cfg);
            ret = system("rm /appcfg/curr-mode");
            goto default_param;
        }
        database_write(database_version_index, tmp);
        index = 1;
        while (fread(&tmp, sizeof(database_value), 1, cfg) == 1)
        {
            if (database_write(index++, tmp))
            {
                log_e("database write fault,index: %d", index - 1);
                break;
            }
        }
        fclose(cfg);

        /* force the howling mode to 0 */
        tmp.i32 = YM_MIXER_HOWLING_DETECTION_OFF;
        database_write(howling_enable_ms_index + MIXER1_BASE_INDEX, tmp);
        database_write(howling_enable_ms_index + MIXER2_BASE_INDEX, tmp);
    }
    else
    {
        void platform_lib_param_default(void);
        void platform_mode1_lib_param_default(void);

        log_w("can't open curr-mode");
    default_param:
        index = DATABASE_INDEX_MAX;
        database_write(database_version_index, (database_value){.u32 = DATABASE_VERSION});
        mixer_lib_ms_comm_param_default();
        mixer_lib_geq_init();
        mixer_lib_peq_init();
        master_mixer_lib_param_default();
        slave_mixer_lib_param_default();

        // 平台参数初始化
        platform_lib_param_default();
#ifdef BEAM_ENABLE
        // beam_lib_param_default();
#endif /* BEAM_ENABLE */
        // 初始化混音矩阵
        mastre_mixer_matrix_param_default();
        slave_mixer_matrix_param_default();

        platform_mastre_mixer_matrix_param_default();
        platform_slave_mixer_matrix_param_default();

        // 所有项目都需要的，根据通道类型自动配置的一些参数
        mixer_lib_auto_param_init();

        platform_mixer_lib_auto_param_init();

        if (store_param())
        {
            log_e("store_param err.");
            return -1;
        }

        if (access("/appcfg/0", 0) != 0) /* not exist */
        {
            if (store_mode())
                log_e("save tune mode %d failed", gconfig_ctrl.mode);
        }

        if (access("/appcfg/1", 0) != 0) /* not exist */
        {
            // 临存模式0参数，并设置模式0与模式1不同的一些参数
            database_take_shot();
            mode1_master_param_default();
            mode1_slave_param_default();
            platform_mode1_lib_param_default();
            gconfig_ctrl.mode = 1;

            if (store_mode())
                log_e("save tune mode %d failed", gconfig_ctrl.mode);

            // 恢复模式0参数
            gconfig_ctrl.mode = 0;
            database_restore_frome_shot();
        }

        if (access("/appcfg/2", 0) != 0) /* not exist */
        {
            database_take_shot();
            mode2_master_param_default();
            mode2_slave_param_default();
            platform_mode2_lib_param_default();
            gconfig_ctrl.mode = 2;

            if (store_mode())
                log_e("save tune mode %d failed", gconfig_ctrl.mode);

            // 恢复模式0参数
            gconfig_ctrl.mode = 0;
            database_restore_frome_shot();
        }

        database_take_shot();
        for (int j = 2; YM_MIXER_MAX_MODE > j; j++)
        {
            sprintf(cfg_name, "/appcfg/%d", j);
            if (access(cfg_name, 0) != 0) /* not exist */
            {
                gconfig_ctrl.mode = j;
                if (store_mode())
                {
                    log_e("save tune mode %d failed", gconfig_ctrl.mode);
                    break;
                }
            }
        }
        gconfig_ctrl.mode = 0;
        mode_index_set(gconfig_ctrl.mode);
        database_restore_frome_shot();
    }
#if defined(BD_MIXER_LINE_MICS)
    database_write(a2b_slave_nodes_index, (database_value){.i32 = 0});
#endif
    ret = system("sync;sync");

    // 读取参数配置文件，如果不存在使用默认参数初始化并创建配置文件

    log_i("param init successful,count: %d", index);

    return ret;
}
INIT_COMPONENT_EXPORT(mixer_lib_param_init);

static int mixer_lib_param_app_init(void)
{
    database_write(global_tune_mode_vol_index, (database_value){.i32 = gconfig_ctrl.mode});

    return 0;
}
INIT_APP_EXPORT(mixer_lib_param_app_init);

void mixer_params_restore_default(void)
{
    extern void platform_lib_param_default(void);
    extern void platform_mode1_lib_param_default(void);

    mixer_lib_ms_comm_param_default();
    mixer_lib_geq_init();
    mixer_lib_peq_init();
    master_mixer_lib_param_default();
    slave_mixer_lib_param_default();

    // 平台参数初始化
    platform_lib_param_default();
#ifdef BEAM_ENABLE
    beam_lib_param_default();
#endif /* BEAM_ENABLE */
    // 初始化混音矩阵
    mastre_mixer_matrix_param_default();
    slave_mixer_matrix_param_default();

    platform_mastre_mixer_matrix_param_default();
    platform_slave_mixer_matrix_param_default();

    if (store_param())
    {
        log_e("store_param err.");
        return;
    }

    // 临存模式0参数，并设置模式0与模式1不同的一些参数
    database_take_shot();
    mode1_master_param_default();
    mode1_slave_param_default();
    platform_mode1_lib_param_default();
    gconfig_ctrl.mode = 1;
    if (store_param())
        log_e("store_param err.");
    // 恢复模式0参数
    gconfig_ctrl.mode = 0;
    database_restore_frome_shot();

    /* flash mode 2 */
    database_take_shot();
    mode2_master_param_default();
    mode2_slave_param_default();
    platform_mode2_lib_param_default();
    gconfig_ctrl.mode = 2;
    if (store_param())
        log_e("store_param err.");
    gconfig_ctrl.mode = 0;
    database_restore_frome_shot();
}

int get_mic(int argc, char **argv)
{
    uint8_t *p_micIdx;
    uint32_t micNum;
    uint8_t *get_mic_input_index(uint32_t * micNum);
    p_micIdx = get_mic_input_index(&micNum);
    printf("mic num:%d\n", micNum);
    printf("mic idx:");
    for (size_t i = 0; i < micNum; i++)
    {
        printf("%d ", p_micIdx[i]);
    }
    printf("\n");
    return 0;
}
MSH_CMD_EXPORT(get_mic, get mic);

int get_spk(int argc, char **argv)
{
    uint8_t *p_spkIdx;
    uint32_t spkNum;
    p_spkIdx = mixer_get_speaker_output_index[LATENCY_5MS](ym_mixer_master_handle, &spkNum);
    printf("spk num:%d\n", spkNum);
    printf("spk idx:");
    for (size_t i = 0; i < spkNum; i++)
    {
        printf("%d ", p_spkIdx[i]);
    }
    printf("\n");
    return 0;
}
MSH_CMD_EXPORT(get_spk, get speaker);

static void audio_mixer_inchn_print(void)
{
    static const char chn_attr[YM_MATRIX_MAX_INPUT_CHANNEL_AEC_MODES][8] =
        {
            {"mic"},
            {"aec1"},
            {"aec2"},
            {"aref"},
            {"aux"},
            {"aecm"},
            {"fref"},
            {"nref"},
            {"allm"},
            {"afcm"},
            {"peq1"},
            {"peq2"},
            {"bkg1"},
            {"sref"}
        };

    /* input channel volume, mute */
    int i;
    int j;
    printf("inchn\t");
    for (i = 0; i < MIXER_INPUT_CHN_MAX; i++)
    {
        printf("%u\t", i);
    }

    printf("\nMattr\t");
    for (i = 0; i < MIXER_INPUT_CHN_MAX; i++)
    {
        j = database_get(input_channel1_aec_mode_ms_index + i + MIXER1_BASE_INDEX)->i32;
        printf("%s\t", chn_attr[j]);
    }

    printf("\nSattr\t");
    for (i = 0; i < MIXER_INPUT_CHN_MAX; i++)
    {
        j = database_get(input_channel1_aec_mode_ms_index + i + MIXER2_BASE_INDEX)->i32;
        printf("%s\t", chn_attr[j]);
    }

    printf("\nvol\t");
    for (i = 0; i < MIXER_INPUT_CHN_MAX; i++)
    {
        printf("%u\t", database_get(input_channel_vol1_index + i)->i32);
    }

    printf("\nmute\t");
    for (i = 0; i < MIXER_INPUT_CHN_MAX; i++)
    {
        printf("%u\t", database_get(input_channel1_mute_index + i)->i32);
    }

    printf("\nMmode\t");
    for (i = 0; i < MIXER_INPUT_CHN_MAX; i++)
    {
        printf("%x\t", database_get(input_channel1_aec_mode_ms_index + i + MIXER1_BASE_INDEX)->i32);
    }

    printf("\nSmode\t");
    for (i = 0; i < MIXER_INPUT_CHN_MAX; i++)
    {
        printf("%x\t", database_get(input_channel1_aec_mode_ms_index + i + MIXER2_BASE_INDEX)->i32);
    }

    printf("\nMatm\t");
    for (i = 0; i < MIXER_INPUT_CHN_MAX; i++)
    {
        printf("%d\t", database_get(input_channel1_atm_mode_ms_index + i + MIXER1_BASE_INDEX)->i32);
    }

    printf("\nSatm\t");
    for (i = 0; i < MIXER_INPUT_CHN_MAX; i++)
    {
        printf("%d\t", database_get(input_channel1_atm_mode_ms_index + i + MIXER2_BASE_INDEX)->i32);
    }

    printf("\n\n");
}

static void audio_mixer_outchn_print(void)
{
    static const char chn_attr[YM_MATRIX_MAX_OUTPUT_CHANNEL_TYPES][8] =
        {
            {"spk"},
            {"hp"},
            {"rec"},
            {"fout"},
            {"nout"},
            {"spcp"}
        };

    /* input channel volume, mute */
    int i;
    int j;
    printf("outchn\t");
    for (i = 0; i < MIXER_OUTPUT_CHN_MAX; i++)
    {
        printf("%u\t", i);
    }

    printf("\nMattr\t");
    for (i = 0; i < MIXER_OUTPUT_CHN_MAX; i++)
    {
        j = database_get(output_channel1_type_index + i)->i32;
        printf("%s\t", chn_attr[j]);
    }
    printf("\nSattr\t");
    for (i = 0; i < MIXER_OUTPUT_CHN_MAX; i++)
    {
        j = database_get(output_channel1_type_index + i)->i32;
        printf("%s\t", chn_attr[j]);
    }

    printf("\nMvol\t");
    for (i = 0; i < MIXER_OUTPUT_CHN_MAX; i++)
    {
        printf("%u\t", database_get(output_channel_vol1_ms_index + i + MIXER1_BASE_INDEX)->i32);
    }
    printf("\nSvol\t");
    for (i = 0; i < MIXER_OUTPUT_CHN_MAX; i++)
    {
        printf("%u\t", database_get(output_channel_vol1_ms_index + i + MIXER2_BASE_INDEX)->i32);
    }

    printf("\nMmute\t");
    for (i = 0; i < MIXER_OUTPUT_CHN_MAX; i++)
    {
        printf("%u\t", database_get(output_channel1_mute_ms_index + i + MIXER1_BASE_INDEX)->i32);
    }
    printf("\nSmute\t");
    for (i = 0; i < MIXER_OUTPUT_CHN_MAX; i++)
    {
        printf("%u\t", database_get(output_channel1_mute_ms_index + i + MIXER2_BASE_INDEX)->i32);
    }

    printf("\nMmode\t");
    for (i = 0; i < MIXER_OUTPUT_CHN_MAX; i++)
    {
        printf("%x\t", database_get(output_channel1_type_index + i)->i32);
    }
    printf("\nSmode\t");
    for (i = 0; i < MIXER_OUTPUT_CHN_MAX; i++)
    {
        printf("%x\t", database_get(output_channel1_type_index + i)->i32);
    }

    printf("\n\n");
}

static void audio_mixer_aec_print(void)
{
    static const char formatter[] =
        {
            "AEC\tAEC0-M\tAEC1-M\tAEC0-S\tAEC1-S\ndelay\t%d\t%d\t%d\t%d\nlength\t%d\t%d\t%d\t%d\nenergy\t%d\t%d\n\n"};

    /* aec delay and length */
    printf(formatter,
           database_get(aec0_delay_ms_index + MIXER1_BASE_INDEX)->i32,
           database_get(aec1_delay_ms_index + MIXER1_BASE_INDEX)->i32,
           database_get(aec0_delay_ms_index + MIXER2_BASE_INDEX)->i32,
           database_get(aec1_delay_ms_index + MIXER2_BASE_INDEX)->i32,
           database_get(aec0_length_ms_index + MIXER1_BASE_INDEX)->i32,
           database_get(aec1_length_ms_index + MIXER1_BASE_INDEX)->i32,
           database_get(aec0_length_ms_index + MIXER2_BASE_INDEX)->i32,
           database_get(aec1_length_ms_index + MIXER2_BASE_INDEX)->i32,
           database_get(aec_0_energy_ms_index + MIXER1_BASE_INDEX)->i32,
           database_get(aec_1_energy_ms_index + MIXER1_BASE_INDEX)->i32);
}

static void audio_mixer_sys_print(void)
{
    static const char formatter[] =
        {
            "SYSTEM\tvol\tmute\tdelay\nmic\t%d\t%d\t%d\nout\t%d\t%d\t---\nmusic\t---\t---\t%d\nbgm\t---\t---\t%d\n\n"};

    /* aec delay and length */
    printf(formatter,
           database_get(mic_global_sys_vol_index)->i32,
           database_get(global_mic_mute_index)->i32,
           database_get(global_mic_delay_index)->i32,
           database_get(global_sys_out_vol_index)->i32,
           database_get(global_out_mute_index)->i32,
           database_get(global_aux_delay_index)->i32,
           database_get(global_bgm_delay_index)->i32);
}

static void audio_mixer_module_print(void)
{
    static const char module_param[] =
        {
            "\t%x\t%d\t%d\t%u\t%u\t%d\n"};

    printf("MODULE\ton/off\ttarget\tboot\tattack\trelease\tratio\n");

    /* noise gate */
    printf("micNG");
    printf(module_param,
           database_get(mic_noise_gate_gain_control_on_off_index)->i32,
           database_get(mic_noise_gate_gain_control_theshold_target_index)->i32,
           database_get(mic_noise_gate_gain_control_makeup_boost_suppress_index)->i32,
           database_get(mic_noise_gate_gain_control_attacktime_index)->i32,
           database_get(mic_noise_gate_gain_control_releasetime_index)->i32,
           database_get(mic_noise_gate_gain_control_compr_ratio_index)->i32);
    printf("auxNG");
    printf(module_param,
           database_get(aux_noise_gate_gain_control_on_off_index)->i32,
           database_get(aux_noise_gate_gain_control_theshold_target_index)->i32,
           database_get(aux_noise_gate_gain_control_makeup_boost_suppress_index)->i32,
           database_get(aux_noise_gate_gain_control_attacktime_index)->i32,
           database_get(aux_noise_gate_gain_control_releasetime_index)->i32,
           database_get(aux_noise_gate_gain_control_compr_ratio_index)->i32);

    /* DRC */
    printf("micDRC");
    printf(module_param,
           database_get(mic_drc_gain_control_on_off_index)->i32,
           database_get(mic_drc_gain_control_theshold_target_index)->i32,
           database_get(mic_drc_gain_control_makeup_boost_suppress_index)->i32,
           database_get(mic_drc_gain_control_attacktime_index)->i32,
           database_get(mic_drc_gain_control_releasetime_index)->i32,
           database_get(mic_drc_gain_control_compr_ratio_index)->i32);
    printf("auxDRC");
    printf(module_param,
           database_get(aux_drc_gain_control_on_off_index)->i32,
           database_get(aux_drc_gain_control_theshold_target_index)->i32,
           database_get(aux_drc_gain_control_makeup_boost_suppress_index)->i32,
           database_get(aux_drc_gain_control_attacktime_index)->i32,
           database_get(aux_drc_gain_control_releasetime_index)->i32,
           database_get(aux_drc_gain_control_compr_ratio_index)->i32);

    /* expander */
    printf("micExp");
    printf(module_param,
           database_get(mic_expander_gain_control_on_off_index)->i32,
           database_get(mic_expander_gain_control_theshold_target_index)->i32,
           database_get(mic_expander_gain_control_makeup_boost_suppress_index)->i32,
           database_get(mic_expander_gain_control_attacktime_index)->i32,
           database_get(mic_expander_gain_control_releasetime_index)->i32,
           database_get(mic_expander_gain_control_compr_ratio_index)->i32);
    printf("auxExp");
    printf(module_param,
           database_get(aux_expander_gain_control_on_off_index)->i32,
           database_get(aux_expander_gain_control_theshold_target_index)->i32,
           database_get(aux_expander_gain_control_makeup_boost_suppress_index)->i32,
           database_get(aux_expander_gain_control_attacktime_index)->i32,
           database_get(aux_expander_gain_control_releasetime_index)->i32,
           database_get(aux_expander_gain_control_compr_ratio_index)->i32);

    /* AGC */
    printf("micAGC");
    printf(module_param,
           database_get(mic_agc_gain_control_on_off_index)->i32,
           database_get(mic_agc_gain_control_theshold_target_index)->i32,
           database_get(mic_agc_gain_control_makeup_boost_suppress_index)->i32,
           database_get(mic_agc_gain_control_attacktime_index)->i32,
           database_get(mic_agc_gain_control_releasetime_index)->i32,
           database_get(mic_agc_gain_control_compr_ratio_index)->i32);
    printf("auxAGC");
    printf(module_param,
           database_get(aux_agc_gain_control_on_off_index)->i32,
           database_get(aux_agc_gain_control_theshold_target_index)->i32,
           database_get(aux_agc_gain_control_makeup_boost_suppress_index)->i32,
           database_get(aux_agc_gain_control_attacktime_index)->i32,
           database_get(aux_agc_gain_control_releasetime_index)->i32,
           database_get(aux_agc_gain_control_compr_ratio_index)->i32);

    /* limiter */
    printf("micLmt");
    printf(module_param,
           database_get(mic_limiter_gain_control_on_off_index)->i32,
           database_get(mic_limiter_gain_control_theshold_target_index)->i32,
           database_get(mic_limiter_gain_control_makeup_boost_suppress_index)->i32,
           database_get(mic_limiter_gain_control_attacktime_index)->i32,
           database_get(mic_limiter_gain_control_releasetime_index)->i32,
           database_get(mic_limiter_gain_control_compr_ratio_index)->i32);
    printf("auxLmt");
    printf(module_param,
           database_get(aux_limiter_gain_control_on_off_index)->i32,
           database_get(aux_limiter_gain_control_theshold_target_index)->i32,
           database_get(aux_limiter_gain_control_makeup_boost_suppress_index)->i32,
           database_get(aux_limiter_gain_control_attacktime_index)->i32,
           database_get(aux_limiter_gain_control_releasetime_index)->i32,
           database_get(aux_limiter_gain_control_compr_ratio_index)->i32);
    printf("spkLmt");
    printf(module_param,
           database_get(sp_limiter_gain_control_on_off_index)->i32,
           database_get(sp_limiter_gain_control_theshold_target_index)->i32,
           database_get(sp_limiter_gain_control_makeup_boost_suppress_index)->i32,
           database_get(sp_limiter_gain_control_attacktime_index)->i32,
           database_get(sp_limiter_gain_control_releasetime_index)->i32,
           database_get(sp_limiter_gain_control_compr_ratio_index)->i32);
    printf("hpLmt");
    printf(module_param,
           database_get(hp_limiter_gain_control_on_off_index)->i32,
           database_get(hp_limiter_gain_control_theshold_target_index)->i32,
           database_get(hp_limiter_gain_control_makeup_boost_suppress_index)->i32,
           database_get(hp_limiter_gain_control_attacktime_index)->i32,
           database_get(hp_limiter_gain_control_releasetime_index)->i32,
           database_get(hp_limiter_gain_control_compr_ratio_index)->i32);
    printf("recLmt");
    printf(module_param,
           database_get(rec_limiter_gain_control_on_off_index)->i32,
           database_get(rec_limiter_gain_control_theshold_target_index)->i32,
           database_get(rec_limiter_gain_control_makeup_boost_suppress_index)->i32,
           database_get(rec_limiter_gain_control_attacktime_index)->i32,
           database_get(rec_limiter_gain_control_releasetime_index)->i32,
           database_get(rec_limiter_gain_control_compr_ratio_index)->i32);

    /* ducker */
    printf("ducker");
    printf(module_param,
           database_get(auto_ducker_gain_control_on_off_index)->i32,
           database_get(auto_ducker_gain_control_theshold_target_index)->i32,
           database_get(auto_ducker_gain_control_makeup_boost_suppress_index)->i32,
           database_get(auto_ducker_gain_control_attacktime_index)->i32,
           database_get(auto_ducker_gain_control_releasetime_index)->i32,
           database_get(auto_ducker_gain_control_compr_ratio_index)->i32);

    /* auto-mixer */
    printf("aec0ATM");
    printf(module_param,
           database_get(mic_aec0_auto_mixer_gain_control_on_off_index)->i32,
           database_get(mic_aec0_auto_mixer_gain_control_theshold_target_index)->i32,
           database_get(mic_aec0_auto_mixer_gain_control_makeup_boost_suppress_index)->i32,
           database_get(mic_aec0_auto_mixer_gain_control_attacktime_index)->i32,
           database_get(mic_aec0_auto_mixer_gain_control_releasetime_index)->i32,
           database_get(mic_aec0_auto_mixer_gain_control_compr_ratio_index)->i32);
    printf("aec1ATM");
    printf(module_param,
           database_get(mic_aec1_auto_mixer_gain_control_on_off_index)->i32,
           database_get(mic_aec1_auto_mixer_gain_control_theshold_target_index)->i32,
           database_get(mic_aec1_auto_mixer_gain_control_makeup_boost_suppress_index)->i32,
           database_get(mic_aec1_auto_mixer_gain_control_attacktime_index)->i32,
           database_get(mic_aec1_auto_mixer_gain_control_releasetime_index)->i32,
           database_get(mic_aec1_auto_mixer_gain_control_compr_ratio_index)->i32);

    printf("\n");
}

static void audio_mixer_mode_print(void)
{
    printf("\ttune\tmicNS\tmicAEC\tCmfNs\tderevb\tHVoc\thowling\n");
    printf("MCore\t%u\t%u\t%u\t%u\t%u\t%u\t%u\n",
           gconfig_ctrl.mode,
           database_get(mic_ans_mode_ms_index + MIXER1_BASE_INDEX)->i32,
           database_get(mic_aec_mode_ms_index + MIXER1_BASE_INDEX)->i32,
           database_get(aec_comfor_noise_ms_index + MIXER1_BASE_INDEX)->i32,
           database_get(dereverb_level_ms_index + MIXER1_BASE_INDEX)->i32,
           0,
           database_get(howling_enable_ms_index + MIXER1_BASE_INDEX)->i32);
    printf("SCore\t%u\t%u\t%u\t%u\t%u\t%u\t%u\n\n",
           gconfig_ctrl.mode,
           database_get(mic_ans_mode_ms_index + MIXER2_BASE_INDEX)->i32,
           database_get(mic_aec_mode_ms_index + MIXER2_BASE_INDEX)->i32,
           database_get(aec_comfor_noise_ms_index + MIXER2_BASE_INDEX)->i32,
           database_get(dereverb_level_ms_index + MIXER2_BASE_INDEX)->i32,
           0,
           database_get(howling_enable_ms_index + MIXER2_BASE_INDEX)->i32);
}

static void audio_mixer_matrix_print(void)
{
    int i, j, v;

    printf("MATRIX");
    for (i = 0; i < MIXER_INPUT_CHN_MAX; i++)
    {
        printf("\tin%u", i);
    }
    printf("\tAEC\tAFC");
    printf("\n");

    for (j = 0; j < MIXER_OUTPUT_CHN_MAX; j++)
    {
        printf("\033[32mMout%u\033[32m", j);
        v = ochn1_ichn1_vol_ms_index + ochn1_ichn1_vol_unit_count * j + MIXER1_BASE_INDEX;
        for (i = 0; i < MIXER_INPUT_CHN_MAX; i++)
        {
            printf("\t\033[32m%d/%x\033[32m", database_get(v + i)->i32, (database_get(ochn1_ichn_mute_ms_index + j + MIXER1_BASE_INDEX)->i32 >> i) & 1);
        }
        printf("\t\033[32m%d/%x\033[32m",
               database_get(v + YM_MATRIX_INPUT_AEC_OUTPUT_CHANNEL)->i32,
               (database_get(ochn1_ichn_mute_ms_index + j + MIXER1_BASE_INDEX)->i32 >> YM_MATRIX_INPUT_AEC_OUTPUT_CHANNEL) & 1);
        printf("\t\033[32m%d/%x\033[32m",
               database_get(v + YM_MATRIX_INPUT_AFC_OUTPUT_CHANNEL)->i32,
               (database_get(ochn1_ichn_mute_ms_index + j + MIXER1_BASE_INDEX)->i32 >> YM_MATRIX_INPUT_AFC_OUTPUT_CHANNEL) & 1);
        printf("\033[37m\n");
    }

    for (j = 0; j < MIXER_OUTPUT_CHN_MAX; j++)
    {
        printf("\033[35mSout%u\033[35m", j);
        v = ochn1_ichn1_vol_ms_index + ochn1_ichn1_vol_unit_count * j + MIXER2_BASE_INDEX;
        for (i = 0; i < MIXER_INPUT_CHN_MAX; i++)
        {
            printf("\t\033[35m%d/%x\033[35m", database_get(v + i)->i32, (database_get(ochn1_ichn_mute_ms_index + j + MIXER2_BASE_INDEX)->i32 >> i) & 1);
        }
        printf("\t\033[35m%d/%x\033[35m",
               database_get(v + YM_MATRIX_INPUT_AEC_OUTPUT_CHANNEL)->i32,
               (database_get(ochn1_ichn_mute_ms_index + j + MIXER2_BASE_INDEX)->i32 >> YM_MATRIX_INPUT_AEC_OUTPUT_CHANNEL) & 1);
        printf("\t\033[35m%d/%x\033[35m",
               database_get(v + YM_MATRIX_INPUT_AFC_OUTPUT_CHANNEL)->i32,
               (database_get(ochn1_ichn_mute_ms_index + j + MIXER2_BASE_INDEX)->i32 >> YM_MATRIX_INPUT_AFC_OUTPUT_CHANNEL) & 1);
        printf("\033[37m\n");
    }
    printf("\n");
}

static void audio_mixer_peq_print(void)
{
    int32_t i = 0;
    int32_t j = 0;
    int32_t index = 0;

    for (i = 0; i < 22; i++)
    {
        index = 0;
        switch (i)
        {
        case YM_MIXER_MIC_IN:
            index = peq_mic_onoff_index;
            printf("PEQ-MIC=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case YM_MIXER_MUSIC_IN:
            index = peq_aux_onoff_index;
            printf("PEQ-AUX=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case YM_MIXER_SP_OUT:
            index = peq_spk_onoff_index;
            printf("PEQ-SPK=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case YM_MIXER_HP_OUT:
            index = peq_hp_onoff_index;
            printf("PEQ-HP=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case YM_MIXER_REC_OUT:
            index = peq_rec_onoff_index;
            printf("PEQ-REC=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case YM_MIXER_MIC_HOWLING_SUPPR0:
            index = peq_anf_0_onoff_ms_index + MIXER1_BASE_INDEX;
            printf("M-PEQ-AFC0=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case YM_MIXER_MIC_HOWLING_SUPPR1:
            index = peq_anf_1_onoff_ms_index + MIXER1_BASE_INDEX;
            printf("M-PEQ-AFC1=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case YM_MIXER_MIC_HOWLING_SUPPR2:
            index = peq_anf_2_onoff_ms_index + MIXER1_BASE_INDEX;
            printf("M-PEQ-AFC2=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case 8:
            index = peq_aec_1_onoff_ms_index + MIXER1_BASE_INDEX;
            printf("M-PEQ-AEC1=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case 9:
            index = peq_aec_2_onoff_ms_index + MIXER1_BASE_INDEX;
            printf("M-PEQ-AEC2=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case 10:
            index = peq_anf_0_onoff_ms_index + MIXER2_BASE_INDEX;
            printf("S-PEQ-AFC0=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case 11:
            index = peq_anf_1_onoff_ms_index + MIXER2_BASE_INDEX;
            printf("S-PEQ-AFC1=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case 12:
            index = peq_anf_2_onoff_ms_index + MIXER2_BASE_INDEX;
            printf("S-PEQ-AFC2=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case 13:
            index = peq_aec_1_onoff_ms_index + MIXER2_BASE_INDEX;
            printf("S-PEQ-AEC1=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case 14:
            index = peq_aec_2_onoff_ms_index + MIXER2_BASE_INDEX;
            printf("S-PEQ-AEC2=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
#ifdef BEAM_ENABLE
        case 15:
            index = peq_beamf_onoff_index + BEAM_BASE_INDEX;
            printf("PEQ-BEAM=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case 16:
            if ((i - 15) >= BEAM_INSTANCE_NUMBER)
                break;
            index = peq_beamf_onoff_index + BEAM2_BASE_INDEX;
            printf("PEQ-BEAM2=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case 17:
            if ((i - 15) >= BEAM_INSTANCE_NUMBER)
                break;
            index = peq_beamf_onoff_index + BEAM3_BASE_INDEX;
            printf("PEQ-BEAM3=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case 18:
            if ((i - 15) >= BEAM_INSTANCE_NUMBER)
                break;
            index = peq_beamf_onoff_index + BEAM4_BASE_INDEX;
            printf("PEQ-BEAM4=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case 19:
            if ((i - 15) >= BEAM_INSTANCE_NUMBER)
                break;
            index = peq_beamf_onoff_index + BEAM5_BASE_INDEX;
            printf("PEQ-BEAM5=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case 20:
            if ((i - 15) >= BEAM_INSTANCE_NUMBER)
                break;
            index = peq_beamf_onoff_index + BEAM6_BASE_INDEX;
            printf("PEQ-BEAM6=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case 21:
            if ((i - 15) >= BEAM_INSTANCE_NUMBER)
                break;
            index = peq_beamf_onoff_index + BEAM7_BASE_INDEX;
            printf("PEQ-BEAM7=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
        case 22:
            if ((i - 15) >= BEAM_INSTANCE_NUMBER)
                break;
            index = peq_beamf_onoff_index + BEAM8_BASE_INDEX;
            printf("PEQ-BEAM8=%s\tcount=%u\n", (database_get(index)->i32 > 0) ? ("on") : ("off"), database_get(index + 1)->i32);
            break;
#endif /* BEAM_ENABLE */
        default:
            break;
        }

        if (index == 0)
            continue;

        printf("band");
        for (j = 0; j < MIXER_PARAM_PEQ_MAX; j++)
        {
            printf("\t%d", j);
        }

        printf("\nfreq");
        for (j = 0; j < MIXER_PARAM_PEQ_MAX; j++)
        {
            printf("\t%d", database_get(index + 2 + j)->i32);
        }

        printf("\ngain");
        for (j = 0; j < MIXER_PARAM_PEQ_MAX; j++)
        {
            printf("\t%d", database_get(index + 2 + 8 + j)->i32);
        }

        printf("\nfilter");
        for (j = 0; j < MIXER_PARAM_PEQ_MAX; j++)
        {
            printf("\t%d", database_get(index + 2 + 16 + j)->i32);
        }

        printf("\nqval");
        for (j = 0; j < MIXER_PARAM_PEQ_MAX; j++)
        {
            printf("\t%d", database_get(index + 2 + 24 + j)->i32);
        }
        printf("\n\n");
    }
}

int ls(int argc, char **argv)
{
#define PRINT_MASK_EQ (1 << 0)

    uint32_t bitmask = 0;

    if (argc >= 2)
    {
        if (memcmp(argv[1], "-a", 2) == 0)
        {
            bitmask = 0xffffffff;
        }
        else if (memcmp(argv[1], "-q", 2) == 0)
        {
            bitmask |= PRINT_MASK_EQ;
        }
    }

    audio_mixer_inchn_print();

    audio_mixer_outchn_print();

    audio_mixer_aec_print();

    audio_mixer_sys_print();

    audio_mixer_module_print();

    audio_mixer_mode_print();

    audio_mixer_matrix_print();

    if (bitmask & PRINT_MASK_EQ)
    {
        audio_mixer_peq_print();
    }

    return 0;
}
MSH_CMD_EXPORT(ls, list parameters);

// extern void mixer_printAmxChannelGain(ym_mixer_state *p_mixer, unsigned int idx);
// idx = 0: AEC1; 1: AEC2; 2: AEC_MIX; 3: AFC_MIX, 4: outChannel 0 etc
int amxprint(int argc, char* argv[])
{
	if((argc == 2)
		&& ((strcmp(argv[1],"0") == 0)||(strcmp(argv[1],"1") == 0)||(strcmp(argv[1],"2") == 0)||(strcmp(argv[1],"3") == 0)||(strcmp(argv[1],"4") == 0)))
	{
	    printf("\n----------- master core -------------------\n");
	    mixer_printAmxChannelGain[LATENCY_5MS](ym_mixer_master_handle, argv[1][0] - '0');
	    printf("\n----------- slave core -------------------\n");
	    mixer_printAmxChannelGain[LATENCY_10MS](ym_mixer_slave_handle, argv[1][0] - '0');
	}
	else
	{
	    printf("\nInvalid parameter\n");
	    printf("\namxprint 0/1/2/3/4\n");
	}
    return 0;
}
MSH_CMD_EXPORT(amxprint, print auto-mixer channel gain);

