/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-01-06 16:28:17
 * @LastEditTime: 2024-11-21 11:22:52
 * @LastEditors: Anzhichao
 * @Description: 音频流中间件，观察者模式，浅拷贝
 * @FilePath: /audio_mixer_rk3308/component/ym_stream.c
 * Copyright (c) 2006-2024, Yinkman Development Team
 */
#include <stdlib.h>
#include <assert.h>
#include <string.h>
#include <semaphore.h>

#include "ym_stream.h"
#include "utlist.h"
#include "ym_shell.h"
#include "ym_log.h"
#include "ym_mem_pool.h"

#define STREAM_POOL_SIZE (3)
#define STREAM_NAME_LEN (20)

struct yinkman_stream_elem
{
    struct yinkman_stream_elem *next, *prev;
    uint32_t mask; // must atomic op
    uint32_t used;
    void *data;
};

struct yinkman_stream_notify
{
    struct yinkman_stream_notify *next, *prev;
    yinkman_stream_notify_func notify_func;
    void *args;
};

struct yinkman_stream
{
    char name[STREAM_NAME_LEN];
    uint32_t package_size;
    uint32_t package_count;
    volatile uint32_t package_id;
    volatile int register_count;
    volatile uint32_t register_mask;
    struct yinkman_stream_elem *stream_data;
    struct yinkman_stream_notify *notifies;
    struct yinkman_stream_notify *pull_callback;
    pthread_mutex_t mutex;
    void *elem_pool;
    sem_t *notify_sem;
    struct yinkman_stream *next, *prev;
};

static struct yinkman_stream *all_stream;

struct yinkman_stream *yinkman_stream_create(
    const char *name,
    size_t frames,
    size_t frame_size,
    size_t block_count)
{
    struct yinkman_stream *stream = malloc(sizeof(struct yinkman_stream));
    if (!stream)
    {
        log_e("malloc err!");
        exit(0);
    }
    memset(stream, 0, sizeof(struct yinkman_stream));
    snprintf(stream->name, STREAM_NAME_LEN - 1, "%s", name);
    stream->package_size = frames * frame_size;
    pthread_mutex_init(&stream->mutex, NULL);
    DL_APPEND(all_stream, stream);
    stream->elem_pool = ym_mem_pool_init(block_count, sizeof(struct yinkman_stream_elem) + stream->package_size);
    uint8_t *pos;
    for (size_t i = 0; i < block_count; i++)
    {
        pos = ym_mem_pool_alloc(stream->elem_pool);
        ((struct yinkman_stream_elem *)pos)->data = pos + sizeof(struct yinkman_stream_elem);
    }
    ym_mem_pool_clear(stream->elem_pool);

    return stream;
}

struct yinkman_stream *yinkman_stream_with_sem_create(
    const char *name,
    size_t frames,
    size_t frame_size,
    size_t block_count)
{
    struct yinkman_stream *stream = yinkman_stream_create(name, frames, frame_size, block_count);
    if (stream)
    {
        sem_t *sem = malloc(sizeof(sem_t));
        if (sem_init(sem, 0, 0) == -1)
        {
            log_e("sem_init err!");
            exit(0);
        }
        stream->notify_sem = sem;
    }
    return stream;
}

void yinkman_stream_wait(struct yinkman_stream *stream)
{
    if (stream->notify_sem)
    {
        sem_wait(stream->notify_sem);
    }
}

void yinkman_stream_trywait(struct yinkman_stream *stream)
{
    if (stream->notify_sem)
    {
        while (sem_trywait(stream->notify_sem) == 0)
            ;
    }
}

uint32_t yinkman_stream_get_package_size(struct yinkman_stream *stream)
{
    return stream->package_size;
}

/**
 * @description: 销毁音频流有巨大的风险，除非明确风险不可能发生，否则不应该调用该API
 * @param {yinkman_stream} *stream
 * @return {none}
 * @use: 只有明确的知道消费者和生产者的关系，才能调用该API。
 */
void yinkman_stream_destroy(struct yinkman_stream *stream)
{
    pthread_mutex_lock(&stream->mutex);
    // clean all data
    ym_mem_pool_destroy(stream->elem_pool);
    // destroy mutex
    pthread_mutex_unlock(&stream->mutex);
    pthread_mutex_destroy(&stream->mutex);
    // destroy sem
    if (stream->notify_sem)
    {
        sem_destroy(stream->notify_sem);
        free(stream->notify_sem);
    }
    // destroy self
    DL_DELETE(all_stream, stream);
    free(stream);
}

const char *yinkman_stream_name(struct yinkman_stream *stream)
{
    return stream->name;
}

uint32_t yinkman_stream_register(struct yinkman_stream *stream, yinkman_stream_notify_func notify_callback, void *args)
{
    uint32_t res;
    if (stream->register_count >= 32)
    {
        log_e("too much register!");
        return -1;
    }

    struct yinkman_stream_notify *notify = malloc(sizeof(struct yinkman_stream_notify));
    if (!notify)
    {
        log_e("malloc err!");
        return -1;
    }
    notify->notify_func = notify_callback;
    notify->args = args;
    pthread_mutex_lock(&stream->mutex);
    DL_APPEND(stream->notifies, notify);
    res = (1 << stream->register_count++);
    stream->register_mask |= res;
    pthread_mutex_unlock(&stream->mutex);
    return res;
}

uint32_t yinkman_stream_register_no_data(struct yinkman_stream *stream,
                                         yinkman_stream_notify_func notify_callback,
                                         void *args)
{
    struct yinkman_stream_notify *notify = malloc(sizeof(struct yinkman_stream_notify));
    if (!notify)
    {
        log_e("malloc err!");
        return -1;
    }
    notify->notify_func = notify_callback;
    notify->args = args;
    pthread_mutex_lock(&stream->mutex);
    DL_APPEND(stream->notifies, notify);
    pthread_mutex_unlock(&stream->mutex);
    return 0;
}

void yinkman_stream_register_free_callback(struct yinkman_stream *stream,
                                           yinkman_stream_notify_func pull_callback,
                                           void *args)
{
    struct yinkman_stream_notify *notify = malloc(sizeof(struct yinkman_stream_notify));
    if (!notify)
    {
        log_e("malloc err!");
        return;
    }
    notify->notify_func = pull_callback;
    notify->args = args;
    pthread_mutex_lock(&stream->mutex);
    DL_APPEND(stream->pull_callback, notify);
    pthread_mutex_unlock(&stream->mutex);
    return;
}

void *yinkman_stream_malloc(struct yinkman_stream *stream)
{
    struct yinkman_stream_elem *elem;
    if (!stream)
        return NULL;
    pthread_mutex_lock(&stream->mutex);
    elem = ym_mem_pool_alloc(stream->elem_pool);
    pthread_mutex_unlock(&stream->mutex);
    if (elem)
    {
        return elem->data;
    }
    return NULL;
}

void *yinkman_stream_calloc(struct yinkman_stream *stream)
{
    uint8_t *data = yinkman_stream_malloc(stream);
    if (data)
        memset(data, 0, stream->package_size);
    return data;
}

void yinkman_stream_free(struct yinkman_stream *stream, void *data, uint32_t mask)
{
    pthread_mutex_lock(&stream->mutex);
    struct yinkman_stream_elem *elem = (struct yinkman_stream_elem *)((uint8_t *)data - sizeof(struct yinkman_stream_elem));
    elem->used &= ~mask;
    if ((elem->mask == 0) &&
        (elem->used == 0))
    {
        ym_mem_pool_free(stream->elem_pool, elem);

        if (stream->stream_data)
        {
            DL_DELETE(stream->stream_data, elem);
            stream->package_count--;
        }
        pthread_mutex_unlock(&stream->mutex);

        // pull successful callback
        struct yinkman_stream_notify *pos;
        DL_FOREACH(stream->pull_callback, pos)
        {
            pos->notify_func(pos->args);
        }
    }
    else
    {
        pthread_mutex_unlock(&stream->mutex);
    }
}

int yinkman_stream_push(struct yinkman_stream *stream, const void *data)
{
    int ret = -1;
    if (!data)
        return -2;
    struct yinkman_stream_elem *elem = (struct yinkman_stream_elem *)((uint8_t *)data - sizeof(struct yinkman_stream_elem));
    if (stream->register_count == 0)
        elem->mask = 0x01;
    else
        elem->mask = stream->register_mask;
    elem->used = 0;
    pthread_mutex_lock(&stream->mutex);
    DL_APPEND(stream->stream_data, elem);
    pthread_mutex_unlock(&stream->mutex);
    ret = 0;
    if (stream->notify_sem)
    {
        sem_post(stream->notify_sem);
    }
    // notify callback
    struct yinkman_stream_notify *pos;
    DL_FOREACH(stream->notifies, pos)
    {
        pos->notify_func(pos->args);
    }

    stream->package_count++;
    stream->package_id++;

    return ret;
}

int yinkman_stream_pull(struct yinkman_stream *stream, void **pdata, uint32_t mask)
{
    struct yinkman_stream_elem *elem;
    pthread_mutex_lock(&stream->mutex);
    DL_FOREACH(stream->stream_data, elem)
    {
        if (mask & elem->mask)
        {
            *pdata = elem->data;
            elem->used |= mask;
            elem->mask &= ~mask;
            pthread_mutex_unlock(&stream->mutex);
            return 0;
        }
    }
    pthread_mutex_unlock(&stream->mutex);
    return 1;
}

void yinkman_stream_release_all(struct yinkman_stream *stream)
{
    struct yinkman_stream_elem *elem, *tmp;
    pthread_mutex_lock(&stream->mutex);
    DL_FOREACH_SAFE(stream->stream_data, elem, tmp)
    {
        elem->mask = 0;
        elem->used = 0;
        DL_DELETE(stream->stream_data, elem);
    }
    ym_mem_pool_clear(stream->elem_pool);
    stream->package_count = 0;
    pthread_mutex_unlock(&stream->mutex);
}

struct yinkman_stream *yinkman_stream_get_by_name(const char *name)
{
    struct yinkman_stream *stream = NULL;
    DL_FOREACH(all_stream, stream)
    {
        if (!strcmp(name, stream->name))
        {
            break;
        }
    }
    if (stream == NULL)
    {
        log_e("can't find %s stream.", name);
        exit(0);
    }
    return stream;
}

uint32_t yinkman_stream_get_register_count(struct yinkman_stream *stream)
{
    return stream->register_count;
}

void yinkman_stream_list(void)
{
    struct yinkman_stream *stream = NULL;
    printf("%-20s\t%-20s\t%-20s\t%-20s\n", "stream", "register", "count", "id");
    printf("-----------------------------------------------------\n");
    DL_FOREACH(all_stream, stream)
    {
        printf("%-20s\t%-20d\t%-20d\t%-20d\n",
               stream->name,
               stream->register_count,
               stream->package_count,
               stream->package_id);
    }
}
MSH_CMD_EXPORT_ALIAS(yinkman_stream_list, stream_list, list all stream);
