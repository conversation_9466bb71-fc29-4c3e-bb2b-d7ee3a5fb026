/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-01-06 16:28:17
 * @LastEditTime: 2024-11-26 11:21:03
 * @LastEditors: Anzhi<PERSON>o
 * @Description: 常用工具库
 * @FilePath: /audio_mixer_rk3308/component/utilities.c
 * Copyright (c) 2006-2024, Yinkman Development Team
 */

#include <stdio.h>
#include <stdlib.h>
#include <limits.h>
#include <string.h>
#include <math.h>
#include <float.h>
#include <fcntl.h>
#include <printf.h>
#include <errno.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <sys/types.h>
#include <linux/ioctl.h>
#include <linux/input.h>
#include <netinet/ether.h>
#include <arpa/inet.h>
#include <net/if.h>
#include <gpiod.h>
#include <termios.h>
#include <linux/watchdog.h>
#include <alsa/asoundlib.h>
// #include <tinyalsa/asoundlib.h>

#include "config.h"
#include "ym_utilities.h"
#include "ym_startup.h"
#include "ym_timer.h"
#include "ym_database.h"
#include "ym_thread.h"
#include "ym_shell.h"
#include "ym_log.h"

#define UAC_CARD_INFO_NAME ("Yinkman UAC1")

static int32_t clear(int32_t argc, char **argv)
{
    (void)argc;
    (void)argv;

    printf("\x1b[2J\x1b[H");

    return 0;
}
MSH_CMD_EXPORT(clear, clear the terminal screen);

static int32_t reboot(int32_t argc, char **argv)
{
    (void)argc;
    (void)argv;

    printf("reboot...");
    pause_mixer();
    usleep(100000);
    return system("reboot -f");
}
MSH_CMD_EXPORT(reboot, reboot SoC);

static int _ym_tick_get()
{
    printf("tick:%lu\n", ym_tick_get());
    return 0;
}
MSH_CMD_EXPORT_ALIAS(_ym_tick_get, tick, get tick);

void *aligned_malloc(size_t required_bytes, size_t alignment)
{
    int offset = alignment - 1 + sizeof(void *);
    void *p1 = (void *)malloc(required_bytes + offset);
    if (p1 == NULL)
        return NULL;
    void **p2 = (void **)(((size_t)p1 + offset) & ~(alignment - 1));
    p2[-1] = p1;
    return p2;
}

void aligned_free(void *p2)
{
    void *p1 = ((void **)p2)[-1];
    free(p1);
}


int store_param(void)
{
    int err = 0;
    FILE *fc = fopen("/appcfg/curr-mode", "wb");
    if (fc != NULL)
    {
        fseek(fc, 0, SEEK_SET);

        if (fwrite(database_get(0), sizeof(database_value), DATABASE_INDEX_MAX, fc) != DATABASE_INDEX_MAX)
        {
            log_e("write parameters to curr-mode failed");
            err = -2;
        }
        else
        {
            log_i("store_param:store param successful.");
        }

        fflush(fc);
        fsync(fileno(fc));
        fclose(fc);
        /* save mode index */
        mode_index_set(gconfig_ctrl.mode);
    }
    else
    {
        err = -1;
        log_e("open curr-mode failed");
    }
    err |= system("sync;sync;");
    return err;
}

static int tune_mode_file_copy(const char *dst, const char *src, int init)
{
    int err = 0;
    FILE *fs = fopen(src, "rb");
    if (fs != NULL)
    {
        FILE *fd = NULL;
        struct stat st;
        database_value tmp;
        int index = 0;

        fd = fopen(dst, "wb");
        if (fd != NULL)
        {
            err = fstat(fileno(fs), &st);
            if (err == 0)
            {
                fseek(fs, 0, SEEK_SET);
                fseek(fd, 0, SEEK_SET);

                while (st.st_size > 0)
                {
                    if (st.st_size >= sizeof(database_value))
                    {
                        err = fread(&tmp, sizeof(database_value), 1, fs);
                        st.st_size -= sizeof(database_value);
                    }
                    else
                    {
                        err = fread(&tmp, st.st_size, 1, fs);
                        st.st_size = 0;
                    }
                    if (err == 0)
                    {
                        break;
                    }
                    else
                    {
                        err = 0;
                        if (init != 0) /* need to re-initialize parameters */
                        {
                            if (database_write(index++, tmp))
                            {
                                log_e("database write fault,index: %d", index - 1);
                                err = -3;
                                break;
                            }
                        }
                    }

                    fwrite(&tmp, sizeof(database_value), 1, fd);
                }

                fflush(fd);
                fsync(fileno(fd));
            }
            else
            {
                log_e("Get %s failed", dst);
            }

            fclose(fd);
        }
        else
        {
            err = -2;
            log_e("open %s failed", dst);
        }

        fclose(fs);
    }
    else
    {
        err = -1;
        log_e("open %s failed", src);
    }
    return err;
}

/**
 * @description: 保存调音模式和数据库版本
 * @return {0，成功，其他失败}
 * @use:
 */
int store_mode(void)
{
    char mode_file[32];
    memset(mode_file, '\0', 32);
    sprintf(mode_file, "/appcfg/%d", gconfig_ctrl.mode);
    log_i("Save mode %d params", gconfig_ctrl.mode);

    int err = 0;
    FILE *fc = fopen(mode_file, "wb");
    if (fc != NULL)
    {
        fseek(fc, 0, SEEK_SET);

        if (fwrite(database_get(0), sizeof(database_value), DATABASE_INDEX_MAX, fc) != DATABASE_INDEX_MAX)
        {
            log_e("write parameters to %s failed", mode_file);
            err = -2;
        }
        else
        {
            log_i("store_mode:store param successful.");
            mode_index_set(gconfig_ctrl.mode);
        }

        fflush(fc);
        fsync(fileno(fc));
        fclose(fc);
    }
    else
    {
        err = -1;
        log_e("open %s failed", mode_file);
    }
    return err;
}

int set_mixer_mode(int mode)
{
    int err;
    char mode_file[32];
    memset(mode_file, '\0', 32);
    err = mode_index_set(mode);
    database_take_shot();
    if (err == 0)
    {
        sprintf(mode_file, "/appcfg/%d", gconfig_ctrl.mode);
        log_i("Switch to mode %d, and re-initialize params", gconfig_ctrl.mode);
        err = tune_mode_file_copy("/appcfg/curr-mode", mode_file, 1);
        database_write_no_callback(a2b_slave_nodes_index, *database_get_from_shot(a2b_slave_nodes_index));
    }
    return err;
}

int mode_index_set(int mode)
{
    FILE *fmi = fopen("/appcfg/mode-index", "wb");
    if (fmi != NULL)
    {
        gconfig_ctrl.mode = mode;
        fseek(fmi, 0, SEEK_SET);
        int err = fwrite(&gconfig_ctrl, sizeof(config_ctrl_def), 1, fmi);
        if (err != 1)
        {
            log_e("write mode-index failed");
        }
        fflush(fmi);
        fsync(fileno(fmi));
        fclose(fmi);
        log_i("mode-index set to %d", mode);
        return 0;
    }
    else
    {
        log_e("open mode-index failed");
        return -1;
    }
}

int mode_index_get(void)
{
    FILE *fmi = fopen("/appcfg/mode-index", "rb");
    if (fmi != NULL)
    {
        fseek(fmi, 0, SEEK_SET);
        int err = fread(&gconfig_ctrl, sizeof(config_ctrl_def), 1, fmi);
        if (err != 1)
        {
            log_e("read mode-index failed");
        }
        fclose(fmi);
    }
    return gconfig_ctrl.mode;
}

int nonvolatile_global_cfg_save(void)
{
    FILE *fmi = fopen(NONVOLATILE_GLOBAL_CFG_PATH, "wb");
    if (fmi != NULL)
    {
        fseek(fmi, 0, SEEK_SET);
        int err = fwrite(&nonvolatile_global_cfg, sizeof(nonvolatile_global_cfg_t), 1, fmi);
        if (err != 1)
        {
            log_e("write nonvolatile_global_cfg failed");
        }
        fflush(fmi);
        fsync(fileno(fmi));
        fclose(fmi);
        return 0;
    }
    else
    {
        log_e("open nonvolatile_global_cfg failed");
        return -1;
    }
}

int nonvolatile_global_cfg_read(void)
{
    int err = 0;
    FILE *fmi = fopen(NONVOLATILE_GLOBAL_CFG_PATH, "rb");
    if (fmi != NULL)
    {
        fseek(fmi, 0, SEEK_SET);
        err = fread(&nonvolatile_global_cfg, sizeof(nonvolatile_global_cfg_t), 1, fmi);
        if (err != 1)
        {
            log_e("read nonvolatile_global_cfg failed");
        }
        fclose(fmi);
    }
    return !err;
}


struct tune_key_check_def
{
    int fd;
    ym_timer_p upgraed_timer;
};

static void upgrade_timeout(void *param)
{
    log_i("upgrade_timeout");
    if (system("sync;sync;") < 0)
    {
        log_e("system sync fault");
    }

    {
        int reboot(int argc, char **argv);
#define MISC_FILE_PATH "/dev/block/by-name/misc"
#define MISC_MSG_OFFSET 16 * 1024

        /* Bootloader Message (2-KiB)
         *
         * This structure describes the content of a block in flash
         * that is used for recovery and the bootloader to talk to
         * each other.
         *
         * The command field is updated by linux when it wants to
         * reboot into recovery or to update radio or bootloader firmware.
         * It is also updated by the bootloader when firmware update
         * is complete (to boot into recovery for any final cleanup)
         *
         * The status field is written by the bootloader after the
         * completion of an "update-radio" or "update-hboot" command.
         *
         * The recovery field is only written by linux and used
         * for the system to send a message to recovery or the
         * other way around.
         *
         * The stage field is written by packages which restart themselves
         * multiple times, so that the UI can reflect which invocation of the
         * package it is.  If the value is of the format "#/#" (eg, "1/3"),
         * the UI will add a simple indicator of that status.
         *
         * We used to have slot_suffix field for A/B boot control metadata in
         * this struct, which gets unintentionally cleared by recovery or
         * uncrypt. Move it into struct bootloader_message_ab to avoid the
         * issue.
         */
        struct android_bootloader_message
        {
            char command[32];
            char status[32];
            char recovery[768];

            /* The 'recovery' field used to be 1024 bytes.	It has only ever
             * been used to store the recovery command line, so 768 bytes
             * should be plenty.  We carve off the last 256 bytes to store the
             * stage string (for multistage packages) and possible future
             * expansion. */
            char stage[32];

            /* The 'reserved' field used to be 224 bytes when it was initially
             * carved off from the 1024-byte recovery field. Bump it up to
             * 1184-byte so that the entire bootloader_message struct rounds up
             * to 2048-byte. */
            char reserved[1184];
        };
        FILE *misc_file;
        if ((misc_file = fopen(MISC_FILE_PATH, "wb")) == NULL)
        {
            log_e("Open misc file printf.");
            return;
        }
        log_i("update: write command to misc file: ");
        fseek(misc_file, MISC_MSG_OFFSET, SEEK_SET);
        struct android_bootloader_message msg;
        memset(&msg, 0, sizeof(msg));
        strcpy(msg.command, "boot-recovery");
        strcpy(msg.recovery, "ymupgrade");
        fwrite(&msg, sizeof(msg), 1, misc_file);
        fsync(fileno(misc_file));
        fclose(misc_file);
        log_i("done.");
        reboot(0, NULL);
    }
}

int upgrade(int argc, char **argv)
{
    upgrade_timeout(NULL);
    return 0;
}
MSH_CMD_EXPORT(upgrade, reboot and into loader mode);

uint32_t tune_key_status = 0;
uint32_t disable_key_tune = 0;

static void tune_key_check_timeout(void *param)
{
    int ret;
    struct input_event event;
    struct tune_key_check_def *p = (struct tune_key_check_def *)param;
    static uint32_t count;
    void aec_afc_set_tune_mode(AEC_AFC_TUNE_MODE mode);

    count++;
    ret = read(p->fd, &event, sizeof(struct input_event));
    if (ret < 0)
        return;

    if (event.type != EV_SYN)
    {
        // 有信号沿
        log_i("type:%d, code:%d, value:%d", event.type, event.code, event.value);
        if (event.value)
        {
            ym_timer_start(p->upgraed_timer);
            count = 0;
        }
        else
        {
            ym_timer_stop(p->upgraed_timer);
            // 判断按下多久
            if (count <= (1000 / 50))
            {
                // 1s以下，不做处理
            }
            else if (count <= (3000 / 50))
            {
                tune_key_status = 2;
                // 1-3s ,切到模式0配置，开始AEC_AFC调音
                if (!disable_key_tune)
                {
                    log_i("button tune mode0");
                    pause_mixer();
                    usleep(1000 * 10);
                    set_mixer_mode(0);
                    resume_mixer();
                    aec_afc_set_tune_mode(AEC_TUNE);
                }
            }
            else
            {
                tune_key_status = 3;
                if (!disable_key_tune)
                {
                    log_i("button tune mode1");
                    // 3-20s 切到模式1配置，开始AEC_AFC调音
                    pause_mixer();
                    usleep(1000 * 10);
                    set_mixer_mode(1);
                    resume_mixer();
                    aec_afc_set_tune_mode(AEC_AFC_TUNE);
                }
            }
        }
    }
}

int tune_key_init(void)
{
    static struct tune_key_check_def p_tune_key;

    p_tune_key.fd = open("/dev/input/event0", O_RDONLY);
    if (!p_tune_key.fd)
    {
        log_e("can't open tune key fd:/dev/input/event0");
        return 1;
    }
    else
    {
        set_fd_noblock(p_tune_key.fd);
        p_tune_key.upgraed_timer = ym_timer_create("upgrade_timer", upgrade_timeout, &p_tune_key, 1, TUNE_KEY_UPGRADE_TIMEOUT);
        // 每50ms poll一次tune key
        ym_timer_start(ym_timer_create("tune_key_check", tune_key_check_timeout, &p_tune_key, 0, 50));
    }

    return 0;
}

#ifndef PCBA_TEST

static struct gpiod_line *func_gpio_line[gpio_max];

void set_gpio(gpio_index_def index, int value)
{
    if (index >= gpio_max)
    {
        log_e("gpio index out of range.");
        return;
    }
    if (func_gpio_line[index])
        gpiod_line_set_value(func_gpio_line[index], value);
}

int get_gpio(gpio_index_def index)
{
    if (index >= gpio_max)
    {
        log_e("gpio index out of range.");
        return -1;
    }
    if (func_gpio_line[index])
        return gpiod_line_get_value(func_gpio_line[index]);
    return -1;
}

static int func_gpio_init(void)
{
    struct gpiod_chip *tmp_gpio = gpiod_chip_open_by_number(0);
    if (tmp_gpio)
    {
        func_gpio_line[led_gate] = gpiod_chip_get_line(tmp_gpio, 20); /* gpio0_c4 */
        if (func_gpio_line[led_gate])
        {
            gpiod_line_request_output(func_gpio_line[led_gate], "led", 0);
        }
        else
        {
            log_e("can't open led gpio line.");
        }
    }
    else
    {
        log_e("can't open led gpio.");
    }
    tmp_gpio = gpiod_chip_open_by_number(4);
    if (tmp_gpio)
    {
#ifndef SPK_MUTE_GATE_DISABLED
        func_gpio_line[spk_mute_gate] = gpiod_chip_get_line(tmp_gpio, 7); /* gpio4_a7 */
        if (func_gpio_line[spk_mute_gate])
        {
            gpiod_line_request_output(func_gpio_line[spk_mute_gate], "spk_mute", 0);
        }
        else
        {
            log_e("can't open spk_mute gpio line.");
        }
#endif // !SPK_MUTE_GATE_DISABLED

#ifndef HP_MUTE_GATE_DISABLED
        func_gpio_line[hp_mute_gate] = gpiod_chip_get_line(tmp_gpio, 11); /* gpio4_b3 */
        if (func_gpio_line[hp_mute_gate])
        {
            gpiod_line_request_output(func_gpio_line[hp_mute_gate], "hp_mute", 0);
        }
        else
        {
            log_e("can't open hp_mute gpio line.");
        }
#endif // end HP_MUTE_GATE_DISABLED
    }
    else
    {
        log_e("can't open spk_hp gpio.");
    }
#ifndef REC_MUTE_GATE_DISABLED
    tmp_gpio = gpiod_chip_open_by_number(2);
    if (tmp_gpio)
    {
        func_gpio_line[rec_mute_gate] = gpiod_chip_get_line(tmp_gpio, 0); /* gpio2_a0 */
        if (func_gpio_line[rec_mute_gate])
        {
            gpiod_line_request_output(func_gpio_line[rec_mute_gate], "rec_mute", 0);
        }
        else
        {
            log_e("can't open rec_mute gpio line.");
        }
    }
    else
    {
        log_e("can't open rec_mute gpio.");
    }
#endif /* end #if REC_MUTE_GATE_DISABLED */

#ifdef PA_MUTE_GATE_ENABLED
    tmp_gpio = gpiod_chip_open_by_number(0);
    if (tmp_gpio)
    {
        func_gpio_line[pa_mute_gate] = gpiod_chip_get_line(tmp_gpio, 2); /* gpio0_a2 */
        if (func_gpio_line[pa_mute_gate])
        {
            gpiod_line_request_output(func_gpio_line[pa_mute_gate], "pa_mute", 0);
        }
        else
        {
            log_e("can't open pa_mute gpio line.");
        }
    }
    else
    {
        log_e("can't open pa_mute gpio.");
    }
#endif /* end #if PA_MUTE_GATE_ENABLED */
    return 0;
}
// INIT_LOW_LEVEL_EXPORT(func_gpio_init);

__weak void platform_led_toggle(void) {}

void led_toggle(void)
{
    if (gpiod_line_get_value(func_gpio_line[led_gate]))
        gpiod_line_set_value(func_gpio_line[led_gate], 0);
    else
        gpiod_line_set_value(func_gpio_line[led_gate], 1);
    platform_led_toggle();
}
#else

__weak void led_toggle(void) {}

__weak void set_gpio(gpio_index_def index, int value) {}

__weak int get_gpio(gpio_index_def index) { return 0; }

#endif // !PCBA_TEST

uint16_t drv_crc_16_ccitt_init_value(uint16_t crc, const uint8_t *data, uint32_t num)
{
    const uint16_t crc16_table_ccitt[256] = {
        0x0000, 0x1189, 0x2312, 0x329B, 0x4624, 0x57AD, 0x6536, 0x74BF,
        0x8C48, 0x9DC1, 0xAF5A, 0xBED3, 0xCA6C, 0xDBE5, 0xE97E, 0xF8F7,
        0x1081, 0x0108, 0x3393, 0x221A, 0x56A5, 0x472C, 0x75B7, 0x643E,
        0x9CC9, 0x8D40, 0xBFDB, 0xAE52, 0xDAED, 0xCB64, 0xF9FF, 0xE876,
        0x2102, 0x308B, 0x0210, 0x1399, 0x6726, 0x76AF, 0x4434, 0x55BD,
        0xAD4A, 0xBCC3, 0x8E58, 0x9FD1, 0xEB6E, 0xFAE7, 0xC87C, 0xD9F5,
        0x3183, 0x200A, 0x1291, 0x0318, 0x77A7, 0x662E, 0x54B5, 0x453C,
        0xBDCB, 0xAC42, 0x9ED9, 0x8F50, 0xFBEF, 0xEA66, 0xD8FD, 0xC974,
        0x4204, 0x538D, 0x6116, 0x709F, 0x0420, 0x15A9, 0x2732, 0x36BB,
        0xCE4C, 0xDFC5, 0xED5E, 0xFCD7, 0x8868, 0x99E1, 0xAB7A, 0xBAF3,
        0x5285, 0x430C, 0x7197, 0x601E, 0x14A1, 0x0528, 0x37B3, 0x263A,
        0xDECD, 0xCF44, 0xFDDF, 0xEC56, 0x98E9, 0x8960, 0xBBFB, 0xAA72,
        0x6306, 0x728F, 0x4014, 0x519D, 0x2522, 0x34AB, 0x0630, 0x17B9,
        0xEF4E, 0xFEC7, 0xCC5C, 0xDDD5, 0xA96A, 0xB8E3, 0x8A78, 0x9BF1,
        0x7387, 0x620E, 0x5095, 0x411C, 0x35A3, 0x242A, 0x16B1, 0x0738,
        0xFFCF, 0xEE46, 0xDCDD, 0xCD54, 0xB9EB, 0xA862, 0x9AF9, 0x8B70,
        0x8408, 0x9581, 0xA71A, 0xB693, 0xC22C, 0xD3A5, 0xE13E, 0xF0B7,
        0x0840, 0x19C9, 0x2B52, 0x3ADB, 0x4E64, 0x5FED, 0x6D76, 0x7CFF,
        0x9489, 0x8500, 0xB79B, 0xA612, 0xD2AD, 0xC324, 0xF1BF, 0xE036,
        0x18C1, 0x0948, 0x3BD3, 0x2A5A, 0x5EE5, 0x4F6C, 0x7DF7, 0x6C7E,
        0xA50A, 0xB483, 0x8618, 0x9791, 0xE32E, 0xF2A7, 0xC03C, 0xD1B5,
        0x2942, 0x38CB, 0x0A50, 0x1BD9, 0x6F66, 0x7EEF, 0x4C74, 0x5DFD,
        0xB58B, 0xA402, 0x9699, 0x8710, 0xF3AF, 0xE226, 0xD0BD, 0xC134,
        0x39C3, 0x284A, 0x1AD1, 0x0B58, 0x7FE7, 0x6E6E, 0x5CF5, 0x4D7C,
        0xC60C, 0xD785, 0xE51E, 0xF497, 0x8028, 0x91A1, 0xA33A, 0xB2B3,
        0x4A44, 0x5BCD, 0x6956, 0x78DF, 0x0C60, 0x1DE9, 0x2F72, 0x3EFB,
        0xD68D, 0xC704, 0xF59F, 0xE416, 0x90A9, 0x8120, 0xB3BB, 0xA232,
        0x5AC5, 0x4B4C, 0x79D7, 0x685E, 0x1CE1, 0x0D68, 0x3FF3, 0x2E7A,
        0xE70E, 0xF687, 0xC41C, 0xD595, 0xA12A, 0xB0A3, 0x8238, 0x93B1,
        0x6B46, 0x7ACF, 0x4854, 0x59DD, 0x2D62, 0x3CEB, 0x0E70, 0x1FF9,
        0xF78F, 0xE606, 0xD49D, 0xC514, 0xB1AB, 0xA022, 0x92B9, 0x8330,
        0x7BC7, 0x6A4E, 0x58D5, 0x495C, 0x3DE3, 0x2C6A, 0x1EF1, 0x0F78};

    while (num--)
    {
        crc = (crc >> 8) ^ crc16_table_ccitt[((crc ^ (*data)) & 0xff)];
        data++;
    }
    return crc;
}

void padFile(const char *filePath, size_t alignSize)
{
    FILE *file = fopen(filePath, "ab");

    if (file == NULL)
    {
        log_e("Error opening file");
        return;
    }

    // Get the current file size
    fseek(file, 0, SEEK_END);
    long fileSize = ftell(file);

    // Calculate the padding size needed for alignment
    size_t paddingSize = (alignSize - (fileSize % alignSize)) % alignSize;

    // Pad the file with zeros
    for (size_t i = 0; i < paddingSize; i++)
    {
        fputc(0, file);
    }

    fclose(file);
}

void printProgressBar(int progress, int total)
{
    const int barWidth = 50;

    float percentage = (float)progress / total;
    int progressBarLength = percentage * barWidth;

    printf("[");
    for (int i = 0; i < barWidth; i++)
    {
        if (i < progressBarLength)
        {
            printf("#");
        }
        else
        {
            printf(" ");
        }
    }
    printf("] %.2f%%\r", percentage * 100);
    fflush(stdout);
}

int is_that_card(int card, const char *name)
{
    snd_ctl_t *handle;
    int err;
    snd_ctl_card_info_t *info;
    snd_ctl_card_info_alloca(&info);

    char tmp_name[32];
    memset(tmp_name, 0, 32);
    sprintf(tmp_name, "hw:%d", card);
    if ((err = snd_ctl_open(&handle, tmp_name, 0)) < 0)
    {
        log_w("control open (%i): %s", card, snd_strerror(err));
        return 0;
    }
    if ((err = snd_ctl_card_info(handle, info)) < 0)
    {
        log_w("control hardware info (%i): %s", card, snd_strerror(err));
        snd_ctl_close(handle);
        return 0;
    }
    err = strcmp(name, snd_ctl_card_info_get_name(info));
    snd_ctl_close(handle);
    return !err;
}

int get_uac_card(void)
{
    snd_ctl_t *handle;
    int card, err;
    snd_ctl_card_info_t *info;
    snd_pcm_info_t *pcminfo;
    snd_ctl_card_info_alloca(&info);
    snd_pcm_info_alloca(&pcminfo);

    card = -1;
    if (snd_card_next(&card) < 0 || card < 0)
    {
        printf(("no soundcards found..."));
        return -1;
    }

    while (card >= 0)
    {
        char name[32];
        sprintf(name, "hw:%d", card);
        if ((err = snd_ctl_open(&handle, name, 0)) < 0)
        {
            printf("control open (%i): %s", card, snd_strerror(err));
            goto next_card;
        }
        if ((err = snd_ctl_card_info(handle, info)) < 0)
        {
            printf("control hardware info (%i): %s", card, snd_strerror(err));
            snd_ctl_close(handle);
            goto next_card;
        }
        snd_ctl_close(handle);
        if (strcmp(UAC_CARD_INFO_NAME, snd_ctl_card_info_get_name(info)) == 0)
        {
            break;
        }
    next_card:
        if (snd_card_next(&card) < 0)
        {
            card = -1;
            break;
        }
    }
    return card;
}

static void device_list(void)
{
    snd_ctl_t *handle;
    int card, err, dev, idx;
    snd_ctl_card_info_t *info;
    snd_pcm_info_t *pcminfo;
    snd_ctl_card_info_alloca(&info);
    snd_pcm_info_alloca(&pcminfo);

    card = -1;
    if (snd_card_next(&card) < 0 || card < 0)
    {
        printf(("no soundcards found..."));
        return;
    }
    printf(("**** List of %s Hardware Devices ****\n"),
           snd_pcm_stream_name(SND_PCM_STREAM_PLAYBACK));
    while (card >= 0)
    {
        char name[32];
        sprintf(name, "hw:%d", card);
        if ((err = snd_ctl_open(&handle, name, 0)) < 0)
        {
            printf("control open (%i): %s", card, snd_strerror(err));
            goto next_card;
        }
        if ((err = snd_ctl_card_info(handle, info)) < 0)
        {
            printf("control hardware info (%i): %s", card, snd_strerror(err));
            snd_ctl_close(handle);
            goto next_card;
        }
        dev = -1;
        while (1)
        {
            unsigned int count;
            if (snd_ctl_pcm_next_device(handle, &dev) < 0)
                printf("snd_ctl_pcm_next_device");
            if (dev < 0)
                break;
            snd_pcm_info_set_device(pcminfo, dev);
            snd_pcm_info_set_subdevice(pcminfo, 0);
            snd_pcm_info_set_stream(pcminfo, SND_PCM_STREAM_PLAYBACK);
            if ((err = snd_ctl_pcm_info(handle, pcminfo)) < 0)
            {
                if (err != -ENOENT)
                    printf("control digital audio info (%i): %s", card, snd_strerror(err));
                continue;
            }
            printf(("card %i: %s [%s], device %i: %s [%s]\n"),
                   card, snd_ctl_card_info_get_id(info), snd_ctl_card_info_get_name(info),
                   dev,
                   snd_pcm_info_get_id(pcminfo),
                   snd_pcm_info_get_name(pcminfo));
            count = snd_pcm_info_get_subdevices_count(pcminfo);
            printf(("  Subdevices: %i/%i\n"),
                   snd_pcm_info_get_subdevices_avail(pcminfo), count);
            for (idx = 0; idx < (int)count; idx++)
            {
                snd_pcm_info_set_subdevice(pcminfo, idx);
                if ((err = snd_ctl_pcm_info(handle, pcminfo)) < 0)
                {
                    printf("control digital audio playback info (%i): %s", card, snd_strerror(err));
                }
                else
                {
                    printf(("  Subdevice #%i: %s\n"),
                           idx, snd_pcm_info_get_subdevice_name(pcminfo));
                }
            }
        }
        snd_ctl_close(handle);
    next_card:
        if (snd_card_next(&card) < 0)
        {
            printf("snd_card_next");
            break;
        }
    }
}
MSH_CMD_EXPORT(device_list, device list);

static void soc_temp_callback(int index, database_value old_value, database_value new_value)
{
    float temp = new_value.i32 / 100.0f;
    if (temp > 100.0)
    {
        log_i("soc temp: %3.2f", temp);
    }
}

static int monitor_temp(void)
{
    database_register(soc_temp_index, soc_temp_callback);
    return 0;
}
INIT_APP_EXPORT(monitor_temp);

static void *temp_log_thread_handle(void *arg)
{
    int temp;
    database_value tmp;
    for (;;)
    {
        FILE *file = fopen("/sys/class/thermal/thermal_zone0/temp", "r");
        if (file == NULL)
        {
            log_e("Failed to open thermal_zone0/temp file.\n");
            sleep(1);
            continue;
        }
        if (fscanf(file, "%d", &temp) < 0)
        {
            log_e("Failed to read thermal_zone0/temp file.\n");
            fclose(file);
            sleep(1);
            continue;
        }
        fclose(file);
        tmp.i32 = temp / 10;
        database_write(soc_temp_index, tmp);
        temp = 0;
        sleep(1);
    }
    return NULL;
}

static int temp_log_app_init(void)
{
    ym_thread_create("temp_log",
                     default_thread_cpuid,
                     default_thread_prio,
                     temp_log_thread_handle,
                     NULL);
    return 0;
}
INIT_APP_EXPORT(temp_log_app_init);

// void ym_protocol_serial_set_param(int fd, int baudrate)
// {
//     struct termios param;
//     tcgetattr(fd, &param); // Get terminal properties

//     cfsetospeed(&param, baudrate);
//     cfsetispeed(&param, baudrate);

//     param.c_iflag &= ~(BRKINT | ICRNL | INPCK | ISTRIP | IXON); // 屏蔽各种控制字符
//     // OPOST: 使用原始输出，就是禁用输出处理，使数据能不经过处理、过滤地完整地输出到串口接口。
//     param.c_oflag &= ~OPOST;
//     // CLOCAL: 忽略调制调解器状态行
//     // CREAD: 启用接收
//     param.c_cflag |= (CLOCAL | CREAD);
//     // ~(ICANON | ECHO | ECHOE | ISIG): 原始数据模式，并关闭信号
//     param.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);

//     // 设置读取超时
//     param.c_cc[VMIN] = 1;   // 协议最少4字节
//     param.c_cc[VTIME] = 10; // 1s,单位百毫秒

//     tcsetattr(fd, TCSANOW, &param);
// }

// void ym_otg_serial_set_param(int fd)
// {
//     struct termios param;
//     tcgetattr(fd, &param); // Get terminal properties
//     cfsetospeed(&param, B9600);
//     cfsetispeed(&param, B9600);
//     param.c_iflag &= ~(BRKINT | ICRNL | INPCK | ISTRIP | IXON); // 屏蔽各种控制字符
//     // OPOST: 使用原始输出，就是禁用输出处理，使数据能不经过处理、过滤地完整地输出到串口接口。
//     param.c_oflag &= ~OPOST;
//     // CLOCAL: 忽略调制调解器状态行
//     // CREAD: 启用接收
//     param.c_cflag |= (CLOCAL | CREAD);
//     // ~(ICANON | ECHO | ECHOE | ISIG): 原始数据模式，并关闭信号
//     param.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);

//     tcsetattr(fd, TCSANOW, &param);
// }

// void ym_console_serial_set_param(int fd)
// {
//     struct termios param;
//     tcgetattr(fd, &param); // Get terminal properties
//     cfsetospeed(&param, B9600);
//     cfsetispeed(&param, B9600);
//     param.c_iflag &= ~(BRKINT | INPCK | ISTRIP | IXON); // 屏蔽各种控制字符
//     param.c_cflag |= (CLOCAL | CREAD);
//     param.c_oflag &= ~OPOST;
//     param.c_oflag |= ONLCR | OPOST;
//     // ~(ICANON | ISIG): 原始数据模式，并关闭信号
//     param.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);

//     tcsetattr(fd, TCSANOW, &param);
// }

static void get_temp(void)
{
    log_i("soc temp: %3.2f", database_get(soc_temp_index)->i32 / 100.0f);
}
MSH_CMD_EXPORT(get_temp, get soc temp);

static void get_load(void)
{
    log_i("cpu2 load: %u", database_get(cpu_load_ms_index + MIXER1_BASE_INDEX)->u32);
    log_i("cpu3 load: %u", database_get(cpu_load_ms_index + MIXER2_BASE_INDEX)->u32);
}
MSH_CMD_EXPORT(get_load, get cpu load);

#define CPU_COUNTS (4) // 根据你的系统 CPU 核心数调整

static void read_cpu_times(long long int times[CPU_COUNTS][10])
{
    FILE *fp = fopen("/proc/stat", "r");
    if (!fp)
    {
        perror("Failed to open /proc/stat");
        return;
    }

    char buffer[256];
    int cpu_id = 0;
    while (fgets(buffer, sizeof(buffer), fp))
    {
        if (strncmp(buffer, "cpu", 3) == 0)
        {
            long long int user, nice, system, idle, iowait, irq, softirq, steal, guest, guest_nice;
            sscanf(buffer, "cpu%d %lld %lld %lld %lld %lld %lld %lld %lld %lld %lld",
                   &cpu_id, &user, &nice, &system, &idle, &iowait, &irq, &softirq, &steal, &guest, &guest_nice);
            if (cpu_id < CPU_COUNTS)
            {
                times[cpu_id][0] = user;
                times[cpu_id][1] = nice;
                times[cpu_id][2] = system;
                times[cpu_id][3] = idle;
                times[cpu_id][4] = iowait;
                times[cpu_id][5] = irq;
                times[cpu_id][6] = softirq;
                times[cpu_id][7] = steal;
                times[cpu_id][8] = guest;
                times[cpu_id][9] = guest_nice;
            }
        }
    }
    fclose(fp);
}

static void calculate_and_print_usage(long long int start[CPU_COUNTS][10], long long int end[CPU_COUNTS][10])
{
    for (int i = 0; i < CPU_COUNTS; i++)
    {
        long long int total_time = 0;
        long long int idle_time = 0;

        for (int j = 0; j < 10; j++)
        {
            total_time += end[i][j] - start[i][j];
        }

        idle_time = end[i][3] - start[i][3]; // idle time is at index 3

        double usage_percentage = 100.0 * (1 - (idle_time / (double)total_time));
        printf("CPU %d usage: %.2f%%\n", i, usage_percentage);
    }
}

static void print_cpu_load(void)
{
    static long long int start[CPU_COUNTS][10];
    static long long int end[CPU_COUNTS][10];

    if (start[0][0] == 0)
    {
        read_cpu_times(start);
        sleep(1);
    }
    read_cpu_times(end);

    calculate_and_print_usage(start, end);
    memcpy(start, end, sizeof(start));
}
MSH_CMD_EXPORT(print_cpu_load, print cpu load);

static const int hwid_adc_margin = 15;

typedef struct
{
    uint32_t hwid;
    int adc_value;
} hwid_to_adc;

static hwid_to_adc hwid_list[] = {
    {.hwid = 0x594d0010, .adc_value = 1024},
    {.hwid = 0x594d0011, .adc_value = 886}, // 小圆盘V3
    {.hwid = 0x594d0011, .adc_value = 860}, // 小圆盘V2
    {.hwid = 0x594d0011, .adc_value = 820}, // 小圆盘V1
    {.hwid = 0x594d0012, .adc_value = 307},
    {.hwid = 0x594d0012, .adc_value = 165},
    {.hwid = 0x594d0012, .adc_value = 394},
    {.hwid = 0x594d0012, .adc_value = 233},
    {.hwid = 0x594d0013, .adc_value = 678},
    {.hwid = 0x594d0014, .adc_value = 508},
    {.hwid = 0x594d0015, .adc_value = 609},
    {.hwid = 0x594d0016, .adc_value = 764},
    {.hwid = 0x594d0017, .adc_value = 554},
    {.hwid = 0x594d0018, .adc_value = 468},
    {.hwid = 0x594d0019, .adc_value = 645},
    {.hwid = 0x594d001a, .adc_value = 916}, // wx_128
    {.hwid = 0x594d001b, .adc_value = 948}, // youyan
    {.hwid = 0, .adc_value = 0},
};

uint32_t hwid_adc_subversion;

static int hwid_subversion_adc_read(void)
{
#define BUFFER_SIZE 64
    int fd;
    char buffer[BUFFER_SIZE];
    ssize_t bytes_read;
    database_value adc_value;
    // extern uint32_t hwid_adc_subversion;

    fd = open("/sys/bus/iio/devices/iio:device0/in_voltage2_raw", O_RDONLY);
    if (fd < 0)
    {
        log_e("Failed to open ADC channel");
        return -1;
    }

    bytes_read = read(fd, buffer, BUFFER_SIZE - 1);
    if (bytes_read < 0)
    {
        log_e("Failed to read ADC value");
        close(fd);
        return -1;
    }

    buffer[bytes_read] = '\0';

    adc_value.i32 = atoi(buffer);

    database_write(hwid_adc_subversion_index, adc_value);
    hwid_adc_subversion = adc_value.i32;

    log_i("ADC value: %d", adc_value.i32);

    close(fd);

    return 0;
}
// INIT_HW_EXPORT(hwid_subversion_adc_read); 暂时没有识别电阻

static int app_hwid_subversion_adc_read(void) { return hwid_subversion_adc_read(); }
INIT_APP_EXPORT(app_hwid_subversion_adc_read);

// int check_upgrade_file(void)
// {
//     typedef struct
//     {
//         uint32_t hwid;
//         char sha256[66];
//     } __attribute__((packed)) check_upgrade_file_def;
//     check_upgrade_file_def data;

//     extern uint32_t hwid_adc_subversion;

//     int ret = -1;
//     int err = 0;

//     ret = system("rm -rf /data/tmp;mkdir -p /data/tmp/;tar -xf /data/upgrade -C /data/tmp/;rm -rf /data/upgrade");
//     ret = access("/data/tmp/upgrade", 0);
//     if (ret != 0)
//     {
//         printf("%s errno=%d\n", __FUNCTION__, ret);
//         return ret;
//     }
//     // 打开/data/upgarde 文件
//     FILE *file = fopen("/data/tmp/upgrade", "rb");
//     // 将文件seek到文件末尾
//     fseek(file, -(sizeof(check_upgrade_file_def) - 1), SEEK_END);
//     // 读取文件内容
//     err = fread(&data, (sizeof(check_upgrade_file_def) - 1), 1, file);
//     if (err != 1)
//     {
//         log_e("Failed to read upgrade file.");
//         fclose(file);
//         return -1;
//     }

//     data.sha256[65] = 0;
//     fclose(file);

//     data.hwid = be32toh(data.hwid);
//     log_i("hwid: 0x%08x", data.hwid);

//     // 将/data/upgrade中最后的65字节去掉
//     int fd = open("/data/tmp/upgrade", O_RDWR);
//     if (ftruncate(fd, lseek(fd, 0, SEEK_END) - 65) < 0)
//     {
//         log_e("Failed to truncate upgrade file.");
//         close(fd);
//         return -1;
//     }
//     close(fd);

//     // 检查文件是否正确
//     hwid_subversion_adc_read();

//     // 检查hwid是否匹配
//     for (int i = 0; hwid_list[i].hwid != 0; i++)
//     {
//         if (abs(hwid_list[i].adc_value - hwid_adc_subversion) < hwid_adc_margin)
//         {
//             if (hwid_list[i].hwid == data.hwid)
//             {
//                 log_i("hwid match.");
//                 ret = 0;
//             }
//             else
//             {
//                 break;
//             }
//         }
//     }
//     if (ret != 0)
//     {
//         log_e("hwid not match.");
//         return -1;
//     }
//     char cmd[128];
//     sprintf(cmd, "sha256sum /data/tmp/upgrade | grep %s", data.sha256);
//     ret = system(cmd);
//     if (ret != 0)
//     {
//         log_e("sha256 not match.");
//         return -1;
//     }

//     return 0;
// }

static inline void parse_ip(const char *ip_str, uint8_t ip[4])
{
    sscanf(ip_str, "%hhu.%hhu.%hhu.%hhu", &ip[0], &ip[1], &ip[2], &ip[3]);
}
/**
 * @description: 该函数只适配0xf5协议获取，其他协议请自行适配
 * @param {uint8_t} *data，len = 13
 * @return {*}
 * @use:
 */
void get_net_config(uint8_t *data)
{
    typedef struct
    {
        uint8_t dhcp;
        uint8_t addr[4];
        uint8_t mask[4];
        uint8_t gate[4];
    } __attribute__((packed)) NetworkConfig_t;
    NetworkConfig_t *netconfig = (NetworkConfig_t *)data;

    FILE *file = fopen("/data/net_config", "r");
    if (file == NULL)
    {
        perror("cat't open net_config file.");
        return;
    }

    char line[128];
    while (fgets(line, sizeof(line), file))
    {
        if (strncmp(line, "DHCP=", 5) == 0)
        {
            netconfig->dhcp = (uint8_t)atoi(line + 5);
        }
        else if (strncmp(line, "ADDR=", 5) == 0)
        {
            parse_ip(line + 5, netconfig->addr);
        }
        else if (strncmp(line, "MASK=", 5) == 0)
        {
            parse_ip(line + 5, netconfig->mask);
        }
        else if (strncmp(line, "GATE=", 5) == 0)
        {
            parse_ip(line + 5, netconfig->gate);
        }
    }

    fclose(file);
    return;
}
/**
 * @description: 该函数只适配0xf5协议获取，其他协议请自行适配
 * @param {uint8_t} *data，len = 6
 * @return {*}
 * @use:
 */
void get_mac_addr(uint8_t *data)
{
    int sock;
    struct ifreq ifr;
    int i;

    memset(&ifr, 0, sizeof(struct ifreq));
    strncpy(ifr.ifr_name, "eth0", sizeof(ifr.ifr_name));

    if ((sock = socket(AF_INET, SOCK_DGRAM, 0)) == -1)
    {
        printf("Socket creation failed\n");
        return;
    }

    if (ioctl(sock, SIOCGIFHWADDR, &ifr) < 0)
    {
        printf("IOCTL error %d.\n", __LINE__);
        return;
    }
    // printf("mac addr:%s\n", ether_ntoa((struct ether_addr *)ifr.ifr_hwaddr.sa_data));
    struct ether_addr *hw_addr = (struct ether_addr *)ifr.ifr_hwaddr.sa_data;
    for (i = 0; i < 6; i++)
    {
        data[i] = hw_addr->ether_addr_octet[i];
    }
}

void recored_start_count(void)
{
    // 读取文件中的启动计数，如果文件不存在则创建文件，启动计数为0
    int fd = open("/userdata/start_count", O_RDWR | O_CREAT, 0666);
    if (fd < 0)
    {
        log_e("Failed to open start count file.");
        return;
    }
    char buffer[16];
    ssize_t bytes_read = read(fd, buffer, sizeof(buffer) - 1);
    if (bytes_read < 0)
    {
        log_e("Failed to read start count file.");
        close(fd);
        return;
    }
    buffer[bytes_read] = '\0';
    int start_count = atoi(buffer);
    log_i("Start count: %d", start_count);
    start_count++;
    lseek(fd, 0, SEEK_SET);
    sprintf(buffer, "%d", start_count);
    if (write(fd, buffer, strlen(buffer)) < 0)
    {
        log_e("Failed to write start count file.");
        close(fd);
        return;
    }
    fsync(fd);
    close(fd);
}


// __weak void dwin_test(void) {}

// __weak uint16_t get_dwin_test_flag(void)
// {
//     return 0;
// }




int set_fd_noblock(int fd)
{
    /* Change the socket into non-blocking state F_SETFL is a command saying set flag and flag is 0_NONBLOCK */
    const int flags = fcntl(fd, F_GETFL, 0);
    if (flags < 0)
    {
        return flags;
    }
    if (flags & O_NONBLOCK)
    {
        return 0;
    }
    return fcntl(fd, F_SETFL, flags | O_NONBLOCK);
}

