#include <stdint.h>
#include <stdlib.h>
#include <string.h>

#include "ym_slide_filter.h"
#include "ym_log.h"

int32_t sliding_average_filter_init(sliding_average_filter_p filter, int32_t w_size)
{
    filter->cache = (float *)calloc(w_size + 1, sizeof(float));
    if (filter->cache == NULL)
    {
        log_e("sliding_average_filter_init calloc err!");
        return -1;
    }

    filter->w_size = w_size;
    filter->sum = 0;
    filter->head = 0;
    filter->count = 0;

    return 0;
}

sliding_average_filter_p sliding_average_filter_create(int32_t w_size)
{
    sliding_average_filter_p filter = (sliding_average_filter_p)malloc(sizeof(sliding_average_filter_t));
    if (filter != NULL)
    {
        if (sliding_average_filter_init(filter, w_size) != 0)
        {
            free(filter);
            filter = NULL;
        }
    }
    else
    {
        log_e("sliding_average_filter_create malloc err!\n");
    }

    return filter;
}

void sliding_average_filter_destroy(sliding_average_filter_p filter)
{
    free(filter->cache);
    free(filter);
}

float sliding_average_filter(sliding_average_filter_p filter, float k)
{
    filter->cache[filter->head] = k;
    filter->head = (filter->head + 1) % (filter->w_size + 1);
    filter->sum = filter->sum + k - filter->cache[filter->head];
    if (filter->count < filter->w_size)
        filter->count++;

    return (filter->sum / (float)filter->count);
}

void sliding_average_filter_reset(sliding_average_filter_p filter)
{
    filter->sum = 0.0f;
    filter->head = 0;
    filter->count = 0;
    memset(filter->cache, 0, sizeof(float) * filter->w_size);
}
