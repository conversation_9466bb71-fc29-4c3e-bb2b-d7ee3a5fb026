/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-06-12 09:32:11
 * @LastEditTime: 2024-10-12 11:12:18
 * @LastEditors: Anzhi<PERSON><PERSON>
 * @Description:
 * @FilePath: /audio_mixer_rk3308/component/ym_resamplerate.c
 * Copyright (c) 2006-2024, Yinkman Development Team
 */
#include <string.h>
#include <stdlib.h>
#include "ym_resamplerate.h"
#include "ym_audio_tools.h"
#include "ym_utilities.h"

#include "ym_log.h"

samplerate_p samplerate_new(int channels, int frames, int sample_bits, int in_rate, int out_rate, int quality)
{
    samplerate_p p_samplerate = (samplerate_p)malloc(sizeof(samplerate_t));
    if (!p_samplerate)
    {
        log_e("malloc samplerate_t failed");
        return NULL;
    }
    memset(p_samplerate, 0, sizeof(samplerate_t));
    p_samplerate->channels = channels;
    p_samplerate->in_frames = frames;
    p_samplerate->sample_bits = sample_bits;
    p_samplerate->in_rate = in_rate;
    p_samplerate->out_rate = out_rate;
    p_samplerate->out_frames = (int)((frames * out_rate / (double)in_rate) + 0.5);

    p_samplerate->raw_data = malloc(frames * channels * sample_bits / 8);
    p_samplerate->pf_raw_data = malloc(frames * channels * sizeof(float));

    p_samplerate->output_buffer_size = (int)((frames * out_rate / (double)in_rate) + 0.5) * channels;
    p_samplerate->out_data = malloc(p_samplerate->output_buffer_size * 4);
    p_samplerate->pf_out_data = malloc(p_samplerate->output_buffer_size * sizeof(float));

    if ((p_samplerate->raw_data == NULL) ||
        (p_samplerate->pf_raw_data == NULL) ||
        (p_samplerate->out_data == NULL) ||
        (p_samplerate->pf_out_data == NULL))
    {
        log_e("malloc failed");
        if (p_samplerate->raw_data)
        {
            free(p_samplerate->raw_data);
        }
        if (p_samplerate->pf_raw_data)
        {
            free(p_samplerate->pf_raw_data);
        }
        if (p_samplerate->out_data)
        {
            free(p_samplerate->out_data);
        }
        if (p_samplerate->pf_out_data)
        {
            free(p_samplerate->pf_out_data);
        }
        free(p_samplerate);
        return NULL;
    }
    p_samplerate->data = (SRC_DATA){
        .data_in = p_samplerate->pf_raw_data,
        .data_out = p_samplerate->pf_out_data,
        .input_frames = frames,
        .output_frames = p_samplerate->output_buffer_size / channels,
        .input_frames_used = 0,
        .output_frames_gen = 0,
        .end_of_input = 0, // 始终持续转换
        .src_ratio = (double)out_rate / in_rate,
    };

    p_samplerate->state = src_new(quality, channels, NULL);
    if (!p_samplerate->state)
    {
        log_e("src_new failed");
        free(p_samplerate->raw_data);
        free(p_samplerate->pf_raw_data);
        free(p_samplerate->out_data);
        free(p_samplerate->pf_out_data);
        free(p_samplerate);
        return NULL;
    }
    p_samplerate->mutex = (pthread_mutex_t *)malloc(sizeof(pthread_mutex_t));
    pthread_mutex_init(p_samplerate->mutex, NULL);
    log_i("samplerate_new success");
    return p_samplerate;
}

int samplerate_process(samplerate_p p_samplerate)
{
    int error;
    pthread_mutex_lock(p_samplerate->mutex);
    if (!p_samplerate)
    {
        log_e("invalid parameter");
        pthread_mutex_unlock(p_samplerate->mutex);
        return -1;
    }

    if (p_samplerate->sample_bits == 16)
    {
        int16_to_float((int16_t *)p_samplerate->raw_data, p_samplerate->pf_raw_data, p_samplerate->in_frames * p_samplerate->channels);
    }
    else
    {
        int32_to_float((int32_t *)p_samplerate->raw_data, p_samplerate->pf_raw_data, p_samplerate->in_frames * p_samplerate->channels);
    }

    // 进行采样率转换
    error = src_process(p_samplerate->state, &p_samplerate->data);
    if (error)
    {
        log_e("Error: %s", src_strerror(error));
        pthread_mutex_unlock(p_samplerate->mutex);
        return -1;
    }

    if (p_samplerate->sample_bits == 16)
    {
        float_to_int16(p_samplerate->pf_out_data, (int16_t *)p_samplerate->out_data, p_samplerate->data.output_frames_gen * p_samplerate->channels);
    }
    else
    {
        float_to_int32(p_samplerate->pf_out_data, (int32_t *)p_samplerate->out_data, p_samplerate->data.output_frames_gen * p_samplerate->channels);
    }
    pthread_mutex_unlock(p_samplerate->mutex);
    return p_samplerate->data.output_frames_gen;
}

void samplerate_free(samplerate_p p_samplerate)
{
    pthread_mutex_t *mutex = p_samplerate->mutex;
    pthread_mutex_lock(mutex);
    if (p_samplerate)
    {
        if (p_samplerate->raw_data)
        {
            free(p_samplerate->raw_data);
        }
        if (p_samplerate->pf_raw_data)
        {
            free(p_samplerate->pf_raw_data);
        }
        if (p_samplerate->out_data)
        {
            free(p_samplerate->out_data);
        }
        if (p_samplerate->pf_out_data)
        {
            free(p_samplerate->pf_out_data);
        }
        if (p_samplerate->state)
        {
            src_delete(p_samplerate->state);
        }
        free(p_samplerate);
        p_samplerate = NULL;
    }
    pthread_mutex_unlock(mutex);

    pthread_mutex_lock(mutex);
    pthread_mutex_unlock(mutex);

    pthread_mutex_destroy(mutex);
    free(mutex);
}
