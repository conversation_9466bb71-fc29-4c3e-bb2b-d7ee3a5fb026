#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <stdint.h>

#include "ym_mem_pool.h"
#include "ym_log.h"

typedef struct ym_mem_pool
{
    void *pool;
    uint8_t *markers;
    uint32_t pool_sz;
    uint32_t item_sz;
    uint32_t currentIndex;
    pthread_mutex_t mutex;
} ym_mem_pool_t, *ym_mem_pool_p;

void *ym_mem_pool_init(uint32_t pool_sz, uint32_t item_sz)
{
    ym_mem_pool_p pool_id = malloc(sizeof(ym_mem_pool_t));
    if (pool_id == NULL)
    {
        log_e("pool struct malloc err.");
        return NULL;
    }

    pool_id->pool = calloc(item_sz * pool_sz, sizeof(uint8_t));
    if (pool_id->pool == NULL)
    {
        free(pool_id);
        log_e("pool data malloc err.");
        return NULL;
    }

    pool_id->markers = (uint8_t *)calloc(pool_sz, sizeof(uint8_t));
    if (pool_id->markers == NULL)
    {
        free(pool_id->pool);
        free(pool_id);
        log_e("pool markers malloc err.");
        return NULL;
    }

    pool_id->pool_sz = pool_sz;
    pool_id->item_sz = item_sz;
    pool_id->currentIndex = 0;
    pthread_mutex_init(&pool_id->mutex, NULL);

    return pool_id;
}

void *ym_mem_pool_alloc(void *id)
{
    ym_mem_pool_p pool_id = (ym_mem_pool_p)id;
    void *p = NULL;
    uint32_t index;

    pthread_mutex_lock(&pool_id->mutex);
    for (uint32_t i = 0; i < pool_id->pool_sz; i++)
    {
        index = (pool_id->currentIndex + i) % (pool_id->pool_sz);

        if (pool_id->markers[index] == 0u)
        {
            pool_id->markers[index] = 1u;
            p = (void *)((size_t)(pool_id->pool) + (index * pool_id->item_sz));
            pool_id->currentIndex = (index + 1u) % (pool_id->pool_sz);
            break;
        }
    }
    pthread_mutex_unlock(&pool_id->mutex);

    return p;
}

void ym_mem_pool_free(void *id, void *block)
{
    uint32_t index;
    ym_mem_pool_p pool_id = (ym_mem_pool_p)id;

    pthread_mutex_lock(&pool_id->mutex);

    if (block < pool_id->pool)
        goto __exit;

    index = (size_t)block - (size_t)(pool_id->pool);
    if ((index % pool_id->item_sz) != 0u)
        goto __exit;

    index = index / pool_id->item_sz;
    if (index >= pool_id->pool_sz)
        goto __exit;

    pool_id->markers[index] = 0u;

__exit:
    pthread_mutex_unlock(&pool_id->mutex);
}

void ym_mem_pool_destroy(void *id)
{
    ym_mem_pool_p pool_id = (ym_mem_pool_p)id;
    pthread_mutex_lock(&pool_id->mutex);
    if (pool_id != NULL)
    {
        if (pool_id->pool != NULL)
        {
            free(pool_id->pool);
        }

        if (pool_id->markers != NULL)
        {
            free(pool_id->markers);
        }
        pthread_mutex_unlock(&pool_id->mutex);
        pthread_mutex_destroy(&pool_id->mutex);
        free(pool_id);
    }
}

void ym_mem_pool_clear(void *id)
{
    ym_mem_pool_p pool_id = (ym_mem_pool_p)id;
    pthread_mutex_lock(&pool_id->mutex);
    memset(pool_id->markers, 0u, pool_id->pool_sz);
    pthread_mutex_unlock(&pool_id->mutex);
}
