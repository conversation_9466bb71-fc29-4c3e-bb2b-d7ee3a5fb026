#include <stdint.h>

#include "ym_startup.h"
#include "ym_log.h"

static inline void _ym_init_(ym_init_syscall_t *start, ym_init_syscall_t *end)
{
    ym_init_syscall_t *index = (ym_init_syscall_t *)start;
    int32_t ret = 0;
    for (; (unsigned long)index < (unsigned long)end; index++)
    {
        if (index)
        {
            ret = index->func();
            if (ret != 0)
            {
                log_e("name:%s\taddr:%p", index->name, index->func);
                return;
            }
        }
    }
}

static inline void ym_low_level_init(void)
{
    extern const ym_init_syscall_t __start_ykm_fn0;
    extern const ym_init_syscall_t __stop_ykm_fn0;
    _ym_init_((ym_init_syscall_t *)&__start_ykm_fn0, (ym_init_syscall_t *)&__stop_ykm_fn0);
}

static inline void ym_hw_init(void)
{
    extern const ym_init_syscall_t __start_ykm_fn1;
    extern const ym_init_syscall_t __stop_ykm_fn1;
    _ym_init_((ym_init_syscall_t *)&__start_ykm_fn1, (ym_init_syscall_t *)&__stop_ykm_fn1);
}

static inline void ym_sem_init(void)
{
    extern const ym_init_syscall_t __start_ykm_fn2;
    extern const ym_init_syscall_t __stop_ykm_fn2;
    _ym_init_((ym_init_syscall_t *)&__start_ykm_fn2, (ym_init_syscall_t *)&__stop_ykm_fn2);
}

static inline void ym_mem_init(void)
{
    extern const ym_init_syscall_t __start_ykm_fn3;
    extern const ym_init_syscall_t __stop_ykm_fn3;
    _ym_init_((ym_init_syscall_t *)&__start_ykm_fn3, (ym_init_syscall_t *)&__stop_ykm_fn3);
}

static inline void ym_stream_init(void)
{
    extern const ym_init_syscall_t __start_ykm_fn4;
    extern const ym_init_syscall_t __stop_ykm_fn4;
    _ym_init_((ym_init_syscall_t *)&__start_ykm_fn4, (ym_init_syscall_t *)&__stop_ykm_fn4);
}

static inline void ym_component_init(void)
{
    extern const ym_init_syscall_t __start_ykm_fn5;
    extern const ym_init_syscall_t __stop_ykm_fn5;
    _ym_init_((ym_init_syscall_t *)&__start_ykm_fn5, (ym_init_syscall_t *)&__stop_ykm_fn5);
}

static inline void ym_app_before_init(void)
{
    extern const ym_init_syscall_t __start_ykm_fn6;
    extern const ym_init_syscall_t __stop_ykm_fn6;
    _ym_init_((ym_init_syscall_t *)&__start_ykm_fn6, (ym_init_syscall_t *)&__stop_ykm_fn6);
}

static inline void ym_app_init(void)
{
    extern const ym_init_syscall_t __start_ykm_fn7;
    extern const ym_init_syscall_t __stop_ykm_fn7;
    _ym_init_((ym_init_syscall_t *)&__start_ykm_fn7, (ym_init_syscall_t *)&__stop_ykm_fn7);
}

void ym_init(void)
{
    ym_low_level_init();
    ym_hw_init();
    ym_sem_init();
    ym_mem_init();
#ifndef SYSTEM_NO_AUDIO_STREAM
    ym_stream_init();
    ym_component_init();
#endif /* SYSTEM_NO_AUDIO_STREAM */
    ym_app_before_init();
    ym_app_init();
}

static inline void _ym_exit_(void)
{
    extern const ym_init_syscall_t __start_ykm_fn8;
    extern const ym_init_syscall_t __stop_ykm_fn8;
    _ym_init_((ym_init_syscall_t *)&__start_ykm_fn8, (ym_init_syscall_t *)&__stop_ykm_fn8);
}

void ym_exit(void)
{
    _ym_exit_();
}
