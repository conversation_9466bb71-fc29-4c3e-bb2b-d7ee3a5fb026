/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-01-15 15:44:27
 * @LastEditTime: 2024-10-12 10:06:15
 * @LastEditors: An<PERSON><PERSON>o
 * @Description: 有限状态机框架
 * @FilePath: /audio_mixer_rk3308/component/ym_fms.c
 * Copyright (c) 2006-2024, Yinkman Development Team
 */

#include <string.h>
#include <stdlib.h>
#include <errno.h>
#include <signal.h>

#include "ym_thread.h"
#include "ym_fms.h"
#include "ym_mem_pool.h"
#include "ym_log.h"

static void fms_state_timeout(void *param);

/**
 * @description: 创建状态机
 * @param {state_node_t} init_state,状态机初始节点
 * @param {char} *name,状态机名称
 * @return {state_machine_t} fms,RT_NULL失败，其他成功
 * @author: <PERSON>hic<PERSON>.An
 */
state_machine_t fms_create(state_node_t init_state, const char *name)
{
    state_machine_t fms = (state_machine_t)malloc(sizeof(state_machine));
    uint32_t name_len = strlen(name);
    char *state_timer_name = NULL, *action_timer_name = NULL;

    state_timer_name = (char *)malloc(name_len + 2);
    action_timer_name = (char *)malloc(name_len + 2);

    if (fms && state_timer_name && action_timer_name)
    {
        memset(fms, 0, sizeof(state_machine));
        memset(state_timer_name, 0, name_len + 2);
        memset(action_timer_name, 0, name_len + 2);

        memcpy(state_timer_name, name, name_len);
        state_timer_name[name_len] = 's';
        memcpy(action_timer_name, name, name_len);
        action_timer_name[name_len] = 'a';

        fms->event_pool = ym_mem_pool_init(5, sizeof(state_event));

        pthread_mutex_init(&fms->event_queue_mutex, NULL);

        sem_init(&fms->event_queue_sem, 0, 0);
        fms->cur_state = init_state;

        memcpy(fms->name, name, name_len > YM_FMS_NAME_MAX ? YM_FMS_NAME_MAX : name_len);
        fms->state_timer = ym_timer_create(state_timer_name,
                                           fms_state_timeout,
                                           (void *)fms,
                                           1,
                                           1000); // 1s
        fms->event_queue = NULL;

        if (init_state->_state_task)
        {
            // 创建当前状态的线程函数
            fms->state_thread_handle = ym_thread_create(init_state->name,
                                                        default_thread_cpuid,
                                                        default_thread_prio,
                                                        init_state->_state_task,
                                                        (void *)init_state->user_data);
            if (fms->state_thread_handle)
                ym_thread_run(fms->state_thread_handle);
            else
                log_e("fms_create ym_thread_create err.");
        }

        if (init_state->state_timeout != 0xffffffff)
        {
            ym_timer_set_time(fms->state_timer, init_state->state_timeout);
            ym_timer_start(fms->state_timer);
        }
        if (init_state->enter)
            init_state->enter(init_state);
    }
    else
    {
        log_e("malloc err.");
        exit(0);
    }
    return fms;
}

static void fms_state_timeout(void *param)
{
    state_machine_t fms = (state_machine_t)param;
    // 执行当前状态节点的超时回调函数
    if (fms->cur_state->timeout_callback)
        fms->cur_state->timeout_callback(fms->cur_state);
    fms_post_event(fms, STATE_TIMEOUT_EVENT);
}

int32_t state_add_event(state_node_t node, state_api_t api)
{
    state_api_t pos = node->api;
    while (pos)
    {
        if (pos->event == api->event)
        {
            log_i("%s node have this event:%d", node->name, api->event);
            return 0;
        }
        pos = pos->next;
    }
    api->next = node->api;
    node->api = api;
    return 0;
}

void fms_post_event(state_machine_t fms, uint32_t event)
{
    state_event_t state_event = (state_event_t)ym_mem_pool_alloc(fms->event_pool);
    if (state_event)
    {
        state_event->event = event;
        pthread_mutex_lock(&fms->event_queue_mutex);
        DL_APPEND(fms->event_queue, state_event);
        pthread_mutex_unlock(&fms->event_queue_mutex);
        sem_post(&fms->event_queue_sem);
        log_i("%s fms post event:%d.", fms->name, event);
    }
    else
    {
        log_e("%s fms event pool full.", fms->name);
    }
}

static int fms_event_recv(state_machine_t fms, uint32_t *event)
{
    state_event_t state_event;
    int ret = 0;
    sem_wait(&fms->event_queue_sem);
    pthread_mutex_lock(&fms->event_queue_mutex);
    state_event = fms->event_queue;
    if (state_event)
    {
        DL_DELETE(fms->event_queue, state_event);
        *event = state_event->event;
    }
    else
    {
        ret = 1;
    }
    pthread_mutex_unlock(&fms->event_queue_mutex);

    if (state_event)
        ym_mem_pool_free(fms->event_pool, state_event);

    return ret;
}

void fms_poll(state_machine_t fms)
{
    uint32_t event;

    if (fms_event_recv(fms, &event) == 0)
    {
        state_api_t pos = fms->cur_state->api;
        while (pos)
        {
            if (pos->event == event)
            {
                log_i("%s post event:%d", fms->name, event);

                // 判断当前状态是否和下一个状态一致，如果一致不执行进入、离开函数
                if (fms->cur_state != pos->next_node)
                {
                    ym_timer_stop(fms->state_timer);

                    // 如果当前状态有线程函数，则停止，并等待返回
                    if (fms->cur_state->_state_task && fms->state_thread_handle)
                    {
                        ym_thread_destroy(fms->state_thread_handle);
                        fms->state_thread_handle = 0;
                    }

                    // 离开当前状态
                    if (fms->cur_state->leave)
                        fms->cur_state->leave(fms->cur_state);

                    // 是否为特殊事件
                    if (pos->event_action)
                        pos->event_action(fms->cur_state);

                    // 进入下个状态
                    log_i("%s to %s", fms->cur_state->name, pos->next_node->name);
                    fms->cur_state = pos->next_node;
                    if (fms->cur_state->enter)
                        fms->cur_state->enter(fms->cur_state);

                    if (fms->cur_state->state_timeout != 0xffffffff)
                    {
                        ym_timer_set_time(fms->state_timer, fms->cur_state->state_timeout);
                        ym_timer_start(fms->state_timer);
                    }

                    if (fms->cur_state->_state_task)
                    {
                        // 创建当前状态的线程函数
                        fms->state_thread_handle = ym_thread_create(pos->next_node->name,
                                                                    default_thread_cpuid,
                                                                    default_thread_prio,
                                                                    fms->cur_state->_state_task,
                                                                    (void *)fms->cur_state->user_data);
                        if (fms->state_thread_handle)
                            ym_thread_run(fms->state_thread_handle);
                        else
                            log_e("fms_create ym_thread_create err.");
                    }
                }

                return;
            }
            pos = pos->next;
        }
    }
}

uint32_t get_fms_state_id(state_machine_t fms)
{
    return fms->cur_state->id;
}
