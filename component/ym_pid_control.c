#include <stdlib.h>
#include <math.h>

#include "ym_pid_control.h"

pid_control_p pid_control_create(double p, double i, double d, double target, double threshold, double just_area)
{
    pid_control_p pp = (pid_control_p)malloc(sizeof(pid_control_t));
    if (pp != NULL)
    {
        pp->p = p;
        pp->i = i;
        pp->d = d;
        pp->integral = 0.0;
        pp->target = target;
        pp->err_last = 0.0;
        pp->threshold = threshold;
        pp->just_area = just_area;
    }

    return pp;
}

double pid_control_process(pid_control_p ppid_control, double d)
{
    double err = (ppid_control->target - d);
    double ret;

    if (fabs(err) < fabs(ppid_control->threshold))
        return 0.0f;

    if (fabs(err) > fabs(ppid_control->just_area))
        return 0.0f;

    ppid_control->integral += err;
    ret = err * ppid_control->p +
          ppid_control->i * ppid_control->integral +
          ppid_control->d * (err - ppid_control->err_last);
    ppid_control->err_last = err;

    return ret;
}

void pid_control_reset(pid_control_p ppid_control)
{
    ppid_control->err_last = 0;
    ppid_control->integral = 0;
}

void pid_control_destroy(pid_control_p ppid_control)
{
    free(ppid_control);
}
