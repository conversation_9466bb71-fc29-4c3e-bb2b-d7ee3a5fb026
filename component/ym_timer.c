#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <time.h>
#include <pthread.h>
#include <stdint.h>
#include <string.h>
#include <stdbool.h>

#include "ym_timer.h"
#include "ym_startup.h"
#include "utlist.h"
#include "ym_shell.h"
#include "ym_mem_pool.h"
#include "ym_log.h"

typedef struct ym_timer_list
{
    void *timer_pool;
    ym_timer_p activated;
    ym_timer_p all_timer;
    pthread_mutex_t mutex;
    pthread_mutex_t all_mutex;
} ym_timer_list_t;

static ym_timer_list_t ym_timer_head;

static int32_t cmp_func(ym_timer_p a, ym_timer_p b)
{
    return (int32_t)(a->timeout_tick - b->timeout_tick);
}

ym_timer_p ym_timer_create(const char *name,
                           ym_timer_cb_t timeout_func,
                           void *param,
                           uint32_t is_one_shot,
                           uint32_t timeout_tick_ms)
{
    ym_timer_p ptimer = ym_mem_pool_alloc(ym_timer_head.timer_pool);
    if (ptimer == NULL)
    {
        log_e("mem pool malloc err.");
        return NULL;
    }

    memset(ptimer, 0, sizeof(ym_timer_t));
    ptimer->name = name;
    ptimer->timeout_func = timeout_func;
    if (param == NULL)
        ptimer->parameter = ptimer;
    else
        ptimer->parameter = param;
    ptimer->is_one_shot = is_one_shot;
    ptimer->init_tick = timeout_tick_ms;
    ptimer->timeout_tick = (~0);
    ptimer->is_actieved = false;
    pthread_mutex_init(&ptimer->mutex, NULL);

    pthread_mutex_lock(&ym_timer_head.all_mutex);
    DL_APPEND2(ym_timer_head.all_timer, ptimer, all_prev, all_next);
    pthread_mutex_unlock(&ym_timer_head.all_mutex);
    return ptimer;
}

int32_t ym_timer_start(ym_timer_p ptimer)
{
    if (!ptimer)
    {
        log_e("ptimer is null.");
        return -1;
    }

    pthread_mutex_lock(&ptimer->mutex);
    if (ptimer->is_actieved)
    {
        // stop timer first
        pthread_mutex_unlock(&ptimer->mutex);
        ym_timer_stop(ptimer);
        pthread_mutex_lock(&ptimer->mutex);
    }

    ptimer->timeout_tick = ym_tick_get() + ptimer->init_tick;
    ptimer->is_actieved = true;
    pthread_mutex_unlock(&ptimer->mutex);

    pthread_mutex_lock(&ym_timer_head.mutex);
    DL_INSERT_INORDER(ym_timer_head.activated, ptimer, cmp_func);
    pthread_mutex_unlock(&ym_timer_head.mutex);

    return 0;
}

int32_t ym_timer_stop(ym_timer_p ptimer)
{
    pthread_mutex_lock(&ptimer->mutex);
    if (ptimer->is_actieved)
    {
        ptimer->is_actieved = false;
        pthread_mutex_unlock(&ptimer->mutex);
    }
    else
    {
        pthread_mutex_unlock(&ptimer->mutex);
        return 0;
    }

    pthread_mutex_lock(&ym_timer_head.mutex);
    DL_DELETE(ym_timer_head.activated, ptimer);
    pthread_mutex_unlock(&ym_timer_head.mutex);
    return 0;
}

int32_t ym_timer_set_time(ym_timer_p ptimer, uint32_t tick)
{
    pthread_mutex_lock(&ptimer->mutex);
    if (ptimer->is_actieved)
    {
        // 如果被设置timer正在运行，则先停止，再设置，再开始
        ptimer->is_actieved = false;
        ptimer->init_tick = tick;
        pthread_mutex_unlock(&ptimer->mutex);

        pthread_mutex_lock(&ym_timer_head.mutex);
        DL_DELETE(ym_timer_head.activated, ptimer);
        pthread_mutex_unlock(&ym_timer_head.mutex);

        ym_timer_start(ptimer);
    }
    else
    {
        ptimer->init_tick = tick;
        pthread_mutex_unlock(&ptimer->mutex);
    }
    return 0;
}

void ym_timer_destory(ym_timer_p ptimer)
{
    pthread_mutex_lock(&ym_timer_head.all_mutex);
    DL_DELETE2(ym_timer_head.all_timer, ptimer, all_prev, all_next);
    pthread_mutex_unlock(&ym_timer_head.all_mutex);

    ym_timer_stop(ptimer);
    pthread_mutex_destroy(&ptimer->mutex);
    ym_mem_pool_free(ym_timer_head.timer_pool, ptimer);
}

void *ym_timer_thread_handle(void *arg)
{
    ym_timer_p pos, tmp;

    for (;;)
    {
        usleep(1000);
        pthread_mutex_lock(&ym_timer_head.mutex);
        for ((pos) = (ym_timer_head.activated); pos;)
        {
            pthread_mutex_lock(&pos->mutex);
            if (pos->timeout_tick > ym_tick_get())
            {
                pthread_mutex_unlock(&pos->mutex);
                break;
            }
            else
            {
                tmp = pos->next;
                DL_DELETE(ym_timer_head.activated, pos);
                if (pos->is_one_shot)
                {
                    pos->is_actieved = false;
                    pos->timeout_tick = ~(0);
                }
                else
                {
                    pos->timeout_tick = ym_tick_get() + pos->init_tick;
                    DL_INSERT_INORDER(ym_timer_head.activated, pos, cmp_func);
                }
                pthread_mutex_unlock(&pos->mutex);

                pthread_mutex_unlock(&ym_timer_head.mutex);
                pos->timeout_func(pos->parameter);
                pthread_mutex_lock(&ym_timer_head.mutex);

                (pos) = tmp;
            }
        }
        pthread_mutex_unlock(&ym_timer_head.mutex);
    }

    return NULL;
}

uint64_t ym_tick_get(void)
{
    struct timespec ts1;
    // clock_gettime(CLOCK_REALTIME, &ts1);
    clock_gettime(CLOCK_MONOTONIC, &ts1);
    return (uint64_t)((ts1.tv_sec * 1000) + (ts1.tv_nsec / 1000000));
}

uint64_t ym_tick_get_us(void)
{
    struct timespec ts1;
    // clock_gettime(CLOCK_REALTIME, &ts1);
    clock_gettime(CLOCK_MONOTONIC, &ts1);
    return (uint64_t)((ts1.tv_sec * 1000 * 1000) + (ts1.tv_nsec / 1000));
}

uint64_t ym_tick_get_ns(void)
{
    struct timespec ts1;
    // clock_gettime(CLOCK_REALTIME, &ts1);
    clock_gettime(CLOCK_MONOTONIC, &ts1);
    return (uint64_t)((ts1.tv_sec * 1000 * 1000 * 1000) + (ts1.tv_nsec));
}

struct tm *convertDateTime(const char *dateStr, const char *timeStr)
{
    static struct tm tmTime;
    memset(&tmTime, 0, sizeof(struct tm));

    char monthStr[4];
    int32_t day, year, hour, minute, second;

    sscanf(dateStr, "%s %d %d", monthStr, &day, &year);
    sscanf(timeStr, "%d:%d:%d", &hour, &minute, &second);

    static const char *months[] = {"Jan", "Feb", "Mar", "Apr", "May", "Jun",
                                   "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};
    for (int32_t i = 0; i < 12; i++)
    {
        if (strcmp(monthStr, months[i]) == 0)
        {
            tmTime.tm_mon = i;
            break;
        }
    }

    tmTime.tm_mday = day;
    tmTime.tm_year = year - 1900;
    tmTime.tm_hour = hour;
    tmTime.tm_min = minute;
    tmTime.tm_sec = second;

    return &tmTime;
}

// 函数用于将默认格式的日期字符串转换为 "yyyy-mm-dd" 的格式
void convertDate(const char *inputDate, char *outputDate)
{
    int32_t month;
    int32_t day;
    int32_t year;

    // 从默认格式中提取月、日、年
    sscanf(inputDate, "%*s %d %d", &day, &year);

    // 将月份缩写转换为数字
    const char *monthAbbreviation = inputDate;
    if (strncmp(monthAbbreviation, "Jan", 3) == 0)
        month = 1;
    else if (strncmp(monthAbbreviation, "Feb", 3) == 0)
        month = 2;
    else if (strncmp(monthAbbreviation, "Mar", 3) == 0)
        month = 3;
    else if (strncmp(monthAbbreviation, "Apr", 3) == 0)
        month = 4;
    else if (strncmp(monthAbbreviation, "May", 3) == 0)
        month = 5;
    else if (strncmp(monthAbbreviation, "Jun", 3) == 0)
        month = 6;
    else if (strncmp(monthAbbreviation, "Jul", 3) == 0)
        month = 7;
    else if (strncmp(monthAbbreviation, "Aug", 3) == 0)
        month = 8;
    else if (strncmp(monthAbbreviation, "Sep", 3) == 0)
        month = 9;
    else if (strncmp(monthAbbreviation, "Oct", 3) == 0)
        month = 10;
    else if (strncmp(monthAbbreviation, "Nov", 3) == 0)
        month = 11;
    else if (strncmp(monthAbbreviation, "Dec", 3) == 0)
        month = 12;
    else
        month = 0;

    // 将转换后的日期写入输出字符串
    sprintf(outputDate, "%04d-%02d-%02d", year, month, day);
}

static int32_t ym_timer_sem_init(void)
{
    pthread_mutex_init(&ym_timer_head.mutex, NULL);
    pthread_mutex_init(&ym_timer_head.all_mutex, NULL);
    log_i("ym_timer_sem_init successful.");
    return 0;
}
INIT_SEM_EXPORT(ym_timer_sem_init);

static int32_t ym_timer_mem_init(void)
{
    ym_timer_head.timer_pool = ym_mem_pool_init(20, sizeof(ym_timer_t));
    if (ym_timer_head.timer_pool == NULL)
    {
        log_e("mem pool malloc err.");
        return -1;
    }

    log_i("ym_timer_mem_init successful.");
    return 0;
}
INIT_MEM_EXPORT(ym_timer_mem_init);

static int32_t list_timer(void)
{
    ym_timer_p pos;
    printf("current system tick:%-10lu\n", ym_tick_get());
    printf(" %-10s\t%-10s\t%-10s\t%-10s\n", "timer", "period", "timeout", "activated");
    printf("----------------------------------------------------------\n");
    pthread_mutex_lock(&ym_timer_head.all_mutex);
    DL_FOREACH2(ym_timer_head.all_timer, pos, all_next)
    {
        printf(" %-10s\t%-10lu\t%-10lu\t%-10s\n",
               pos->name,
               pos->init_tick,
               pos->timeout_tick == ~(0) ? 0 : pos->timeout_tick,
               pos->is_actieved ? "TRUE" : "FALSE");
    }
    pthread_mutex_unlock(&ym_timer_head.all_mutex);
    return 0;
}
MSH_CMD_EXPORT(list_timer, list timer in system);
