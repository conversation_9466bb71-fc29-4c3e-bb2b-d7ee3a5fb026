#!/bin/bash

target=()
for dir in "platform"/*/; do
    if [ -d "$dir" ] && [[ $(basename "$dir") != _* ]]; then
        target+=("$(basename "$dir")")
    fi
done

# 显示可用平台列表到stderr，这样不会被捕获到变量中
echo "Available platforms:" >&2
tar_index=1
for i in "${target[@]}"; do
    echo "$tar_index: $i" >&2
    ((tar_index++))
done
echo "$tar_index: build all boards program" >&2

# 检查是否有输入参数
if [ $# -gt 0 ]; then
    sel_platform=$1
    echo "Using parameter: platform $sel_platform" >&2
else
    sel_platform=0
    echo -n "Select platform(timeout 10s):" >&2
    read -t 10 sel_platform
fi

if [ "$sel_platform" == "" ]; then
    echo -e "\nno selection, all platforms will be compiled" >&2
    ((sel_platform = $tar_index))
    selected_product="all"
elif [ $sel_platform -eq $tar_index ]; then
    echo -e "\ntotal selection, all platforms will be compiled" >&2
    selected_product="all"
elif [ $sel_platform -gt $tar_index ] || [ $sel_platform -eq 0 ]; then
    echo "Wrong platform" >&2
    exit 1
else
    echo "the ${target[$sel_platform-1]} will be compiled" >&2
    selected_product="${target[$sel_platform-1]}"
fi

ym_plat=`grep -n 'YM_AUDIO_MIXER_COMPILE_PLATFORM=' ~/.bashrc`
#echo $ym_line
ym_line=`echo ${ym_plat%:*}`
#echo $ym_line
#echo ${ym_plat#*:}
if [ "$ym_line" == "" ]; then
    echo "export YM_AUDIO_MIXER_COMPILE_PLATFORM=$sel_platform" >> ~/.bashrc
else
    sed -i "${ym_line}s/=.*/=${sel_platform}/g" ~/.bashrc
fi
#echo $YM_AUDIO_MIXER_COMPILE_PLATFORM
source ~/.bashrc
export YM_AUDIO_MIXER_COMPILE_PLATFORM=$sel_platform
#echo $YM_AUDIO_MIXER_COMPILE_PLATFORM

# 输出选择的产品名称，供调用脚本使用
echo "SELECTED_PRODUCT:$selected_product"
