#!/bin/bash
###
 # @Author: <PERSON><PERSON><PERSON><PERSON>
 # @Date: 2023-07-06 16:19:03
 # @LastEditTime: 2023-08-15 15:19:47
 # @LastEditors: Anzhichao
 # @Description: 
 # @FilePath: /rk3308-work/set_acodec_alc.sh
 # Copyright (c) 2006-2024, Yinkman Development Team
### 

# Define an array of mixer control numbers
mixer_controls=(21 22 23 24 25 26 27 28)

# Loop through the array and set the value for each mixer control
for control in "${mixer_controls[@]}"; do
    tinymix set "$control" $1
done
