#!/bin/sh

# 删除旧的 tar 文件和临时目录
rm -rf /data/ym_log_export.tar /data/ym_log_export

# 创建临时目录
mkdir -p /data/ym_log_export || { echo "Failed to create directory"; exit 1; }

# 强制同步文件系统缓存
sync

# 复制日志文件到临时目录
cp /data/ym_log/* /data/ym_log_export || { echo "Failed to copy files"; exit 1; }

# 定义临时文件
temp_file="/data/ym_log_export/file_list.txt"

# 使用 ls -tr 按修改时间由远到近列出文件，并保存到临时文件
cd /data/ym_log_export
ls -tr | while read -r file; do
    if [ -f "$file" ]; then
        echo "$file" >> "$temp_file"
    fi
done

# 根据临时文件中的文件列表进行打包
tar -cvf /data/ym_log_export.tar -C /data/ym_log_export -T "$temp_file" || { echo "Failed to create tar file"; exit 1; }

# 删除临时文件
rm -rf "$temp_file"

# 删除临时目录
rm -rf /data/ym_log_export
