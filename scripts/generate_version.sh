###
 # @Author: <PERSON><PERSON><PERSON><PERSON>
 # @Date: 2023-11-09 09:44:57
 # @LastEditTime: 2024-10-10 09:32:56
 # @LastEditors: Anzhichao
 # @Description: 
 # @FilePath: /audio_mixer_rk3308/scripts/generate_version.sh
 # Copyright (c) 2006-2024, Yinkman Development Team
### 
#!/bin/bash

case $1 in
  "audio_mixer_matrix_mic")
    target=0x10
    ;;
  "pcba_test")
    target=0xff
    ;;
  "wrong_board_id")
    target=0x00
    ;;
  *)
    echo "Unknown app value: $1"
    ;;
esac

# 判断当前rk3308_release_version环境变量是否存在
if [ -z "$rk3576_release_version" ]; then
  rk3576_release_version=$(git rev-parse --abbrev-ref HEAD)
  
  if [[ ! $rk3576_release_version =~ ^release_V[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    echo "当前分支 ($rk3576_release_version) 不符合 Vx.x.x 格式，开始检索父分支..."
  
    rk3576_release_version=""
  
    for commit in $(git rev-list --first-parent HEAD); do
      branches=$(git branch --contains $commit | sed 's/^[ *]*//')
      for branch in $branches; do
        if [[ $branch =~ ^release_V[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
          rk3576_release_version=$branch
          break 2
        fi
      done
    done
  
    if [ -z "$rk3576_release_version" ]; then
      echo "未找到符合条件的父分支。"
      exit 1
    else
      echo "父分支为: $rk3576_release_version"
    fi
  else
    echo "当前分支 ($rk3576_release_version) 符合 Vx.x.x 格式。"
  fi
  
  rk3576_release_version=${rk3576_release_version#release_}
  
  export rk3576_release_version
fi

version=${rk3576_release_version#V}

# 解析版本号
IFS='-' read -ra version_parts <<< "$version"
IFS='.' read -ra version_numbers <<< "${version_parts[0]}"

# 分离主版本、子版本和修订版本
major_version=${version_numbers[0]}
minor_version=${version_numbers[1]}
revision_version=${version_numbers[2]}

# 生成C头文件
cat <<EOL > include/version.h
/*
  ############################################################################
  # Don't edit this file!File automatically-generated by generate_version.sh.#
  ############################################################################
*/
#ifndef _VERSION_H_
#define _VERSION_H_

#define YM_VERSION ($major_version)
#define YM_SUBVERSION ($minor_version)
#define YM_REVISION ($revision_version)

#define YM_RK3576_HARDWARE_VERSION ($target)

#define YM_HARDWARE_VERSION (0x594D0000 | YM_RK3576_HARDWARE_VERSION)

#endif
EOL

echo "Version header file 'version.h' generated with $version"
