#!/bin/bash
###
# @Author: <PERSON><PERSON><PERSON><PERSON>
# @Date: 2024-11-25 11:30:39
 # @LastEditTime: 2024-11-25 16:06:55
 # @LastEditors: Anzhichao
# @Description:
 # @FilePath: /audio_mixer_rk3308/scripts/udisk_test.sh
# Copyright (c) 2006-2024, Yinkman Development Team
###

# 设定挂载点
MOUNT_POINT="/tmp/udisk"

test_udisk() {
    # Generate a random file
    RANDOM_FILE="/tmp/random_file.txt"
    head -c 100 /dev/urandom >"$RANDOM_FILE"

    # Copy the file to the mounted location
    cp "$RANDOM_FILE" "$MOUNT_POINT/random_file.txt"

    sync
    sync

    # Compare the files and verify
    if cmp -s "$MOUNT_POINT/random_file.txt" "$RANDOM_FILE"; then
        exit 0
    else
        exit 1
    fi
}

if mountpoint -q "$MOUNT_POINT"; then
    test_udisk
else
    exit 1
fi
