#!/bin/sh
###
# @Author: Anzhicha<PERSON>
# @Date: 2023-12-06 11:17:33
 # @LastEditTime: 2024-08-27 11:34:05
 # @LastEditors: Anzhichao
# @Description: usb device init
 # @FilePath: /audio_mixer_rk3308/scripts/usbdevice.sh
# Copyright (c) 2006-2024, Yinkman Development Team
###

usb_hid_device_config()
{
    if [ -d /sys/kernel/config/usb_gadget/ ] ; then
        ######USB HID Device config######
#        cd /sys/kernel/config/usb_gadget/
#        mkdir -p ymhid
#        cd ymhid
#        echo 0x220a > idVendor
#        echo 0x001a > idProduct
#        echo 0x0311 > bcdDevice # v1.0.0
#        echo 0x0110 > bcdUSB # USB2
#        mkdir -p strings/0x409
#        local SERIAL=$(grep Serial /proc/cpuinfo | cut -d':' -f2)
        # 写入到USB Gadget配置
#        echo ${SERIAL:-0123456789ABCDEF} > strings/0x409/serialnumber
#        echo "Yinkman HID" > strings/0x409/manufacturer
#        echo "Yinkman HID" > strings/0x409/product
#        mkdir -p configs/c.1/strings/0x409
#        echo "HID_CONFIGUE" > configs/c.1/strings/0x409/configuration
#        echo 2 > configs/c.1/MaxPower

        # Add functions here
        local func_hid="functions/hid.usb0"
        mkdir -p $func_hid
        echo 0 > $func_hid/protocol
        echo 0 > $func_hid/subclass
        echo 1 > $func_hid/report_length
        # Set HID in/out desc
#        echo -ne \\x05\\x81\\x09\\x82\\xa1\\x01\\x09\\x83\\x09\\x84\\x15\\x00\\x26\\xff\\x00\\x75\\x08\\x95\\x40\\x81\\x02\\x09\\x84\\x15\\x00\\x26\\xff\\x00\\x75\\x08\\x95\\x40\\x91\\x02\\xc0 > $func_hid/report_desc
        # HID userdefined communication report descriptor
#        echo -ne \\x05\\xff\\x09\\x00\\xa1\\x01\\x09\\x01\\x15\\x00\\x26\\x00\\xff\\x75\\x08\\x95\\x40\\x81\\x02\\x09\\x02\\x15\\x00\\x26\\x00\\xff\\x75\\x08\\x95\\x40\\x91\\x02\\xc0 > $func_hid/report_desc
        # HID volume+,volume-,mute/unmute,play/pause
        echo -ne \\x05\\x0c\\x09\\x01\\xa1\\x01\\xa1\\x00\\x09\\xe9\\x09\\xea\\x09\\xe2\\x09\\xcd\\x35\\x00\\x45\\x07\\x15\\x00\\x25\\x01\\x75\\x01\\x95\\x04\\x81\\x02\\x75\\x01\\x95\\x04\\x81\\x01\\xc0\\xc0 > $func_hid/report_desc
        # HID volume set
#        echo -ne \\x05\\x0c\\x09\\x01\\xa1\\x01\\x09\\xe0\\x15\\x00\\x25\\x64\\x75\\x07\\x95\\x01\\x81\\x22\\x95\\x01\\x75\\x01\\x81\\x03\\xc0 > $func_hid/report_desc

        ln -s functions/hid.usb0 configs/b.1/
        # End functions

#        ls /sys/class/udc > UDC
        ######USB HID Device end######
    else
        errlog "usb_gadget not found!"
        return 4
    fi
}

# read uac name from "/userdata/nonvolatile_global_cfg"
uac_name()
{
    local file="/userdata/nonvolatile_global_cfg"
    local offset=176  # 8*16+24*2
    local name=""
    local trimmed_name=""
    if [ -f ${file} ]
    then
        name=$(dd if="$file" bs=1 skip="$offset" count=24 2>/dev/null)
        trimmed_name="${name%"${name##*[![:space:]]}"}"
        if [[ "$trimmed_name" != "$name" ]]; then
            name="$trimmed_name"
        fi
    fi
    echo -en "$name"
}

usb_udisk_config()
{
    if [ -d /sys/kernel/config/usb_gadget/ ] ; then
        local func_udisk="functions/mass_storage.usb0"
        local mass_img='/userdata/mass_storage.img'
        local mass_dir='/userdata/vudisk'

        if [ ! -e ${mass_img} ]
        then
            dd if=/dev/zero of=${mass_img} bs=1M count=256
        fi
        # format disk, in case of the volume of disk was corrupted. Or the u disk can not be used.
        mkfs.vfat --codepage=850 ${mass_img}

        if [ ! -d ${mass_dir} ]
        then
            mkdir -p ${mass_dir}
        fi

        chmod 775 ${mass_dir}
        chmod +x ${mass_img}
#        mount -t vfat -o loop ${mass_img} ${mass_dir}

        if [ ! -d ${func_udisk} ]
        then
            mkdir -p $func_udisk
        fi

        echo ${mass_img} > ${func_udisk}/lun.0/file
        ln -s ${func_udisk} configs/b.1/
    fi
}

# 配置USB
usb_config() {
    cd /sys/kernel/config/usb_gadget/yinkman

    echo 0x2209 >idVendor
    echo 0x0019 >idProduct
    echo 0x0310 >bcdDevice
    #echo 0x0200 >bcdUSB # usb2.0
    echo 0x0110 >bcdUSB # usb 1.1

    mkdir -p /sys/kernel/config/usb_gadget/yinkman/strings/0x409

    SERIAL=$(grep Serial /proc/cpuinfo | cut -d':' -f2)

    # 写入到USB Gadget配置
    echo ${SERIAL:-0123456789ABCDEF} >/sys/kernel/config/usb_gadget/yinkman/strings/0x409/serialnumber

    # 根据设备树app节点判断启动哪些程序
    app_value=$(cat /proc/device-tree/app 2>/dev/null)
    nonvolatile_uac=$(uac_name)

    # 根据app属性值启动不同的应用程序
#    case $app_value in
#    "audio_mixer_wen_xiang_105" | "audio_mixer_wen_xiang_107" | "audio_mixer_wen_xiang_113")
#        echo "wx_audio" >/sys/kernel/config/usb_gadget/yinkman/strings/0x409/manufacturer
#        echo "wx_audio" >/sys/kernel/config/usb_gadget/yinkman/strings/0x409/product
#        ;;
#    'audio_mixer_yi_ou_8mic')
#        echo "ClassIn Audio" >/sys/kernel/config/usb_gadget/yinkman/strings/0x409/manufacturer
#        echo "ClassIn Audio" >/sys/kernel/config/usb_gadget/yinkman/strings/0x409/product
#        ;;
#    *)
#        echo "USB Audio" >/sys/kernel/config/usb_gadget/yinkman/strings/0x409/manufacturer
#        echo "USB Audio" >/sys/kernel/config/usb_gadget/yinkman/strings/0x409/product
#        ;;
#    esac
    echo "${nonvolatile_uac}" >/sys/kernel/config/usb_gadget/yinkman/strings/0x409/manufacturer
    echo "${nonvolatile_uac}" >/sys/kernel/config/usb_gadget/yinkman/strings/0x409/product

    mkdir -p /sys/kernel/config/usb_gadget/yinkman/configs/b.1

    echo 500 >/sys/kernel/config/usb_gadget/yinkman/configs/b.1/MaxPower
    echo 0x1 >os_desc/b_vendor_code
    echo MSFT100 >os_desc/qw_sign
    ln -s /sys/kernel/config/usb_gadget/yinkman/configs/b.1 os_desc/

    mkdir -p /sys/kernel/config/usb_gadget/yinkman/configs/b.1/strings/0x409

    echo "uac1_acm" >/sys/kernel/config/usb_gadget/yinkman/configs/b.1/strings/0x409/configuration

    if [ ! -d functions/uac1.gs0 ]
    then
        mkdir functions/uac1.gs0
    fi

    if [ ! -d functions/acm.gs6 ]
    then
        mkdir functions/acm.gs6
    fi

    if [ "$app_value" != "audio_mixer_yi_ou_8mic" ]
    then
        if [ ! -d functions/acm.gs7 ]
        then
            mkdir functions/acm.gs7
        fi
    else
        usb_hid_device_config
        if [ "$1" == "vudisk" ]
        then
            usb_udisk_config
        fi
    fi

    if [ -f functions/uac1.gs0/function_name ]
    then
        chmod 644 functions/uac1.gs0/function_name
    fi
#    case $app_value in
#    "audio_mixer_wen_xiang_105" | "audio_mixer_wen_xiang_107" | "audio_mixer_wen_xiang_113")
#        echo "wx_audio" >functions/uac1.gs0/function_name
#        ;;
#    'audio_mixer_yi_ou_8mic')
#        echo "ClassIn Audio" >functions/uac1.gs0/function_name
#        ;;
#    *)
#        echo "USB Audio" >functions/uac1.gs0/function_name
#        ;;
#    esac
    echo "${nonvolatile_uac}" >functions/uac1.gs0/function_name
    # read-only
    if [ -f functions/uac1.gs0/function_name ]
    then
        chmod 444 functions/uac1.gs0/function_name
    fi

    # associate function with config
    ln -s functions/uac1.gs0 configs/b.1
    ln -s functions/acm.gs6 configs/b.1
    if [ "$app_value" != "audio_mixer_yi_ou_8mic" ]
    then
        ln -s functions/acm.gs7 configs/b.1
    fi
}

usb_start() {
    # enable gadget by binding it to a UDC from /sys/class/udc
    cd /sys/kernel/config/usb_gadget/yinkman
    USB_UDC=$(ls /sys/class/udc/ | head -n 1)
    echo $USB_UDC > UDC
}

usb_stop() {
    cd /sys/kernel/config/usb_gadget/yinkman
    if [ -e functions/uac1.gs0 ]
    then
        echo "" > UDC
    fi
}

usb_delete() {
    cd /sys/kernel/config/usb_gadget/yinkman

    app_value=$(cat /proc/device-tree/app 2>/dev/null)

    rm configs/b.1/uac1.gs0
    rm configs/b.1/acm.gs6
    rm os_desc/b.1
    if [ "$app_value" != "audio_mixer_yi_ou_8mic" ]
    then
        rm configs/b.1/acm.gs7
    else
        rm configs/b.1/hid.usb0
        if [ -e configs/b.1/mass_storage.usb0 ]
        then
            rm configs/b.1/mass_storage.usb0
        fi
    fi
}

mkdir -p /sys/kernel/config/usb_gadget/yinkman
if [ $# -gt 1 ]
then
    ARGV2=$2
else
    ARGV2=""
fi

case "$1" in
start | "")
    usb_start
    ;;
restart)
    usb_stop
    usb_start
    ;;
stop)
    usb_stop
    ;;
config)
    usb_config $ARGV2
    ;;
delete)
    usb_delete
    ;;
*)
    echo "Usage: [start|stop|restart|config]" >&2
    exit 3
    ;;
esac
