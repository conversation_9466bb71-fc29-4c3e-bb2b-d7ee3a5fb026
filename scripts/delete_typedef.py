'''
Author: Anzhichao
Date: 2023-06-30 11:40:14
LastEditTime: 2024-01-19 18:46:18
LastEditors: Anzhichao
Description: 
FilePath: /audio_mixer_rk3308/scripts/delete_typedef.py
Copyright (c) 2006-2024, Yinkman Development Team
'''
import re

import sys

# 获取输入文件名
input_file = sys.argv[1]

# 获取输出文件名
output_file = sys.argv[2]

def remove_typedefs(source_code):
    pattern = r'typedef\s+.*?{.*?}.*?;'
    result = re.sub(pattern, '', source_code, flags=re.DOTALL)
    result = result.replace(",\n", ", ")
    result = re.sub(r'[\s\t]+', ' ', result)
    result = re.sub(r';', ';\n', result)
    return result

def remove_enum(source_code):
    pattern = r'enum\s+.*?{.*?}.*?;'
    result = re.sub(pattern, '', source_code, flags=re.DOTALL)
    result = result.replace(",\n", ", ")
    result = re.sub(r'[\s\t]+', ' ', result)
    result = re.sub(r';', ';\n', result)
    return result

# 读取源文件
with open(input_file, 'r') as file:
    source_code = file.read()

# 删除typedef语句
modified_code = remove_typedefs(source_code)
modified_code = remove_enum(modified_code)

# 写入修改后的文件
with open(output_file, 'w') as file:
    file.write(modified_code)