###
 # @Author: <PERSON><PERSON><PERSON><PERSON>
 # @Date: 2023-11-09 09:44:57
 # @LastEditTime: 2024-09-03 15:54:16
 # @LastEditors: Anzhichao
 # @Description: 
 # @FilePath: /audio_mixer_rk3308/scripts/post_build.sh
 # Copyright (c) 2006-2024, Yinkman Development Team
### 
#!/bin/bash
# YM_HARDWARE_VERSION
case $1 in
"audio_mixer_matrix_mic")
    echo -n -e "\x59\x4D\x00\x10" >> $2
    ;;
"pcba_test")
    echo -n -e "\x59\x4D\x00\xff" >> $2
    ;;
"wrong_board_id")
    echo -n -e "\x59\x4D\x00\x00" >> $2
    ;;
*)
    echo "Unknown app value: $app_value"
    ;;
esac
# sha256sum $2，将结果append 到 $2
sha256sum $2 | awk '{print $1}' >> $2

cp $2 $2_rk3576_app_${rk3576_release_version}.bin
