import re
import sys

# 获取输入文件名
input_file = sys.argv[1]

# 获取输出文件名
output_file = sys.argv[2]

output_file_head = '''\
/*
  ########################################################################
  # Don't edit this file!File automatically-generated by warrap_param.py.#
  ########################################################################
*/
#ifndef __BEAM_WARRAP_ARGS_H_
#define __BEAM_WARRAP_ARGS_H_

#include <stdint.h>
#include "beam.h"

'''
output_file_tail = '''\
#endif
'''

def extract_function_params(line):
    # 使用正则表达式提取参数声明部分
    pattern = r'\((.*?)\)'
    match = re.search(pattern, line)
    if match:
        params = match.group(1).split(',')
        return params
    return None

def process_header_file(input_file, output_file):
    # 打开输入和输出文件
    with open(input_file, 'r') as f_in, open(output_file, 'w') as f_out:
        # 遍历输入文件的每一行
        f_out.write(output_file_head)
        for line in f_in:
            line = line.strip()

            # 提取函数声明部分
            if '(' in line and ')' in line:
                # 提取函数名和参数
                function_name = line.split('(')[0].split()[-1]
                params = extract_function_params(line)

                if params:
                    # 构造结构体名和参数名
                    struct_name = f'{function_name}_args'
                    param_names = [param.split()[-1] for param in params]
                    param_types = [' '.join(param.split()[0:-1]) for param in params]
                    # 生成结构体定义
                    struct_definition = f'struct {struct_name}\n{{\n'
                    for param_name, param_type in zip(param_names, param_types):
                        struct_definition += f'    {param_type} {param_name};\n'
                    struct_definition += '};\n'

                    # 写入结构体定义到输出文件
                    f_out.write(struct_definition)
        f_out.write(output_file_tail)
        print(f'Successfully generated "{output_file}".')

# 处理头文件
process_header_file(input_file, output_file)


