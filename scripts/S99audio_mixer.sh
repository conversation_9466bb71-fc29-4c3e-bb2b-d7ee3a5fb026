#!/bin/sh
###
 # @Author: an<PERSON><PERSON><PERSON> an<PERSON>@yinkman.com
 # @Date: 2023-08-15 17:13:22
 # @LastEditors: anzhi<PERSON><PERSON> <EMAIL>
 # @LastEditTime: 2023-09-12 18:20:10
 # @FilePath: /rk3308-linux5.10/external/rkscript/S99audio_mixer.sh
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 

case "$1" in
	start|"")
		# while true
		# do
		audio-mixer-start.sh
		# 	sleep $(( 5 ))
		# done &
		;;
	restart|reload|force-reload)
		echo "Error: argument '$1' not supported" >&2
		exit 3
		;;
	stop|status)
		# No-op
		;;
	*)
		echo "Usage: [start|stop]" >&2
		exit 3
		;;
esac
