#!/bin/bash
###
 # @Author: Anzhicha<PERSON>
 # @Date: 2024-06-03 10:16:51
 # @LastEditTime: 2024-06-03 14:40:46
 # @LastEditors: Anzhichao
 # @Description: 
 # @FilePath: /audio_mixer_rk3308/scripts/check_update.sh
 # Copyright (c) 2006-2024, Yinkman Development Team
### 

# 检查是否提供了tar包文件名
if [ "$#" -ne 1 ]; then
    echo "Usage: $0 <tar-file>"
    exit 1
fi

TAR_FILE=$1

# 创建一个临时目录来解压tar包
mkdir -p /data/update_tmp
TMP_DIR="/data/update_tmp"

# 解压tar包
tar -xf "$TAR_FILE" -C "$TMP_DIR"

rm  "$TAR_FILE"

# 查找镜像文件和SHA256文件
IMAGE_FILE=$(find "$TMP_DIR" -type f -name '*.img')
SHA256_FILE=$(find "$TMP_DIR" -type f -name '*.sha256')

# 检查是否找到了镜像文件和SHA256文件
if [ -z "$IMAGE_FILE" ] || [ -z "$SHA256_FILE" ]; then
    echo "Error: Could not find image file or sha256 file in the tar package."
    rm -rf "$TMP_DIR"
    exit 1
fi

# 读取提供的SHA256值
PROVIDED_SHA256=$(cat "$SHA256_FILE")

# 计算镜像文件的SHA256值
CALCULATED_SHA256=$(sha256sum "$IMAGE_FILE" | awk '{ print $1 }')

# 比较SHA256值
if [ "$PROVIDED_SHA256" == "$CALCULATED_SHA256" ]; then
    echo "The image file is complete and its SHA256 matches."
    exit 0
else
    echo "Error: The image file is incomplete or its SHA256 does not match."
    rm -rf "$TMP_DIR"
    exit 1
fi
