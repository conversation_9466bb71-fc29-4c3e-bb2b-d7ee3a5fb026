#!/bin/bash
###
 # @Author: Anzhicha<PERSON>
 # @Date: 2023-06-30 13:20:32
 # @LastEditTime: 2024-01-19 18:46:23
 # @LastEditors: Anzhichao
 # @Description: 
 # @FilePath: /audio_mixer_rk3308/scripts/generate_beam_warrap.sh
 # Copyright (c) 2006-2024, Yinkman Development Team
### 
../../prebuilts/gcc/linux-x86/aarch64/gcc-arm-6.4.0-x86_64-aarch64-buildroot-linux-gnu/bin/aarch64-rockchip-linux-gnu-gcc -E -P ../lib_dsp/beam.h -o beam.h
python delete_typedef.py beam.h beam.h
python warrap_func.py beam.h beam_warrap.h beam_warrap.c beam
rm beam.h

../../prebuilts/gcc/linux-x86/aarch64/gcc-arm-6.4.0-x86_64-aarch64-buildroot-linux-gnu/bin/aarch64-rockchip-linux-gnu-gcc -E -P ../lib_dsp/ym_mixer.h -o mixer.h
python delete_typedef.py mixer.h mixer.h
python warrap_func.py mixer.h mixer_warrap.h mixer_warrap.c mixer
rm mixer.h
