#!/bin/bash

# mount udisk temporarily
mount_udisk()
{
	local mass_img='/userdata/mass_storage.img'
    local mass_dir='/userdata/vudisk'
    local check_mnt=`df -h | grep '/dev/loop'`
    if [ -e ${mass_img} ]
    then
    	if [ "${check_mnt}" != "" ]
    	then
			umount -t vfat ${mass_img}
        fi

        if [ ! -d ${mass_dir} ]
        then
        	mkdir -p ${mass_dir}
        	chmod 775 ${mass_dir}
        fi

        chmod +x ${mass_img}
        mount -t vfat -o loop ${mass_img} ${mass_dir}
    fi
}

umount_udisk()
{
	local mass_img='/userdata/mass_storage.img'
	local mass_dir='/userdata/vudisk'
	local check_mnt=`df -h | grep '/dev/loop'`
	if [ "${check_mnt}" != "" ]
	then
		rm -rf ${mass_dir}/*.tar;
		rm -rf ${mass_dir}/*.bin # clear disk in case of u disk is full.
		umount -t vfat ${mass_img}
    fi
}

if [ $# -lt 1 ]
then
	exit 0
fi

if [ "$1" == "upgrade" ]
then
	case $2 in
		'app' | 'APP')
			sync;
			mount_udisk;
			bin_file=`ls /userdata/vudisk/audio_mixer_*.bin`;
			if [ "${bin_file}" != "" ]
			then
				rm /userdata/upgrade;
				ls -l /userdata/vudisk;
				sha256sum ${bin_file}
				mv ${bin_file} /userdata/upgrade;
				umount_udisk;
			else
				exit 1
			fi
			;;
		'image' | 'IMAGE')
			sync;
			mount_udisk;
			tar_file=`ls /userdata/vudisk/update.tar`;
			if [ "${tar_file}" != "" ]
			then
				rm /userdata/update.tar;
				rm -rf /userdata/update_tmp;
				ls -l /userdata/vudisk;
				sha256sum ${tar_file}
				mv ${tar_file} /userdata/ ;
				umount_udisk;
			else
				exit 1
			fi
			;;
		*)
			;;
	esac
fi
