#!/bin/sh
###
 # @Author: Anzhichao
 # @Date: 2023-12-06 11:17:33
 # @LastEditTime: 2024-04-08 18:31:01
 # @LastEditors: Anzhichao
 # @Description: usb device init
 # @FilePath: /audio_mixer_rk3308/scripts/usb_adb.sh
 # Copyright (c) 2006-2024, Yinkman Development Team
### 

# 配置USB
usb_config()
{
    cd /sys/kernel/config/usb_gadget/yinkman

    echo 0x2207 > idVendor
    echo 0x0006 > idProduct
    echo 0x0310 > bcdDevice
    echo 0x0200 > bcdUSB

    mkdir -p /sys/kernel/config/usb_gadget/yinkman/strings/0x409

    SERIAL=$(grep Serial /proc/cpuinfo | cut -d':' -f2)
    echo ${SERIAL:-0123456789ABCDEF} > /sys/kernel/config/usb_gadget/yinkman/strings/0x409/serialnumber
    echo "yinkman"  > /sys/kernel/config/usb_gadget/yinkman/strings/0x409/manufacturer
    echo "audio_mixer"  > /sys/kernel/config/usb_gadget/yinkman/strings/0x409/product

    mkdir -p /sys/kernel/config/usb_gadget/yinkman/configs/b.1

    echo 500 > /sys/kernel/config/usb_gadget/yinkman/configs/b.1/MaxPower
    echo 0x1 > os_desc/b_vendor_code
    echo MSFT100 > os_desc/qw_sign
    ln -s /sys/kernel/config/usb_gadget/yinkman/configs/b.1 os_desc/

    mkdir -p /sys/kernel/config/usb_gadget/yinkman/configs/b.1/strings/0x409

    echo "adb" > /sys/kernel/config/usb_gadget/yinkman/configs/b.1/strings/0x409/configuration

    mkdir functions/ffs.adb

    mkdir -p /dev/usb-ffs/adb

    mountpoint -q /dev/usb-ffs/adb

    mount adb /dev/usb-ffs/adb -o uid=2000,gid=2000 -t functionfs

    start-stop-daemon -Sqx /usr/bin/adbd &

    for i in `seq 200`;do
		fuser -s /dev/usb-ffs/adb 2>/dev/null && break
		sleep .01
	done

    # associate function with config
    ln -s functions/ffs.adb configs/b.1
}

usb_start()
{
    # enable gadget by binding it to a UDC from /sys/class/udc
    cd /sys/kernel/config/usb_gadget/yinkman
    USB_UDC=$(ls /sys/class/udc/ | head -n 1)
    echo $USB_UDC > UDC
}

usb_stop()
{
    cd /sys/kernel/config/usb_gadget/yinkman
    echo "" > UDC
}

mkdir -p /sys/kernel/config/usb_gadget/yinkman

case "$1" in
	start|"")
        usb_start
		;;
	restart)
		usb_stop
        usb_config
        usb_start
		;;
	stop)
		usb_stop
		;;
	config)
		usb_config
		;;
	*)
		echo "Usage: [start|stop|restart|config]" >&2
		exit 3
		;;
esac