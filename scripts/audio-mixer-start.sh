#!/bin/bash
###
# @Author: an<PERSON><PERSON><PERSON> <EMAIL>
# @Date: 2023-08-16 13:27:21
 # @LastEditors: Anzhicha<PERSON>
 # @LastEditTime: 2024-11-26 13:44:15
 # @FilePath: /audio_mixer_rk3308/scripts/audio-mixer-start.sh
# @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
###

if [ ! -f "/data/timestamp" ]; then
    date +%s >/data/timestamp
else
    last_timestamp=$(cat /data/timestamp)
    date -s "$(date -d @$last_timestamp '+%Y-%m-%d %H:%M:%S')"
fi

log_file="/data/shelllog"
max_size=1048576 # 1MB in bytes

# Check if the log file exists
if [ -f "$log_file" ]; then
    # Get the size of the log file in bytes
    file_size=$(wc -c <"$log_file")

    # Compare the size with the maximum allowed size
    if [ "$file_size" -gt "$max_size" ]; then
        echo "Log file size exceeds 1MB. Deleting the file."
        rm "$log_file"
    fi
fi

if [ ! -d "/data/ym_log" ]; then
    mkdir -p /data/ym_log
fi

# 根据设备树app节点判断启动哪些程序
app_value=$(cat /proc/device-tree/app 2>/dev/null)

app_dir=/app

check_firmware_before_startup() {
    local APP_NAME=$1
    local OTA_PATH=/userdata/app
    local OTA_MNT=/userdata/audio-mixer
    local APP_PATH=/app/
    local APP_BAK=/usr/bin/audio-mixer-bak

    if [ -d $OTA_PATH ]; then
        mkdir $OTA_MNT
        if [ -d $OTA_MNT ]; then
            umount -v $APP_PATH
            if [ $? -eq 0 ]; then
                mount -t ext2 -o rw /dev/mmcblk0p8 $OTA_MNT
                cp -rf $OTA_PATH/* $OTA_MNT
                rm -rf $OTA_PATH
                chmod 555 $OTA_MNT/$APP_NAME
                umount -v $OTA_MNT
                mount -t ext2 -o ro /dev/mmcblk0p8 $APP_PATH
            else
                echo "umount $APP_PATH failed"
            fi
            rm -rf $OTA_MNT
        fi
        rm -rf $OTA_PATH
    fi

    if [ ! -e $APP_PATH$APP_NAME ]; then
        if [ -e $APP_BAK/$APP_NAME ]; then
            app_dir=$APP_BAK
        else
            echo "No app was found!"
        fi
    fi
}

# wenxiang wm520 upgradation
ver_str2num()
{
    local ver_str=$1
    local dg=${ver_str%.*}
    local de=${ver_str#*.}
    local vn=0
    if [[ "$dg" =~ ^[0-9]+$ ]]; then
        if [[ "$de" =~ ^[0-9]+$ ]]; then
            ((dg=16#$dg))
            ((de=16#$de))
            ((vn = $dg * 100 + $de))
        fi
    fi
    printf "%d" $vn
}

check_wm520_upgradation()
{
    str1="`/usr/bin/hidupd --version | grep Version`"
    #str1="Get Version:V0.5"
    echo $str1
    str1_version=${str1#*:}
    if [ "$str1_version" != "$str1" ]
    then
        str1_version=${str1_version#*V}
        version1=`ver_str2num ${str1_version}`
        echo -n "${version1}," > /userdata/wm520_version

        str2="`ls /usr/bin/APP_WX-WM520_V*.dfu`"
        #str2="/usr/bin/APP_WX-WM520_V0.04.dfu"
        echo $str2
        str2_version=${str2#*V}
        str2_version=${str2_version%.*}
        version2=`ver_str2num ${str2_version}`

        if [ $version1 -ne $version2 ]
        then
            retval=`/usr/bin/hidupd $str2`
            echo $retval
            if [ "${retval}" == "0" ]
            then
                echo -n "${version2}," > /userdata/wm520_version
            fi
        fi
        echo -n "${version2}," >> /userdata/wm520_version
    else
        echo -n "65535,0," > /userdata/wm520_version
    fi
}

if [ "$app_value" == "audio_mixer_wen_xiang_105" ] ||
   [ "$app_value" == "audio_mixer_wen_xiang_107" ] ||
   [ "$app_value" == "audio_mixer_wen_xiang_107_pa" ] ||
   [ "$app_value" == "audio_mixer_wen_xiang_113" ] ||
   [ "$app_value" == "audio_mixer_wen_xiang_128" ]
then
    is_wm520_exist=`lsusb | grep '248a:922e'`
    if [ "$is_wm520_exist" != "" ]
    then
        check_wm520_upgradation
    else
        rm /userdata/wm520_version
    fi
fi

check_firmware_before_startup $app_value

# 根据app属性值启动不同的应用程序
startup_app() {
    echo "audio-mixer startup from $app_dir"

    # Copy .so files
    rm -f /tmp/libmixer*.so

    if [ "$app_value" == "audio_mixer_line_mics" ]; then
        cp $app_dir/libmixer_5p3ms_beam_linemics.so /tmp
        # cp $app_dir/libmixer_10p6ms_linemics.so /tmp
        cp $app_dir/libmixer_10p6ms.so /tmp
    elif [ "$app_value" == "audio_mixer_yi_ou_8mic" ]; then
        cp $app_dir/libmixer_5p3ms_beam_yiou.so /tmp
        cp $app_dir/libmixer_10p6ms.so /tmp
    else
        cp $app_dir/libmixer_5p3ms_beam.so /tmp
        cp $app_dir/libmixer_10p6ms.so /tmp
    fi

    cp $app_dir/libatsha204a.so /tmp
#    cp $app_dir/*.so /tmp

    # Handle net_discovery copy for certain app values
    if [[ "$app_value" == "audio_mixer_4mic" ||
        "$app_value" == "audio_mixer_yao_long_8mic" ||
        "$app_value" == "audio_mixer_yao_long_6mic" ||
        "$app_value" == "audio_mixer_6mic" ||
        "$app_value" == "audio_mixer_2mic" ||
        "$app_value" == "audio_mixer_line_mics" ||
        "$app_value" == "audio_mixer_jing_ye_da" ]]; then
        cp "$app_dir/net_discovery" /tmp/net_discovery
    fi

    # Start net_discovery if the file exists
    if [[ -f "/tmp/net_discovery" ]]; then
        /tmp/net_discovery 1>/tmp/discovery.log &
    fi

    # Copy the application binary
    cp "$app_dir/$app_value" /tmp/audio_mixer_app

    # Check if the copy was successful and run the app
    if [[ $? -eq 0 ]]; then
        nice -n -20 /tmp/audio_mixer_app -c audio_mixer -z /etc/zlog.conf &
    else
        echo "Can't find $app_value"
        #exit 1
    fi
}

startup_app
sleep 5

app_pid=$(pidof audio_mixer_app)
app_pid=$((app_pid))
if [ $app_pid -eq 0 ]; then
    if [ "$app_dir" == "/app" ]; then
        app_dir=/usr/bin/audio-mixer-bak
        startup_app

        sleep 3
        app_pid=$(pidof audio_mixer_app)
        app_pid=$((app_pid))
        if [ $app_pid -eq 0 ]; then
            app_value=wrong_board_id # run wrong_board_id program
            if [ -f /app/$app_value ]
            then
                app_dir=/app
            elif [ -f /usr/bin/audio-mixer-bak/$app_value ]
            then
                app_dir=/usr/bin/audio-mixer-bak
            else
                exit 2
            fi
            startup_app
        fi
    fi
fi

exit 0
