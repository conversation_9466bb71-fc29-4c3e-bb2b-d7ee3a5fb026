'''
Author: Anzhichao
Date: 2023-06-30 15:22:27
LastEditTime: 2024-01-19 18:44:50
LastEditors: Anzhichao
Description: 
FilePath: /audio_mixer_rk3308/scripts/warrap_func.py
Copyright (c) 2006-2024, Yinkman Development Team
'''
import re
import sys

# 获取输入文件名
input_file = sys.argv[1]

# 获取输出文件名
output_file_h = sys.argv[2]

output_file_c = sys.argv[3]

beam_or_mixer = sys.argv[4]

beam_file_head_h = '''\
/*
  ########################################################################
  # Don't edit this file!File automatically-generated by warrap_func.py.#
  ########################################################################
*/
#ifndef __BEAM_WARRAP_H_
#define __BEAM_WARRAP_H_

#include <stdint.h>

enum SSL_DIR_PRIORITY_TYPE_s;

typedef enum SSL_DIR_PRIORITY_TYPE_s SSL_DIR_PRIORITY_TYPE;

enum BEAM_MODE_TYPE_s;

typedef enum BEAM_MODE_TYPE_s BEAM_MODE_TYPE;

enum BEAM_TRACK_TYPE_s;

typedef enum BEAM_TRACK_TYPE_s BEAM_TRACK_TYPE;

'''

beam_file_tail_h = '''\

#endif
'''

mixer_file_head_h = '''\
/*
  ########################################################################
  # Don't edit this file!File automatically-generated by warrap_func.py.#
  ########################################################################
*/
#ifndef __MIXER_WARRAP_H_
#define __MIXER_WARRAP_H_

#include <stdint.h>

'''

mixer_file_tail_h = '''\

#endif
'''

beam_file_head_c = '''\
/*
  ########################################################################
  # Don't edit this file!File automatically-generated by warrap_func.py.#
  ########################################################################
*/
#include <pthread.h>
#include "ym_startup.h"
#include "beam.h"
#include "beam_warrap.h"
#include "config.h"

#ifdef BF_ENABLE

static pthread_mutex_t ipc_list_mutex;

static int beam_warrap_sem_init(void)
{
	pthread_mutex_init(&ipc_list_mutex, NULL);
	return 0;
}
INIT_SEM_EXPORT(beam_warrap_sem_init);

'''

beam_file_tail_c = '''\
#endif
'''

mixer_file_head_c = '''\
/*
  ########################################################################
  # Don't edit this file!File automatically-generated by warrap_func.py.#
  ########################################################################
*/
#include <pthread.h>
#include "ym_startup.h"
#include "ym_mixer.h"
#include "mixer_warrap.h"

static pthread_mutex_t ipc_list_mutex;

static int mixer_warrap_sem_init(void)
{
	pthread_mutex_init(&ipc_list_mutex, NULL);
	return 0;
}
INIT_SEM_EXPORT(mixer_warrap_sem_init);

'''

mixer_file_tail_c = '''\
'''

def write_cfile(function_name,returnvalue,params):
    content = returnvalue + " " +  f'{function_name}_warrap' + "(" + params + ")\n"
    content += "{\n"
    if returnvalue != "void":
        content += "\t" + returnvalue + " ret;\n"
    content += "\t" + "pthread_mutex_lock(&ipc_list_mutex);\n"
    param = [param.split()[-1] for param in params.split(',')]
    paramstr = ""
    for pos in param:
        if '[]' in pos:
            pos = pos.split('[')[0]
        paramstr += pos + ", "
    paramstr = paramstr.strip()
    paramstr = re.sub(r',$',"",paramstr)
    paramstr = re.sub(r'\*',"",paramstr)
    if returnvalue != "void":
        content += "\tret = " + function_name + "(" + paramstr + ");\n"
    else:
        content += "\t" + function_name + "(" + paramstr + ");\n"
    content += "\t" + "pthread_mutex_unlock(&ipc_list_mutex);\n"
    if returnvalue != "void":
        content += "\treturn ret;\n}\n\n"
    else:
        content += "\treturn;\n}\n\n"
    return content

def process_header_file(input_file, output_file_h, output_file_c, beam_or_mixer):
    # 打开输入和输出文件
    with open(input_file, 'r') as f_in, open(output_file_h, 'w') as f_out_h, open(output_file_c, 'w') as f_out_c:
        # 遍历输入文件的每一行
        if(beam_or_mixer == "beam"):
            f_out_h.write(beam_file_head_h)
            f_out_c.write(beam_file_head_c)
        else:
            f_out_h.write(mixer_file_head_h)
            f_out_c.write(mixer_file_head_c)
        for line in f_in:
            line = line.strip()
            # 提取函数声明部分
            if '(' in line and ')' in line:
                # 提取函数名和参数
                pattern = r'(.*)\b(\w+)\('
                match = re.match(pattern, line)
                if match:
                    returnvalue = match.group(1).strip()
                    function_name = match.group(2)
                params = line.split('(')[1].replace(");","")
                f_out_h.write(returnvalue + " " +  f'{function_name}_warrap' + "(" + params + ");\n")
                f_out_c.write(write_cfile(function_name,returnvalue,params))
        if(beam_or_mixer == "beam"):
            f_out_h.write(beam_file_tail_h)
            f_out_c.write(beam_file_tail_c)
        else:
            f_out_h.write(mixer_file_tail_h)
            f_out_c.write(mixer_file_tail_c)



# 处理头文件
process_header_file(input_file, output_file_h, output_file_c,beam_or_mixer)
