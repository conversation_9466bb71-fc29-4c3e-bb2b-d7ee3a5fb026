#!/bin/bash
###
 # @Author: Anzhicha<PERSON>
 # @Date: 2024-04-25 16:24:04
 # @LastEditTime: 2024-11-25 16:25:54
 # @LastEditors: Anzhichao
 # @Description: 
 # @FilePath: /audio_mixer_rk3308/scripts/usb-autocopy.sh
 # Copyright (c) 2006-2024, Yinkman Development Team
### 

# 设定挂载点
MOUNT_POINT="/tmp/udisk"

# 检查挂载点是否存在，如果不存在则创建
if [ ! -d "$MOUNT_POINT" ]; then
    mkdir -p "$MOUNT_POINT"
fi

# 从环境变量获取设备名
DEVNAME=$DEVNAME

# 检查设备是否有分区
PARTITIONS=$(ls ${DEVNAME}* | grep -E "${DEVNAME}[0-9]+")

if [ -n "$PARTITIONS" ]; then
    # 如果有分区，使用第一个分区
    DEVNAME=$(echo $PARTITIONS | cut -d' ' -f1)
fi

# 挂载设备
mount -o rw $DEVNAME $MOUNT_POINT

# 匹配文件名称updata-*.img，*为数字，并将最小的数字的文件改为update.img
UPDATE_IMG=$(ls $MOUNT_POINT | grep -E "update-[0-9]+.img" | sort -n | head -n 1)
if [ -n "$UPDATE_IMG" ]; then
    mv $MOUNT_POINT/$UPDATE_IMG $MOUNT_POINT/update.img
fi

# 检测/app目录是否存在
if [ ! -d "/app" ]; then
    sleep 30
fi

# 检查是否存在update.img并拷贝
if [ -f "$MOUNT_POINT/update.img" ]; then
    /app/cmp_version $MOUNT_POINT/update.img
    if [ $? -eq 1 ]; then
        echo "version don't match"
        cp "$MOUNT_POINT/update.img" /data/update.img
        sync
        sync
        updateEngine --image_url=/userdata/update.img --misc=update --savepath=/userdata/update.img --partition=0x3FFCC0 --reboot &
    else
        echo "version match"
    fi

fi

# 卸载设备
# umount $MOUNT_POINT

