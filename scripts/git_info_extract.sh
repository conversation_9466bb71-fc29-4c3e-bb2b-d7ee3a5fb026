#!/bin/bash

# 定义输出文件
OUTPUT_FILE="./scripts/git_info_summary.txt"

# 获取输出文件的绝对路径
OUTPUT_FILE=$(realpath "$OUTPUT_FILE")

# 清空输出文件
> "$OUTPUT_FILE"

# 定义需要查找的文件夹路径列表
REPO_PATHS=(
    "../audio_mixer_rk3576"
    "../buildroot"
    "../kernel"
    "../u-boot"
    # 可按需添加更多路径
)

# 遍历所有定义的文件夹路径
for repo_path in "${REPO_PATHS[@]}"; do
    # 输出调试信息
    # 检查路径是否存在
    if [ ! -d "$repo_path" ]; then
        echo "路径 $repo_path 不存在，跳过。" >> "$OUTPUT_FILE"
        continue
    fi

    # 检查当前用户是否有访问权限
    if [ ! -r "$repo_path" ]; then
        echo "没有访问 $repo_path 的权限，跳过。" >> "$OUTPUT_FILE"
        continue
    fi

    # 进入指定文件夹
    cd "$repo_path" || {
        echo "无法进入 $repo_path，跳过。" >> "$OUTPUT_FILE"
        continue
    }

    # 检查当前目录是否为 Git 仓库
    if ! git rev-parse --is-inside-work-tree > /dev/null 2>&1; then
        echo "路径 $repo_path 不是 Git 仓库，跳过。" >> "$OUTPUT_FILE"
        cd - > /dev/null
        continue
    fi

    # 输出当前仓库路径信息
    echo -e "\n\nrepo: $repo_path" >> "$OUTPUT_FILE"

    # 获取当前本地分支名
    GIT_VERSION=$(git --version | awk '{print $3}' | cut -d'.' -f1-2)
    if (( $(echo "$GIT_VERSION >= 2.22" | bc -l) )); then
        LOCAL_BRANCH=$(git branch --show-current)
    else
        LOCAL_BRANCH=$(git rev-parse --abbrev-ref HEAD)
    fi
    echo "local branch: $LOCAL_BRANCH" >> "$OUTPUT_FILE"

    # 获取对应的远程分支名
    REMOTE_BRANCH=$(git rev-parse --abbrev-ref --symbolic-full-name "$LOCAL_BRANCH@{upstream}" 2>/dev/null)
    if [ $? -ne 0 ] || [ -z "$REMOTE_BRANCH" ]; then
        REMOTE_BRANCH="无对应的远程分支"
    fi
    echo "remote branch: $REMOTE_BRANCH" >> "$OUTPUT_FILE"

    # 获取当前节点的 commit ID
    COMMIT_ID=$(git rev-parse HEAD 2>/dev/null)
    if [ $? -ne 0 ]; then
        COMMIT_ID="获取 commit ID 失败"
    fi
    echo "commit ID: $COMMIT_ID" >> "$OUTPUT_FILE"

    # 获取本地改动的文件列表
    MODIFIED_FILES=$(git status --porcelain 2>/dev/null | awk '{print $2}')
    if [ $? -ne 0 ]; then
        echo -e "\n获取本地改动文件列表失败" >> "$OUTPUT_FILE"
    elif [ -n "$MODIFIED_FILES" ]; then
        echo -e "\n本地改动文件列表:" >> "$OUTPUT_FILE"
        echo "$MODIFIED_FILES" >> "$OUTPUT_FILE"
    else
        echo -e "\n无本地改动文件" >> "$OUTPUT_FILE"
    fi

    # 返回原目录
    cd - > /dev/null
done

echo "代码仓库信息写入 $OUTPUT_FILE"
