#!/usr/bin/python3
# -*- coding: utf-8 -*-
#
# Copyright (c) 2025 Yinkman, Inc. All Rights Reserved.
#
# Generate library header and source file. Do not include ym_mixerboth.h directly.
# Just run this script after the library header files are updated.

import re
from typing import Set, List, Dict, Optional

# Precompile regular expressions to improve performance
FUNC_DECL_PATTERN = re.compile(r'^(\w+(?:\s+\w+)*)\s*(\**)\s+(\w+)\s*\(([\s\S]*?)\);$', re.MULTILINE)
ENUM_PATTERN = re.compile(r'typedef\s+enum(?:\s+\w+)?\s*\{[\s\S]*?\}\s*(\w+);')

def replace_pattern(content: str, original_list: List[str], new_list: List[str]):
    """
    A generic regular expression replacement function.
    :param content: Input content.
    :param original_list: List of original strings.
    :param new_list: List of replacement strings.
    :return: Content after replacement.
    """
    for original, new in zip(original_list, new_list):
        pattern = r'\b' + re.escape(original) + r'\b'
        content = re.sub(pattern, new, content)
    return content

def convert_function_to_pointer(content: str, beam: bool) -> str:
    """
    Convert function declarations in the input content to typedef function pointer declarations.

    :param content: The input content containing function declarations.
    :param beam: A boolean flag indicating whether to use an array for function pointers.
    :return: The content with function declarations converted to function pointer declarations.
    """
    array_name = '' if beam else '[LATENCY_MAX]'

    def replace(match):
        """
        Replace a matched function declaration with a typedef function pointer declaration.

        :param match: The match object returned by the regular expression.
        """
        if match:
            return_type = match.group(1)
            pointer_part = match.group(2)
            func_name = match.group(3)
            params = match.group(4)
            full_return_type = f"{return_type}{pointer_part}"
            # Generate a typedef function pointer declaration
            return f"typedef {full_return_type} (*{func_name}_p)({params});\nextern {func_name}_p {func_name}{array_name};\n"
        return match.group(0)

    return FUNC_DECL_PATTERN.sub(replace, content)

def find_typedef_enum_names(content: str) -> str:
    """
    Find the names of enums defined in typedef enum declarations and replace them with new names.

    :param content: The input content containing typedef enum declarations.
    :return: The content with enum names replaced.
    """
    enum_names = [match.group(1) for match in ENUM_PATTERN.finditer(content)]

    new_names = [name.lower() + '_e' for name in enum_names]
    content = replace_pattern(content, enum_names, new_names)

    original_names = [(name + '_s') for name in enum_names]
    new_names = [name.lower() for name in enum_names]
    content = replace_pattern(content, original_names, new_names)

    return content

def process_header_file(content: str, beam: bool) -> str:
    # content = find_typedef_enum_names(content)

    type_names = ['unsigned int', 'int', 'unsigned short', 'short']
    new_names = ['uint32_t', 'int32_t', 'uint16_t', 'int16_t']
    content = replace_pattern(content, type_names, new_names)

    return convert_function_to_pointer(content, beam)

def generate_source_file(content: str) -> List[str]:
    """
    Extract function names from the input content.
    :param content: Input content containing function declarations.
    :return: List of function names.
    """
    return [match.group(3) for match in FUNC_DECL_PATTERN.finditer(content)]

def source_file_special_handle(content: List[str]) -> List[str]:
    """
    Special handling for source file content.
    :param content: List of function names.
    :return: List after special handling.
    """
    special_names = [['ym_matrix_querymem', 'before', '#ifndef ENABLE_MIC_ARRAY_MODULE'],
                     ['ym_matrix_open', 'after', '#else /* ENABLE_MIC_ARRAY_MODULE */'],
                     ['mixer_set_mixer_id', 'before', '#endif /* ENABLE_MIC_ARRAY_MODULE */'],
                     ['ym_matrix_process', 'before', '#ifdef _TMS320C6X'],
                     ['ym_matrix_processShort', 'after', '#else /* _TMS320C6X */'],
                     ['matrix_set_matrix_map_params', 'before', '#endif /* _TMS320C6X */'],
                     ['mixer_read_last_howling_info', 'after', '#ifndef DISABLE_AEC_NLP_MODULE'],
                     ['mixer_check_howling_detection_done', 'before', '#if !defined(AEC_AFC_48K) && !defined(AEC_AFC_24K_HIGH_LATENCY)'],
                     ['mixer_getNoiseLevel_SNR_rt60', 'before', '#endif /* !defined(AEC_AFC_48K) && !defined(AEC_AFC_24K_HIGH_LATENCY) */'],
                     ['mixer_get_input_thdDB', 'before', '#endif /* DISABLE_AEC_NLP_MODULE */'],
                     ['mixerGetAfcRefMSE', 'before', '#ifndef DISABLE_AEC_NLP_MODULE'],
                     ['mixerGetAfcRefMSE', 'before', '#if !defined(AEC_AFC_48K) && !defined(AEC_AFC_24K_HIGH_LATENCY)'],
                     ['mixerGetAECFilterBlkEng', 'before', '#endif /* !defined(AEC_AFC_48K) && !defined(AEC_AFC_24K_HIGH_LATENCY) */'],
                     ['mixer_updateTuningLowShelfFreq', 'before', '#ifndef SPEAKER_CONF_PLATFORM'],
                     ['mixer_getAecMicLowShelfCenterFreq', 'before', '#endif /* SPEAKER_CONF_PLATFORM */'],
                     ['mixer_aecMicLowShelfBiquadsDesign', 'before', '#endif /* DISABLE_AEC_NLP_MODULE */']]

    for name in special_names:
        index = content.index(name[0])
        if name[1] == 'before':
            content.insert(index, name[2])
        elif name[1] == 'after':
            content.insert(index + 1, name[2])

    return content

def main():
    input_header_file_1: str = "./lib_dsp/beam_conference_speaker.h"
    input_header_file_2: str = "./lib_dsp/ym_mixer.h"
    output_header_file: str = "./include/ym_mixer_beam.h"
    output_source_file: str = "./src/ym_mixer_beam.c"

    # Handle input header file 1
    with open(input_header_file_1, 'r', encoding='utf-8') as file:
        input_content_1 = file.read()

    output_header_content = process_header_file(input_content_1, True)
    output_header_content = '\n#ifdef BEAM_ENABLE\n' + output_header_content + '#endif /* BEAM_ENABLE */\n\n'

    # Handle input header file 2
    with open(input_header_file_2, 'r', encoding='utf-8') as file:
        input_content_2 = file.read()

    special_case_names = ['unsigned char *', 'float *mixerGetAECFilterBlkEng']
    changed_names = ['unsigned char * ', 'float * mixerGetAECFilterBlkEng']
    input_content_2 = replace_pattern(input_content_2, special_case_names, changed_names)

    output_header_content += process_header_file(input_content_2, False)

    # Handle output header file
    remove_lines = ['#endif // #ifndef BEAM_H\n', '#ifndef BEAM_H\n', '#define BEAM_H\n', '#endif // #ifndef _YM_MIXER_H_\n', '#ifndef _YM_MIXER_H_\n', '#define _YM_MIXER_H_\n']
    for line in remove_lines:
        output_header_content = output_header_content.replace(line, '')

    with open(output_header_file, 'w', encoding='utf-8') as file:
        file.write('''/*\n * DO NOT EDIT THIS FILE.
 * IT IS GENERATED BY lib_file_generator.py IN THE lib_dsp FOLDER.
 */\n
#ifndef _YM_MIXER_BEAM_H_
#define _YM_MIXER_BEAM_H_\n
#include <stdint.h>
#include "config.h"\n
typedef enum latency
{
    LATENCY_5MS = 0,
    LATENCY_10MS,
    LATENCY_MAX
} latency_e;\n
typedef enum mixer_block_size
{
    MIXER_BLOCK_SIZE_5MS = 256,
    MIXER_BLOCK_SIZE_10MS = 512,
} mixer_block_size_e;\n''')

        file.write(output_header_content)

        file.write('''#ifdef BEAM_ENABLE
int32_t ym_beam_latency_5ms_init(void);
#endif /* BEAM_ENABLE */\n
int32_t ym_mixer_latency_5ms_init(void);
int32_t ym_mixer_latency_10ms_init(void);\n
#endif /* _YM_MIXER_BEAM_H_ */\n''')

    output_source_content_1 = generate_source_file(input_content_1)
    output_source_content_2 = generate_source_file(input_content_2)
    output_source_content_2 = source_file_special_handle(output_source_content_2)

    with open(output_source_file, 'w', encoding='utf-8') as file:
        file.write('''/*\n * DO NOT EDIT THIS FILE.
 * IT IS GENERATED BY lib_file_generator.py IN THE lib_dsp FOLDER.
 */\n
#include <stdio.h>
#include <stdint.h>
#include <ltdl.h>
#include "ym_mixer_beam.h"
#include "ym_startup.h"
#include "ym_log.h"\n
#ifdef BEAM_ENABLE\n''')

        for func_name in output_source_content_1:
            file.write(f"{func_name}_p {func_name} = NULL;\n")

        file.write('#endif /* BEAM_ENABLE */\n\n')

        for func_name in output_source_content_2:
            if '#' in func_name:
                file.write(f"{func_name}\n")
            else:
                file.write(f"{func_name}_p {func_name}[LATENCY_MAX] = {{NULL, NULL}};\n")

        file.write('''\nstatic lt_dlhandle latency_5ms_handle = NULL;
static lt_dlhandle latency_10ms_handle = NULL;\n
#ifdef BEAM_ENABLE
int32_t ym_beam_latency_5ms_init(void)
{
    /* load so */
    if (latency_5ms_handle == NULL)
        latency_5ms_handle = lt_dlopen("libmixer_5p3ms_beam.so");
    if (latency_5ms_handle == NULL)
    {
        log_e("Failed to load 5ms library: %s.", lt_dlerror());
        return -2;
    }\n
    /* capture symbol */\n''')

        for func_name in output_source_content_1:
            file.write(f'''    {func_name} = lt_dlsym(latency_5ms_handle, "{func_name}");
    if ({func_name} == NULL)
    {{
        log_e("Failed to load {func_name} function: %s.", lt_dlerror());
        return -3;
    }}\n\n''')

        file.write('''    log_i("Load beam 5ms library successfully.");\n
    return 0;
}\n
#endif /* BEAM_ENABLE */\n
int32_t ym_mixer_latency_5ms_init(void)
{
    /* Open library */
    if (latency_5ms_handle == NULL)
        latency_5ms_handle = lt_dlopen("libmixer_5p3ms_beam.so");
    if (latency_5ms_handle == NULL)
    {
        log_e("Failed to load 5ms library: %s.", lt_dlerror());
        return -2;
    }\n
    /* Capture symbol */\n''')

        for func_name in output_source_content_2:
            if '#' in func_name:
                file.write(f'{func_name}\n')
            else:
                file.write(f'''    {func_name}[LATENCY_5MS] = lt_dlsym(latency_5ms_handle, "{func_name}");
    if ({func_name}[LATENCY_5MS] == NULL)
    {{
        log_e("Failed to load 5ms {func_name} function: %s.", lt_dlerror());
        return -3;
    }}\n\n''')

        file.write('''    log_i("Load mixer 5ms library successfully.");\n
    return 0;
}\n
int32_t ym_mixer_latency_10ms_init(void)
{
    /* Open library */
    if (latency_10ms_handle == NULL)
        latency_10ms_handle = lt_dlopen("libmixer_10p6ms.so");
    if (latency_10ms_handle == NULL)
    {
        log_e("Failed to load 10ms library: %s.", lt_dlerror());
        return -2;
    }\n
    /* Capture symbol */\n''')

        for func_name in output_source_content_2:
            if '#' in func_name:
                file.write(f'{func_name}\n')
            else:
                file.write(f'''    {func_name}[LATENCY_10MS] = lt_dlsym(latency_10ms_handle, "ym10ms_{func_name}");
    if ({func_name}[LATENCY_10MS] == NULL)
    {{
        log_e("Failed to load 10ms {func_name} function: %s.", lt_dlerror());
        return -3;
    }}\n\n''')

        file.write('''    log_i("Load mixer 10ms library successfully.");\n
    return 0;
}\n
static int32_t ym_lib_load_init(void)
{
    /* Init so loader */
    if (lt_dlinit() != 0)
    {
        log_e("Failed to initialize libltdl: %s.", lt_dlerror());
        return -1;
    }
    log_i("libltdl initialized successfully.");
    return 0;
}
INIT_LOW_LEVEL_EXPORT(ym_lib_load_init);\n
static int32_t ym_lib_load_deinit(void)
{
    if (latency_5ms_handle != NULL)
    {
        lt_dlclose(latency_5ms_handle);
        latency_5ms_handle = NULL;
    }\n
    if (latency_10ms_handle != NULL)
    {
        lt_dlclose(latency_10ms_handle);
        latency_10ms_handle = NULL;
    }\n
    /* Deinit so loader */
    lt_dlexit();
    return 0;
}
EXIT_EXPORT(ym_lib_load_deinit);\n''')

if __name__ == "__main__":
    main()
