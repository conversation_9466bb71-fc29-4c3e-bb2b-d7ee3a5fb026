#!/bin/bash

# 检测是否在SDK根目录
check_sdk_root() {
    local required_dirs=("audio_mixer_rk3576" "buildroot" "kernel" "device" "u-boot")
    local missing_dirs=()

    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            missing_dirs+=("$dir")
        fi
    done

    if [ ${#missing_dirs[@]} -ne 0 ]; then
        echo "错误：当前目录不是SDK根目录！"
        echo "缺少以下必需的文件夹："
        for dir in "${missing_dirs[@]}"; do
            echo "  - $dir"
        done
        echo "请在SDK根目录下执行此脚本。"
        exit 1
    fi
}

# 执行SDK根目录检测
check_sdk_root

# 调用当前目录的预构建脚本
cd audio_mixer_rk3576 && ./prevbuild.sh $1

# 切换到上一级目录并删除之前的编译结果
cd ..
rm -rf buildroot/output/rockchip_rk3576/build/ym-appd-1.0
rm -rf buildroot/output/rockchip_rk3576/build/ym-audio-mixer-1.0

# 调用主构建脚本
./build.sh

# 生成年月日时格式的时间戳（如2024102315）
timestamp=$(date +%Y%m%d%H)
target_dir="./output/firmware/${timestamp}"

# 创建目标文件夹（自动创建上级目录）
mkdir -p "$target_dir"

# 复制git信息文件
cp ./audio_mixer_rk3576/scripts/git_info_summary.txt "$target_dir/"

cp ./audio_mixer_rk3576/ymupdate.tar.gz "$target_dir/"

# 复制img文件
cp ./output/update/Image/update.img "$target_dir/"

