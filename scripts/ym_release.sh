#!/bin/bash

# 定义颜色代码
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 颜色输出函数
echo_error() {
    echo -e "${RED}错误：$1${NC}"
}

echo_warning() {
    echo -e "${YELLOW}警告：$1${NC}"
}

echo_success() {
    echo -e "${GREEN}$1${NC}"
}

echo_info() {
    echo -e "${CYAN}$1${NC}"
}

# 检测是否在SDK根目录
check_sdk_root() {
    local required_dirs=("audio_mixer_rk3576" "buildroot" "kernel" "device" "u-boot")
    local missing_dirs=()

    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            missing_dirs+=("$dir")
        fi
    done

    if [ ${#missing_dirs[@]} -ne 0 ]; then
        echo_error "当前目录不是SDK根目录！"
        echo "缺少以下必需的文件夹："
        for dir in "${missing_dirs[@]}"; do
            echo -e "  ${RED}- $dir${NC}"
        done
        echo_error "请在SDK根目录下执行此脚本。"
        exit 1
    fi
}

# 执行SDK根目录检测
check_sdk_root

# 调用当前目录的预构建脚本并捕获产品型号
# 使用临时文件来分别处理stdout和stderr
temp_stdout=$(mktemp)
temp_stderr=$(mktemp)

# 执行prevbuild.sh，将stdout重定向到临时文件，stderr显示给用户
(cd audio_mixer_rk3576 && ./prevbuild.sh $1) > "$temp_stdout" 2> >(tee "$temp_stderr" >&2)

# 读取stdout内容
prevbuild_output=$(cat "$temp_stdout")

# 从输出中提取产品名称
selected_product=$(echo "$prevbuild_output" | grep "SELECTED_PRODUCT:" | cut -d: -f2)
if [ -z "$selected_product" ]; then
    echo_error "无法获取选择的产品名称！"
    echo_error "prevbuild.sh 执行可能出现问题，请检查。"
    exit 1
fi
echo_info "选择的产品型号: $selected_product"

# 清理临时文件
rm -f "$temp_stdout" "$temp_stderr"

# 删除之前的编译结果
rm -rf buildroot/output/rockchip_rk3576/build/ym-appd-1.0
rm -rf buildroot/output/rockchip_rk3576/build/ym-audio-mixer-1.0

# 调用主构建脚本
# 加一行空白行
echo
echo "开始构建更新镜像..."
echo
./build.sh

# 检查build.sh执行结果
if [ $? -ne 0 ]; then
    echo_error "build.sh 执行失败！"
    echo_error "构建过程出现错误，请检查构建日志。"
    exit 1
fi
echo_success "构建完成成功。"

# 解析版本信息
parse_version_info() {
    local version_file="./audio_mixer_rk3576/include/version.h"

    if [ ! -f "$version_file" ]; then
        echo_error "找不到版本文件 $version_file"
        echo_error "请确保在正确的SDK根目录下执行脚本。"
        exit 1
    fi

    # 提取版本号定义
    local ym_version=$(grep "#define YM_VERSION" "$version_file" | sed 's/.*(\([0-9]*\)).*/\1/')
    local ym_subversion=$(grep "#define YM_SUBVERSION" "$version_file" | sed 's/.*(\([0-9]*\)).*/\1/')
    local ym_revision=$(grep "#define YM_REVISION" "$version_file" | sed 's/.*(\([0-9]*\)).*/\1/')
    local ym_hardware=$(grep "#define YM_RK3576_HARDWARE_VERSION" "$version_file" | sed 's/.*(\(0x[0-9A-Fa-f]*\)).*/\1/')
    local ym_product=$(grep "#define YM_PRODUCT_MODEL" "$version_file" | sed 's/.*"\(.*\)".*/\1/')

    # 检查是否成功提取到版本信息
    if [ -z "$ym_version" ] || [ -z "$ym_subversion" ] || [ -z "$ym_revision" ] || [ -z "$ym_hardware" ]; then
        echo_error "无法从版本文件中解析版本信息！"
        echo_error "版本文件格式可能有问题，请检查 $version_file"
        exit 1
    fi

    # 构建固件版本：V$(YM_VERSION)$(YM_SUBVERSION)$YM_REVISION
    firmware_version="V${ym_version}${ym_subversion}${ym_revision}"

    # 硬件版本：$(YM_RK3576_HARDWARE_VERSION)
    hardware_version="$ym_hardware"

    # 产品型号
    product_model="$ym_product"

    echo_info "产品型号: $product_model"
    echo_info "硬件版本: $hardware_version"
    echo_info "固件版本: $firmware_version"
}

# 解析版本信息
parse_version_info

# 生成年月日时格式的时间戳（如2024102315）
timestamp=$(date +%Y%m%d%H)
target_dir="./output/ym_release/rel_${firmware_version}_${timestamp}_${hardware_version}_${product_model}"
echo_info "目标目录: $target_dir"

# 创建目标文件夹（自动创建上级目录）
mkdir -p "$target_dir"
if [ $? -ne 0 ]; then
    echo_error "无法创建目标目录 $target_dir"
    exit 1
fi

# 复制git信息文件
echo_info "复制git信息文件..."
if [ -f "./audio_mixer_rk3576/scripts/git_info_summary.txt" ]; then
    cp ./audio_mixer_rk3576/scripts/git_info_summary.txt "$target_dir/"
    if [ $? -ne 0 ]; then
        echo_error "复制git信息文件失败"
        exit 1
    fi
else
    echo_error "git信息文件不存在!"
    exit 1
fi

# 复制ymupdate.tar.gz文件
echo_info "复制ymupdate.tar.gz文件..."
if [ -f "./audio_mixer_rk3576/ymupdate.tar.gz" ]; then
    cp ./audio_mixer_rk3576/ymupdate.tar.gz "$target_dir/ymupdate_${product_model}_${firmware_version}_${timestamp}.tar.gz"
    if [ $? -ne 0 ]; then
        echo_error "复制ymupdate.tar.gz文件失败"
        exit 1
    fi
else
    echo_error "ymupdate.tar.gz文件不存在!"
    exit 1
fi

# 复制img文件
echo_info "复制update.img文件..."
if [ -f "./output/update/Image/update.img" ]; then
    cp ./output/update/Image/update.img "$target_dir/update_${product_model}_${firmware_version}_${timestamp}.img"
    if [ $? -ne 0 ]; then
        echo_error "复制update.img文件失败"
        exit 1
    fi
else
    echo_error "找不到update.img文件，构建失败"
    exit 1
fi

echo_success "所有文件复制完成！"
echo_success "发布包已生成到: $target_dir"

