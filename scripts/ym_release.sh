#!/bin/bash

# 检测是否在SDK根目录
check_sdk_root() {
    local required_dirs=("audio_mixer_rk3576" "buildroot" "kernel" "device" "u-boot")
    local missing_dirs=()

    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            missing_dirs+=("$dir")
        fi
    done

    if [ ${#missing_dirs[@]} -ne 0 ]; then
        echo "错误：当前目录不是SDK根目录！"
        echo "缺少以下必需的文件夹："
        for dir in "${missing_dirs[@]}"; do
            echo "  - $dir"
        done
        echo "请在SDK根目录下执行此脚本。"
        exit 1
    fi
}

# 执行SDK根目录检测
check_sdk_root

# 调用当前目录的预构建脚本并捕获产品名称
# 使用临时文件来分别处理stdout和stderr
temp_stdout=$(mktemp)
temp_stderr=$(mktemp)

# 执行prevbuild.sh，将stdout重定向到临时文件，stderr显示给用户
(cd audio_mixer_rk3576 && ./prevbuild.sh $1) > "$temp_stdout" 2> >(tee "$temp_stderr" >&2)

# 读取stdout内容
prevbuild_output=$(cat "$temp_stdout")

# 从输出中提取产品名称
selected_product=$(echo "$prevbuild_output" | grep "SELECTED_PRODUCT:" | cut -d: -f2)
if [ -z "$selected_product" ]; then
    echo "警告：无法获取选择的产品名称，使用默认值"
    selected_product="unknown"
fi
echo "选择的产品: $selected_product"

# 清理临时文件
rm -f "$temp_stdout" "$temp_stderr"

# 删除之前的编译结果
rm -rf buildroot/output/rockchip_rk3576/build/ym-appd-1.0
rm -rf buildroot/output/rockchip_rk3576/build/ym-audio-mixer-1.0

# 调用主构建脚本
# 加一行空白行
echo
echo "Start build update image ..."
# ./build.sh

# 生成年月日时格式的时间戳（如2024102315）
timestamp=$(date +%Y%m%d%H)
target_dir="./output/ym_release/rel_${selected_product}_${timestamp}"
echo "target_dir: $target_dir"

# 创建目标文件夹（自动创建上级目录）
mkdir -p "$target_dir"

# 复制git信息文件
cp ./audio_mixer_rk3576/scripts/git_info_summary.txt "$target_dir/"

cp ./audio_mixer_rk3576/ymupdate.tar.gz "$target_dir/"

# 复制img文件
cp ./output/update/Image/update.img "$target_dir/"

